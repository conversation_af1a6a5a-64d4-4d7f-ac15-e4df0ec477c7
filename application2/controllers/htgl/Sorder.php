<?php
defined('BASEPATH') or exit('No direct script access allowed');
require_once "ini.php";

class Sorder extends PY_Controller
{

    public $modeName = '订单';

    public $c1_arr, $c2_arr, $c2_1_arr, $c3_arr, $column_arr;

    public $c1_json;//json格式

    public function __construct()
    {
        parent::__construct();
        $this->assign('modeName', $this->modeName);

        $this->assignQX(array("add", "update", "delete", "add2", "update2", "delete2", "dprint", "update3", "mprint"));
        $c1findIn = array();
        if($_SESSION['user']['role_id'] == 4){
            
             $res = $this->db
                ->select("column")
                ->from("user")
                ->where("uid", $_SESSION['user']['uid'])
                ->get();
            $user = $res->result_array()[0];
            if($user['column']){
                $this->column_arr = explode(',',$user['column']);
            }else{
                $this->column_arr = [];
            }
            
            $c1findIn['key'] = 'nid';
            if($this->column_arr){
               $c1findIn['value'] = $this->column_arr;
            }
        }
        
        
        $this->c1_arr = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => 0), " oid ",$c1findIn);
        $this->assign('c1_arr', $this->c1_arr);

        $c1_json = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => 0), " oid ",$c1findIn);
        $this->c1_json = json_encode($c1_json);
        $this->assign('c1_json', $this->c1_json);

        $this->c2_arr = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => 1), " oid ");
        $this->assign('c2_arr', $this->c2_arr);

        $this->c2_1_arr = $this->getKeyVal("column", "`nid` as k,`description` as v", array("type" => "column", "tid" => 1), " oid ");
        $this->assign('c2_1_arr', $this->c2_1_arr);

        $res = $this->db
            ->select("oid,nid,title")
            ->from("column")
            ->where(array("type" => "column", "tid" => 1))
            ->order_by("oid,nid")
            ->get();

        $lst = $res->result_array();
        $rt = array();

        foreach ($lst as $uk => $uv) {
            if (empty($rt[$uv['oid']])) {
                $rt[$uv['oid']] = array();
            }
            $rt[$uv['oid']][$uv['nid']] = $uv['title'];
        }

        $this->assign('c2_json', json_encode($rt));

        $this->c3_arr = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => 2), " oid ");
        $this->assign('c3_arr', $this->c3_arr);

        //var_dump($this->config_arr);
    }

    public function mprint()
    {
        $nid = intval($this->input->get('nid'));
        if ($nid > 0) {
            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }
            $odata = $this->ckeckbyodata("sorder", $where);

            $wherepr = "";
            $sql = "SELECT *
            FROM pendy_sorder_products
            WHERE cid={$nid}  {$wherepr}
            order by  nid
            ";
            //echo $sql;
            $res = $this->db->query($sql);
            $odata['lst'] = $res->result_array();

            $this->assign('data', $odata);

            $data2 = array('a2' => $odata['a2'] + 1,'pdater'=>$_SESSION['user']['username']);
            $this->db
                ->where(array('nid' => $nid))
                ->update('sorder', $data2);

            $aconfig = $this->getKeyVal("config", "`key` as k,`value` as v", array(), " oid ");
            $this->assign('aconfig', $aconfig);

            $this->display('admin/' . MODULE . '/mprint.tpl');
        }

        die();
    }

    private function getlst($where, $wherepr)
    {
        $page = array(
            'count' => 0, // 总记录数，用于分页
            'all' => 0, // 总页数
            'current' => 1, // 当前页
            'size' => 100, // 每页显示条数
            'uri_string' => INDEX . $this->uri->uri_string(),
        );
        $c = $this->input->get('page');
        $page['current'] = (isset($c) && !empty($c) && (intval($c) > 0)) ? intval($c) : 1;

        $page['count'] = 0;
        $sql = "SELECT COUNT(DISTINCT s.nid) AS c
        FROM pendy_sorder s
        LEFT JOIN pendy_sorder_products p ON s.nid=p.cid
        WHERE 1=1 {$where}
        ORDER BY s.oid DESC,s.nid DESC";
        $res = $this->db->query($sql);
        if (!empty($res->result_array())) {
            $page['count'] = $res->result_array()[0]['c'];
        }

        $page['all'] = ceil($page['count'] / $page['size']);
        $this->assign('page', $page);

        $sql = "SELECT DISTINCT s.*
        FROM pendy_sorder s
        LEFT JOIN pendy_sorder_products p ON s.nid=p.cid
        WHERE 1=1 {$where}
        ORDER BY s.oid desc,s.nid  desc
        LIMIT " . ($page['current'] - 1) * $page['size'] . "," . $page['size'];

        //echo ($sql);
        //die();
        $res = $this->db->query($sql);
        $alst = $res->result_array();

        $lst = array();
        foreach ($alst as $k => $v) {
            $sql = "SELECT *
            FROM pendy_sorder_products
            WHERE cid={$v['nid']}  {$wherepr}
            order by  nid
            ";
            
            // if($wherepr){
            //     var_dump($sql);
            //     exit;
            // }
            
            $res = $this->db->query($sql);
            

            $blst = $res->result_array();

            // if($blst == []){
            //     continue;
            // }
           
            $plst = array();
            $numSum = 0;
            foreach ($blst as $kk => $vv) {
                $vv['pm'] = $this->getPM($vv['nid'], $vv['cid']);
                $numSum  = $numSum + $vv['num'];
                $plst[] = $vv;
            }

            $v['plst'] = $plst;
            $v['numSum'] = $numSum;
            $lst[] = $v;
     
        }
        return $lst;
        
    }

    public function rlist()
    {
        //搜索条件
        $where = " AND s.`type` = '" . MODULE . "' ";
        $s = array(
            'stitle' => $this->input->get('stitle'),
            'sstatus' => $this->input->get('sstatus'),
            'scplx' => $this->input->get('scplx'),
            'sdddz' => $this->input->get('sdddz'),
            'sstamp' => $this->input->get('sstamp'),

            'stjsjs' => $this->input->get('stjsjs'),
            'stjsje' => $this->input->get('stjsje'),

            'sscsjs' => $this->input->get('sscsjs'),
            'sscsje' => $this->input->get('sscsje'),
            'scpkg' => $this->input->get('scpkg'),
        );


        $s['stitle'] = isset($s['stitle']) ? replaysql(trim($s['stitle'])) : '';
        $s['sstatus'] = isset($s['sstatus']) ? intval($s['sstatus']) : '-99';

        $s['scplx'] = isset($s['scplx']) ? intval($s['scplx']) : '-99';
        $s['sdddz'] = isset($s['sdddz']) ? intval($s['sdddz']) : '-99';
        $s['sstamp'] = isset($s['sstamp']) ? intval($s['sstamp']) : '-99';
        $s['scpkg'] = isset($s['scpkg']) ? intval($s['scpkg']) : '-99';
        
        // if($s['scpkg'] != '-99'){
        //     var_dump($s['sstamp']);
        // var_dump($s['scpkg']);
        // die;
        // }
        

        $s['pid'] = $this->usrpid;

        if (!empty($s['stitle'])) {
            $where .= " AND s.`title` LIKE '%{$s['stitle']}%' ";
        }

        if ($s['sstatus'] > -98) {
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
            
        }

        if ($s['sdddz'] > -98) {
            $where .= " AND s.`s1` = '{$s['sdddz']}' ";
        }

        #未绑邮票
        if ($s['sstamp'] == 1) {
            $where .= " AND s.`stamp` IS NULL ";
        }

        #已绑邮票
        if ($s['sstamp'] == 2) {
            $where .= " AND s.`stamp` != '' ";
        }


        $wherepr = "";

        if ($s['sstatus'] > -98) {
            
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
            if($s['scplx'] == 106){
                
                if($s['sstatus'] == 2){
                    $wherepr .= " AND `status` ='" . $s['sstatus'] . "' ";
                }
            }
            
        }

        if ($s['scplx'] > -98) {
            // $wherepr .= " AND cplx ='" . $s['scplx'] . "' ";
            $where .= " AND p.`cplx` = '{$s['scplx']}' ";
        }

        if ($s['scpkg'] > -98) {
            $wherepr .= " AND cpkg ='" . $s['scpkg'] . "' ";
            $where .= " AND p.`cpkg` = '{$s['scpkg']}' ";
        }

        if ($s['stjsjs']) {
            $stjsjs = intval(strtotime($s['stjsjs'] . " 0:0:0"));
            if ($stjsjs > 0) {
                $where .= " AND s.`a1` >= {$stjsjs} ";
            }
        }

        if ($s['stjsje']) {
            $stjsje = intval(strtotime($s['stjsje'] . " 23:59:59"));
            if ($stjsje > 0) {
                $where .= " AND s.`a1` <= {$stjsje} ";
            }
        }

        if ($s['sscsjs']) {
            $sscsjs = intval(strtotime($s['sscsjs'] . " 0:0:0"));
            if ($sscsjs > 0) {
                $where .= " AND s.`ptime` >= {$sscsjs} ";
            }
        }

        if ($s['sscsje']) {
            $sscsje = intval(strtotime($s['sscsje'] . " 23:59:59"));
            if ($sscsje > 0) {
                $where .= " AND s.`ptime` <= {$sscsje} ";
            }
        }
        
        // echo $s['sscsjs'];
        $where .= " AND s.`status` > 1 ";
        //搜索条件结束
        
        if($_SESSION['user']['role_id'] == 4){
            
            if($this->column_arr){
                $column_str = '';
                foreach ($this->column_arr as $k=>$v){
                    if($k == 0){
                        $column_str = $v;
                    }else{
                        $column_str .= ','. $v;
                    }
                }
                $where .= " AND p.`cplx` in ({$column_str}) ";
            }else{
                //未分配任何产品
                $where .= " AND s.`status` > 99999 ";
            }
        }
        
        $this->assign('s', $s);
        $lst = $this->getlst($where, $wherepr);
        $this->assign('lst', $lst);

        // 单独提取所有的nid
        $lstNidArr = [];
        foreach ($lst as $key => $value) {
            $lstNidArr[] = $value['nid'];
        }
        
        $lstNidArr = implode(',', $lstNidArr);
       
        $this->assign('lstNidArr', $lstNidArr);
        
        $this->display('admin/' . MODULE . '/rlist.tpl');
    }
    
    public function export()
    {
        
        //搜索条件
        $where = " AND s.`type` = '" . MODULE . "' ";
        $s = array(
            'stitle' => $this->input->get('stitle'),
            'sstatus' => $this->input->get('sstatus'),
            'scplx' => $this->input->get('scplx'),
            'sdddz' => $this->input->get('sdddz'),
            'sstamp' => $this->input->get('sstamp'),

            'stjsjs' => $this->input->get('stjsjs'),
            'stjsje' => $this->input->get('stjsje'),

            'sscsjs' => $this->input->get('sscsjs'),
            'sscsje' => $this->input->get('sscsje'),
            'scpkg' => $this->input->get('scpkg'),
        );


        $s['stitle'] = isset($s['stitle']) ? replaysql(trim($s['stitle'])) : '';
        $s['sstatus'] = isset($s['sstatus']) ? intval($s['sstatus']) : '-99';

        $s['scplx'] = isset($s['scplx']) ? intval($s['scplx']) : '-99';
        $s['sdddz'] = isset($s['sdddz']) ? intval($s['sdddz']) : '-99';
        $s['sstamp'] = isset($s['sstamp']) ? intval($s['sstamp']) : '-99';
        $s['scpkg'] = isset($s['scpkg']) ? intval($s['scpkg']) : '-99';
        
        // if($s['scpkg'] != '-99'){
        //     var_dump($s['sstamp']);
        // var_dump($s['scpkg']);
        // die;
        // }
        // if($s['sscsjs']){
        //     if($s['sscsje']){
                
        //         $filename =  date('Ymd',strtotime($s['sscsjs'])).'-'.date('Ymd',strtotime($s['sscsje']));
        //     }else{
        //         $filename =  date('Ymd',strtotime($s['sscsjs'])).'起';
        //     }
        // }else{
        //     if($s['sscsje']){
        //         $filename = '截至'.date('Ymd',strtotime($s['sscsje']));
        //     }else{
        //         $filename =  '时间不限';
        //     }
        // }

        $pstatus = array(0 => '生产回退', 1 => '未提交', 2 => '未生产', 3 => '已生产');
        $s['pid'] = $this->usrpid;

        if (!empty($s['stitle'])) {
            $where .= " AND s.`title` LIKE '%{$s['stitle']}%' ";
        }

        if ($s['sstatus'] > -98) {
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
            $status_name = $pstatus[$s['sstatus']];
        }else{
            $status_name = '状态不限';
        }
        $filename = $status_name; 
        if ($s['sdddz'] > -98) {
            $where .= " AND s.`s1` = '{$s['sdddz']}' ";
            $sdddz_name = $this->c3_arr[$s['sdddz']];
        }else{
            $sdddz_name = '店主不限';
        }
        $filename .= '-'. $sdddz_name; 
        #未绑邮票
        if ($s['sstamp'] == 1) {
            $where .= " AND s.`stamp` IS NULL ";
        }

        #已绑邮票
        if ($s['sstamp'] == 2) {
            $where .= " AND s.`stamp` != '' ";
        }


        $wherepr = "";

        if ($s['sstatus'] > -98) {
            
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
            if($s['scplx'] == 106){
                
                if($s['sstatus'] == 2){
                    $wherepr .= " AND `status` ='" . $s['sstatus'] . "' ";
                }
            }
            
        }

        if ($s['scplx'] > -98) {
            // $wherepr .= " AND cplx ='" . $s['scplx'] . "' ";
            $where .= " AND p.`cplx` = '{$s['scplx']}' ";
            $scplx_name = $this->c1_arr[$s['scplx']];
            
        }else{
            $scplx_name = '产品不限';
        }
        $filename .= '-'. $scplx_name; 
        if ($s['scpkg'] > -98) {
            $wherepr .= " AND cpkg ='" . $s['scpkg'] . "' ";
            $where .= " AND p.`cpkg` = '{$s['scpkg']}' ";
        }

        if ($s['stjsjs']) {
            $stjsjs = intval(strtotime($s['stjsjs'] . " 0:0:0"));
            if ($stjsjs > 0) {
                $where .= " AND s.`a1` >= {$stjsjs} ";
            }
        }

        if ($s['stjsje']) {
            $stjsje = intval(strtotime($s['stjsje'] . " 23:59:59"));
            if ($stjsje > 0) {
                $where .= " AND s.`a1` <= {$stjsje} ";
            }
        }

        if ($s['sscsjs']) {
            $sscsjs = intval(strtotime($s['sscsjs'] . " 0:0:0"));
            if ($sscsjs > 0) {
                $where .= " AND s.`ptime` >= {$sscsjs} ";
            }
        }

        if ($s['sscsje']) {
            $sscsje = intval(strtotime($s['sscsje'] . " 23:59:59"));
            if ($sscsje > 0) {
                $where .= " AND s.`ptime` <= {$sscsje} ";
            }
        }
        
        // echo $s['sscsjs'];
        $where .= " AND s.`status` > 1 ";
        //搜索条件结束
        
        if($_SESSION['user']['role_id'] == 4){
            
            if($this->column_arr){
                $column_str = '';
                foreach ($this->column_arr as $k=>$v){
                    if($k == 0){
                        $column_str = $v;
                    }else{
                        $column_str .= ','. $v;
                    }
                }
                $where .= " AND p.`cplx` in ({$column_str}) ";
            }else{
                //未分配任何产品
                $where .= " AND s.`status` > 99999 ";
            }
        }
        
        
        $sql = "SELECT DISTINCT s.*
        FROM pendy_sorder s
        LEFT JOIN pendy_sorder_products p ON s.nid=p.cid
        WHERE 1=1 {$where}
        ORDER BY s.oid desc,s.nid  desc";

        //echo ($sql);
        //die();
        $res = $this->db->query($sql);
        $alst = $res->result_array();
        
        $lst = array();
        $plst = array();
        $serial = 1;
       
        foreach ($alst as $k => $v) {
            $sql = "SELECT *
            FROM pendy_sorder_products
            WHERE cid={$v['nid']}  {$wherepr}
            order by  nid
            ";
            $res = $this->db->query($sql);
            $blst = $res->result_array();

            // if($blst == []){
            //     continue;
            // }
           
            $numSum = 0;
            $plst = [];
            foreach ($blst as $kk => $vv) {
                $vv['serial'] = $serial;
                // $vv['pm'] = $this->getPM($vv['nid'], $vv['cid']);
                // $numSum  = $numSum + $vv['num'];
                
                //店主
                if($v['s1']){
                    $vv['c3_name'] = !empty($this->c3_arr[$v['s1']]) ? $this->c3_arr[$v['s1']]:'';
                }else{
                    $vv['c3_name'] = '';
                }
                
                //类型
                if($vv['cplx']){
                    $vv['c1_name'] = !empty($this->c1_arr[$vv['cplx']]) ? $this->c1_arr[$vv['cplx']]:'';
                }else{
                    $vv['c1_name'] = '';
                }
                
                //规格
                if($vv['cpkg']){
                    $vv['c2_name'] = !empty($this->c2_arr[$vv['cpkg']]) ? $this->c2_arr[$vv['cpkg']]:'';
                }else{
                    $vv['c2_name'] = '';
                }
                $vv['number_remarks'] = '';
                $vv['logistics_remarks'] = '';
                $vv['theme_remarks'] = '';
                
                $vv['title'] = !empty($v['title']) ? $v['title']:'';
                
                $vv['status_name'] = !empty($pstatus[$vv['status']]) ? $pstatus[$vv['status']]:'未生产';
                $vv['order_id'] = $v['nid'];
                $vv['order_time'] = empty($v['a1'])? '' :date("Y-m-d H:i", $v['a1']);
                $plst[] = $vv;
                $serial++;
            }

            $v['plst'] = $plst;
            $v['numSum'] = $numSum;
            $lst[] = $v;
     
        }
        // return $lst;

        error_reporting(-1);
        ini_set('display_errors', 1);

        // require_once APPPATH . 'third_party/PHPExcel/PHPExcel.php';
        // require_once APPPATH . 'third_party/PHPExcel/PHPExcel/Writer/Excel2007.php';
        // require_once APPPATH . 'third_party/PHPExcel/PHPExcel/IOFactory.php';

        $this -> load -> library('CExcel');
        $this -> load -> library('CExcelIO');
        $filename .= '-订单明细表'; 
        $objPHPExcel = new PHPExcel();
        $objPHPExcel -> getProperties() -> setTitle("export") -> setDescription("none");
 
        $objPHPExcel -> setActiveSheetIndex(0); 
        $sheet = $objPHPExcel->setActiveSheetIndex(0);
        
        $sheet->getStyle('A1:P1')->getAlignment()->setWrapText(true);
        $sheet->getStyle('A1:P1')->getFont()->setBold(true);
        $sheet->getRowDimension(1)->setRowHeight(28);
        $sheet->getStyle('A1:L1')->getFill()
            ->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('eaca97');
        $sheet->getStyle('A1:L1')->applyFromArray(array(
            'borders' =>array(
                'allborders' =>array(
                    'style' =>\PHPExcel_Style_Border::BORDER_THIN, // 设置边框
                    'color' =>array('rgb' =>'aaaaaa'),
                ),
            ),
        ));
        $sheet->setCellValue('A1', '订单提交时间')
                ->setCellValue('B1', '订单ID')
                ->setCellValue('C1', '顾客名')
                ->setCellValue('D1', '店主')
                
                ->setCellValue('E1', '产品')
                ->setCellValue('F1', '数量')
                ->setCellValue('G1', '尺寸规格')
                ->setCellValue('H1', '产品内容')
                ->setCellValue('I1', '生产状态')
                ->setCellValue('J1', '生产时间')
                ->setCellValue('K1', '所属账号')
                ->setCellValue('L1', '价值（美金）')
                
                ->setCellValue('M1', '打包状态')
                ->setCellValue('N1', '打包时间')
                ->setCellValue('O1', '物流备注')
                ->setCellValue('P1', '主题备注');
                
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(18);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(10);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(10);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(10);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(10);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('I')->setWidth(12);
        $objPHPExcel->getActiveSheet()->getColumnDimension('J')->setWidth(18);
        $objPHPExcel->getActiveSheet()->getColumnDimension('K')->setWidth(12);
        $objPHPExcel->getActiveSheet()->getColumnDimension('L')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('M')->setWidth(12);
        $objPHPExcel->getActiveSheet()->getColumnDimension('N')->setWidth(12);
        $objPHPExcel->getActiveSheet()->getColumnDimension('O')->setWidth(12);
        $objPHPExcel->getActiveSheet()->getColumnDimension('P')->setWidth(12);
        
        
        $j = 2;
        foreach ($lst as $k=>$v){
            $order_time = empty($v['a1'])? '' :date("Y-m-d H:i", $v['a1']);
            $c3_name = !empty($this->c3_arr[$v['s1']]) ? $this->c3_arr[$v['s1']]:'';
            
            
            
            
            $sheet->setCellValue('A'.$j, $order_time)
                ->setCellValue('B'.$j, $v['nid'])
                ->setCellValue('C'.$j, $v['title'])
                ->setCellValue('D'.$j, $c3_name);
            $i = $j;
            foreach ($v['plst'] as $key=>$vo){
                  $ptime = empty($vo['ptime'])? '' :date("Y-m-d H:i", $vo['ptime']);
                //   
                  if($vo['num'] && $vo['cpkg'] && isset($this->c2_1_arr[$vo['cpkg']])){
                      $price = $this->c2_1_arr[$vo['cpkg']]*$vo['num'];
                  }else{
                      $price = 0;
                  }
                 
                  $pack_status = '';
                  $pack_time = '';
                  $sheet->setCellValue('E'.$j, $vo['c1_name'])
                        ->setCellValue('F'.$j, $vo['num'])
                        ->setCellValue('G'.$j, $vo['c2_name'])
                        ->setCellValue('H'.$j, $vo['s1'])
                        ->setCellValue('I'.$j, $vo['status_name'])
                        ->setCellValue('J'.$j, $ptime)
                        ->setCellValue('K'.$j, $v['creater'])
                        ->setCellValue('L'.$j, $price)
                        ->setCellValue('M'.$j, $pack_status)
                        ->setCellValue('N'.$j, $pack_time)
                        ->setCellValue('O'.$j, $vo['logistics_remarks'])
                        ->setCellValue('P'.$j, $vo['theme_remarks']);
                    $j++;
            }    
            $endnum = $j - 1;
            $sheet->mergeCells("A{$i}:A{$endnum}"); 
            $sheet->mergeCells("B{$i}:B{$endnum}"); 
            $sheet->mergeCells("C{$i}:C{$endnum}"); 
            $sheet->mergeCells("D{$i}:D{$endnum}"); 
        }
        $sheet->getStyle('A1:P'.$endnum)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER); // 垂直居中
        $sheet->getStyle('A1:P'.$endnum)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER); // 水平居中
         
    
        $objWriter = \PHPExcel_IOFactory :: createWriter($objPHPExcel, 'Excel2007'); 
        // $objWriter = \PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
        
        // Sending headers to force the user to download the file
        header('Pragma: public');
        header('Cache-Control: public');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        
        $objWriter -> save('php://output');
    }
    
    
    public function exports()
    {
        
        //搜索条件
        $where = " AND s.`type` = '" . MODULE . "' ";
        $s = array(
            'stitle' => $this->input->get('stitle'),
            'sstatus' => $this->input->get('sstatus'),
            'scplx' => $this->input->get('scplx'),
            'sdddz' => $this->input->get('sdddz'),
            'sstamp' => $this->input->get('sstamp'),

            'stjsjs' => $this->input->get('stjsjs'),
            'stjsje' => $this->input->get('stjsje'),

            'sscsjs' => $this->input->get('sscsjs'),
            'sscsje' => $this->input->get('sscsje'),
            'scpkg' => $this->input->get('scpkg'),
        );


        $s['stitle'] = isset($s['stitle']) ? replaysql(trim($s['stitle'])) : '';
        $s['sstatus'] = isset($s['sstatus']) ? intval($s['sstatus']) : '-99';

        $s['scplx'] = isset($s['scplx']) ? intval($s['scplx']) : '-99';
        $s['sdddz'] = isset($s['sdddz']) ? intval($s['sdddz']) : '-99';
        $s['sstamp'] = isset($s['sstamp']) ? intval($s['sstamp']) : '-99';
        $s['scpkg'] = isset($s['scpkg']) ? intval($s['scpkg']) : '-99';
        
        // if($s['scpkg'] != '-99'){
        //     var_dump($s['sstamp']);
        // var_dump($s['scpkg']);
        // die;
        // }
        if($s['sscsjs']){
            if($s['sscsje']){
                
                $filename =  date('Ymd',strtotime($s['sscsjs'])).'-'.date('Ymd',strtotime($s['sscsje']));
            }else{
                $filename =  date('Ymd',strtotime($s['sscsjs'])).'起';
            }
        }else{
            if($s['sscsje']){
                $filename = '截至'.date('Ymd',strtotime($s['sscsje']));
            }else{
                $filename =  '时间不限';
            }
        }

        $pstatus = array(0 => '生产回退', 1 => '未提交', 2 => '未生产', 3 => '已生产');
        $s['pid'] = $this->usrpid;

        if (!empty($s['stitle'])) {
            $where .= " AND s.`title` LIKE '%{$s['stitle']}%' ";
        }

        if ($s['sstatus'] > -98) {
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
            $status_name = $pstatus[$s['sstatus']];
        }else{
            $status_name = '状态不限';
        }
        $filename .= '-'. $status_name; 
        if ($s['sdddz'] > -98) {
            $where .= " AND s.`s1` = '{$s['sdddz']}' ";
            $sdddz_name = $this->c3_arr[$s['sdddz']];
        }else{
            $sdddz_name = '店主不限';
        }
        $filename .= '-'. $sdddz_name; 
        #未绑邮票
        if ($s['sstamp'] == 1) {
            $where .= " AND s.`stamp` IS NULL ";
        }

        #已绑邮票
        if ($s['sstamp'] == 2) {
            $where .= " AND s.`stamp` != '' ";
        }


        $wherepr = "";

        if ($s['sstatus'] > -98) {
            
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
            if($s['scplx'] == 106){
                
                if($s['sstatus'] == 2){
                    $wherepr .= " AND `status` ='" . $s['sstatus'] . "' ";
                }
            }
            
        }

        if ($s['scplx'] > -98) {
            // $wherepr .= " AND cplx ='" . $s['scplx'] . "' ";
            $where .= " AND p.`cplx` = '{$s['scplx']}' ";
            $scplx_name = $this->c1_arr[$s['scplx']];
            
        }else{
            $scplx_name = '产品不限';
        }
        $filename .= '-'. $scplx_name; 
        if ($s['scpkg'] > -98) {
            $wherepr .= " AND cpkg ='" . $s['scpkg'] . "' ";
            $where .= " AND p.`cpkg` = '{$s['scpkg']}' ";
        }

        if ($s['stjsjs']) {
            $stjsjs = intval(strtotime($s['stjsjs'] . " 0:0:0"));
            if ($stjsjs > 0) {
                $where .= " AND s.`a1` >= {$stjsjs} ";
            }
        }

        if ($s['stjsje']) {
            $stjsje = intval(strtotime($s['stjsje'] . " 23:59:59"));
            if ($stjsje > 0) {
                $where .= " AND s.`a1` <= {$stjsje} ";
            }
        }

        if ($s['sscsjs']) {
            $sscsjs = intval(strtotime($s['sscsjs'] . " 0:0:0"));
            if ($sscsjs > 0) {
                $where .= " AND s.`ptime` >= {$sscsjs} ";
            }
        }

        if ($s['sscsje']) {
            $sscsje = intval(strtotime($s['sscsje'] . " 23:59:59"));
            if ($sscsje > 0) {
                $where .= " AND s.`ptime` <= {$sscsje} ";
            }
        }
        
        // echo $s['sscsjs'];
        $where .= " AND s.`status` > 1 ";
        //搜索条件结束
        
        if($_SESSION['user']['role_id'] == 4){
            
            if($this->column_arr){
                $column_str = '';
                foreach ($this->column_arr as $k=>$v){
                    if($k == 0){
                        $column_str = $v;
                    }else{
                        $column_str .= ','. $v;
                    }
                }
                $where .= " AND p.`cplx` in ({$column_str}) ";
            }else{
                //未分配任何产品
                $where .= " AND s.`status` > 99999 ";
            }
        }
        
        
        $sql = "SELECT DISTINCT s.*
        FROM pendy_sorder s
        LEFT JOIN pendy_sorder_products p ON s.nid=p.cid
        WHERE 1=1 {$where}
        ORDER BY s.oid desc,s.nid  desc";

        //echo ($sql);
        //die();
        $res = $this->db->query($sql);
        $alst = $res->result_array();
        
        $lst = array();
        $plst = array();
        $serial = 1;
       
        foreach ($alst as $k => $v) {
            $sql = "SELECT *
            FROM pendy_sorder_products
            WHERE cid={$v['nid']}  {$wherepr}
            order by  nid
            ";
            
            // if($wherepr){
            //     var_dump($sql);
            //     exit;
            // }
            
            $res = $this->db->query($sql);
            

            $blst = $res->result_array();

            // if($blst == []){
            //     continue;
            // }
           
           
            $numSum = 0;
           
            foreach ($blst as $kk => $vv) {
                $vv['serial'] = $serial;
                // $vv['pm'] = $this->getPM($vv['nid'], $vv['cid']);
                // $numSum  = $numSum + $vv['num'];
                
                //店主
                if($v['s1']){
                    $vv['c3_name'] = !empty($this->c3_arr[$v['s1']]) ? $this->c3_arr[$v['s1']]:'';
                }else{
                    $vv['c3_name'] = '';
                }
                
                //类型
                if($vv['cplx']){
                    $vv['c1_name'] = !empty($this->c1_arr[$vv['cplx']]) ? $this->c1_arr[$vv['cplx']]:'';
                }else{
                    $vv['c1_name'] = '';
                }
                
                //规格
                if($vv['cpkg']){
                    $vv['c2_name'] = !empty($this->c2_arr[$vv['cpkg']]) ? $this->c2_arr[$vv['cpkg']]:'';
                }else{
                    $vv['c2_name'] = '';
                }
                $vv['number_remarks'] = '';
                $vv['logistics_remarks'] = '';
                $vv['theme_remarks'] = '';
                
                $vv['title'] = !empty($v['title']) ? $v['title']:'';
                
                $vv['status_name'] = !empty($pstatus[$vv['status']]) ? $pstatus[$vv['status']]:'未生产';
                $vv['order_id'] = $v['nid'];
                $vv['order_time'] = empty($v['a1'])? '' :date("Y-m-d H:i", $v['a1']);
                $plst[] = $vv;
                $serial++;
            }

            // $v['plst'] = $plst;
            // $v['numSum'] = $numSum;
            // $lst[] = $v;
     
        }
        // return $lst;
        // echo "<pre>";
        // var_dump($plst);exit;
        error_reporting(-1);
        ini_set('display_errors', 1);

        // require_once APPPATH . 'third_party/PHPExcel/PHPExcel.php';
        // require_once APPPATH . 'third_party/PHPExcel/PHPExcel/Writer/Excel2007.php';
        // require_once APPPATH . 'third_party/PHPExcel/PHPExcel/IOFactory.php';

        $this -> load -> library('CExcel');
        $this -> load -> library('CExcelIO');
 
        $objPHPExcel = new PHPExcel();
        $objPHPExcel -> getProperties() -> setTitle("export") -> setDescription("none");
 
        $objPHPExcel -> setActiveSheetIndex(0); 
        // Field names in the first row
        $fields = array(
            // 'serial' => '序列号',
            'order_time' => '订单提交时间',
            'order_id' => '订单ID',
            'title' => '顾客名',
            'num' => '数量',
            'c3_name' => '店主',
            'c1_name' => '产品',
            'c2_name' => '尺寸规格',
            's1' => '内容',
            'status_name' => '状态',
            'number_remarks' => '数量备注',
            'logistics_remarks' =>'物流备注',
            'theme_remarks' =>'主题备注',
        );
        $col = 0;
       
        foreach ($fields as $key=>$field)
        {
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($col, 1, $field);
            $col++;
        } 
        // Fetching the table data
        $row = 2;
        foreach($plst as $data)
        {
            $col = 0;
            foreach ($fields as $key=>$field)
            {
                $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($col, $row, $data[$key]);
                $col++;
            } 
 
            $row++;
        } 
        // $sdddz_name = '店主不限';
        // $status_name = '状态不限';
        // $scplx_name = '产品不限';
        $filename .= '-订单明细表'; 
        $objPHPExcel -> setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory :: createWriter($objPHPExcel, 'Excel2007'); 
        // $objWriter = \PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
        
        // Sending headers to force the user to download the file
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');
 
        $objWriter -> save('php://output');
    }


    private function getPM($nid, $cid)
    {
        $sql = "SELECT nid
        FROM pendy_sorder_products
        WHERE cid={$cid}
        ORDER BY nid";
        $res = $this->db->query($sql);
        $lst = $res->result_array();
        $i = 1;
        $p = 0;
        foreach ($lst as $k => $v) {
            if ($v['nid'] == $nid) {
                $p = $i;
            }
            $i = $i + 1;
        }
        return $p . '/' . count($lst);
    }


    function list()
    {
        //搜索条件
        $where = " AND s.`type` = '" . MODULE . "' ";
        $s = array(
            'stitle' => $this->input->get('stitle'),
            'sstatus' => $this->input->get('sstatus'),
            'scplx' => $this->input->get('scplx'),
            'sdddz' => $this->input->get('sdddz'),
            'sstamp' => $this->input->get('sstamp'),

            'stjsjs' => $this->input->get('stjsjs'),
            'stjsje' => $this->input->get('stjsje'),

            'sscsjs' => $this->input->get('sscsjs'),
            'sscsje' => $this->input->get('sscsje'),
            
            'creater' => $this->input->get('creater'),
        );


        $s['stitle'] = isset($s['stitle']) ? replaysql(trim($s['stitle'])) : '';
        $s['sstatus'] = isset($s['sstatus']) ? intval($s['sstatus']) : '-99';

        $s['scplx'] = isset($s['scplx']) ? intval($s['scplx']) : '-99';
        $s['sdddz'] = isset($s['sdddz']) ? intval($s['sdddz']) : '-99';
        $s['sstamp'] = isset($s['sstamp']) ? intval($s['sstamp']) : '-99';


        $s['pid'] = $this->usrpid;

        if (!empty($s['stitle'])) {
            $where .= " AND s.`title` LIKE '%{$s['stitle']}%' ";
        }

        if ($s['sstatus'] > -98) {
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
        }

        if ($s['sdddz'] > -98) {
            $where .= " AND s.`s1` = '{$s['sdddz']}' ";
        }

        #未绑邮票
        if ($s['sstamp'] == 1) {
            $where .= " AND s.`stamp` IS NULL ";
        }

        #已绑邮票
        if ($s['sstamp'] == 2) {
            $where .= " AND s.`stamp` != '' ";
        }


        $wherepr = "";
        if ($s['scplx'] > -98) {
            $wherepr = " AND cplx ='" . $s['scplx'] . "' ";
            $where .= " AND p.`cplx` = '{$s['scplx']}' ";
        }

        if ($s['stjsjs']) {
            $stjsjs = intval(strtotime($s['stjsjs'] . " 0:0:0"));
            if ($stjsjs > 0) {
                $where .= " AND s.`a1` >= {$stjsjs} ";
            }
        }

        if ($s['stjsje']) {
            $stjsje = intval(strtotime($s['stjsje'] . " 23:59:59"));
            if ($stjsje > 0) {
                $where .= " AND s.`a1` <= {$stjsje} ";
            }
        }

        if ($s['sscsjs']) {
            $sscsjs = intval(strtotime($s['sscsjs'] . " 0:0:0"));
            if ($sscsjs > 0) {
                $where .= " AND s.`ptime` >= {$sscsjs} ";
            }
        }

        if ($s['sscsje']) {
            $sscsje = intval(strtotime($s['sscsje'] . " 23:59:59"));
            if ($sscsje > 0) {
                $where .= " AND s.`ptime` <= {$sscsje} ";
            }
        }
        
        if($_SESSION['user']['role_id'] == 3){
            $where .= " AND s.`creater` = '{$_SESSION['user']['username']}' ";
        }
        
        
        $user_arr = array();
        if($_SESSION['user']['role_id'] == 1 || $_SESSION['user']['role_id'] == 2){
            $s['creater'] = isset($s['creater']) ? intval($s['creater']) : '-99';
            if($s['creater'] > -99){
                $uresinfo = $this->db
                        ->select('role_id,uid,username')
                        ->from('user')
                        ->where(['uid'=>$s['creater']])
                        ->get();
                $uresinfos = $uresinfo->result_array();
                if($uresinfos[0]){
                    $where .= " AND s.`creater` = '{$uresinfos[0]['username']}' ";
                }else{
                    $where = " 1 = -1 ";
                }

            }
            $ures = $this->db
                ->select('role_id,uid,username')
                ->from('user')
                ->where_in('role_id',array(1,2,3))
                ->order_by('uid')
                ->get();
            
            $ulst = $ures->result_array();
            $user_arr = array();
            foreach ($ulst as $uk => $uv) {
                $user_arr[$uv['uid']] = $uv['username'];
            }
       }
       $this->assign('user_arr', $user_arr);
        
        //搜索条件结束

        $this->assign('s', $s);
        $lst = $this->getlst($where, $wherepr);
        $this->assign('lst', $lst);
        $this->display('admin/' . MODULE . '/list.tpl');
    }

    public function add()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data = array(
                'cid' => 0,
                'tid' => 0,
                'oid' => $this->input->post('oid'),
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'cont' => $this->input->post('cont'),
                's1' => intval($this->input->post('s1')),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );

            $data['createtime'] = $data['updatetime'];
            $data['creater'] = $data['updater'];
            $data['pid'] = intval($this->usrpid);

            //数据校验
            $data['oid'] = intval(strtotime($data['oid']));
            if ($data['oid'] <= 0) {
                $data['oid'] = time();
            }


            $this->db->insert('sorder', $data);
            $nid = $this->db->insert_id();
            $this->User_model->log("添加" . $this->modeName, $data);
            
            //遍历得到上传的产品个数
            $cplxs = [];
            $i = 0;
            while(true){
                $cplx = $this->input->post('cplx'.$i);
                if($cplx){
                    $cplxs[] = $cplx;
                }else{
                    break;
                }
                $i++;
                if($i >= 20){//防止死循环，最多20个
                    break;
                }
            }

            //批量插入
            foreach($cplxs as $k => $v){
                $data2 = array(
                    'cid' => $nid,
                    'pic' => $this->input->post('pic'.$k),
                    'cpkg' => intval($this->input->post('cpkg'.$k)),
                    'cplx' => intval($this->input->post('cplx'.$k)),
                    'num' => intval($this->input->post('num'.$k)),
                    's1' => $this->input->post('ss1'.$k),
                    "type" => MODULE,
                    "updatetime" => time(),
                    "updater" => $_SESSION['user']['username'],
                );
                $data2['createtime'] = $data['updatetime'];
                $data2['creater'] = $data['updater'];
                $data2['pid'] = intval($this->usrpid);
    
                $this->db->insert('sorder_products', $data2);
                $this->User_model->log("添加产品", $data2);
            }

            // $data2 = array(
            //     'cid' => $nid,
            //     'pic' => $this->input->post('pic'),
            //     'cpkg' => intval($this->input->post('cpkg')),
            //     'cplx' => intval($this->input->post('cplx')),
            //     'num' => intval($this->input->post('num')),
            //     's1' => $this->input->post('ss1'),
            //     "type" => MODULE,
            //     "updatetime" => time(),
            //     "updater" => $_SESSION['user']['username'],
            // );
            // $data2['createtime'] = $data['updatetime'];
            // $data2['creater'] = $data['updater'];
            // $data2['pid'] = intval($this->usrpid);

            // $this->db->insert('sorder_products', $data2);
            // $this->User_model->log("添加产品", $data2);


            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");


            $this->error('操作失败！');
        } else {

            $this->display('admin/' . MODULE . '/input.tpl');
        }
    }

    public function update()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data = array(
                'cid' => 0,
                'tid' => 0,
                'oid' => $this->input->post('oid'),
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'cont' => $this->input->post('cont'),
                's1' => intval($this->input->post('s1')),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );


            //数据校验
            $data['oid'] = intval(strtotime($data['oid']));
            if ($data['oid'] <= 0) {
                $data['oid'] = time();
            }


            $nid = intval($this->input->post('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder", $where);

            $this->db
                ->where($where)
                ->update('sorder', $data);

            $this->User_model->log("修改" . $this->modeName . ":" . $nid, $data);

            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
        } else {
            $nid = intval($this->input->get('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            //var_dump($where);
            //die();

            $odata = $this->ckeckbyodata("sorder", $where);
            $odata['oid'] = date("Y-m-d H:i", $odata['oid']);

            $this->assign('data', $odata);
            $this->display('admin/' . MODULE . '/input.tpl');
        }
    }


    public function update3()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data = array(
                'stamp' => $this->input->post('stamp'),
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );


            //数据校验
            $nid = intval($this->input->post('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder", $where);

            $this->db
                ->where($where)
                ->update('sorder', $data);

            $this->User_model->log("更新邮票:" . $nid, $data);

            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
        } else {
            $nid = intval($this->input->get('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder", $where);
            $odata['oid'] = date("Y-m-d H:i", $odata['oid']);

            $this->assign('data', $odata);
            $this->display('admin/' . MODULE . '/input3.tpl');
        }
    }

    public function add2()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data2 = array(
                'pic' => $this->input->post('pic0'),
                'cpkg' => intval($this->input->post('cpkg0')),
                'cplx' => intval($this->input->post('cplx0')),
                'num' => intval($this->input->post('num0')),
                'cid' => intval($this->input->post('cid')),
                's1' => $this->input->post('ss10'),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );

            $data2['createtime'] = $data2['updatetime'];
            $data2['creater'] = $data2['updater'];
            $data2['pid'] = intval($this->usrpid);

            //数据校验
            if ($data2['cid'] <= 0) {
                $this->error('订单错误!');
            }


            $this->db->insert('sorder_products', $data2);
            $this->User_model->log("添加产品", $data2);


            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");


            $this->error('操作失败！');
        } else {
            $cid = $this->input->get('cid');
            $odata = array('cid' => $cid);
            $this->assign('data', $odata);
            $this->display('admin/' . MODULE . '/input2.tpl');
        }
    }


    public function update2()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data2 = array(
                'pic' => $this->input->post('pic0'),
                'cpkg' => intval($this->input->post('cpkg0')),
                'cplx' => intval($this->input->post('cplx0')),
                'num' => intval($this->input->post('num0')),
                's1' => $this->input->post('ss10'),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );


            //数据校验
            $nid = intval($this->input->post('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder_products", $where);

            $this->db
                ->where($where)
                ->update('sorder_products', $data2);

            $this->User_model->log("修改产品:" . $nid, $data2);

            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
        } else {
            $nid = intval($this->input->get('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder_products", $where);
            //$odata['oid'] = date("Y-m-d H:i", $odata['oid']);

            $this->assign('data', $odata);
            $this->display('admin/' . MODULE . '/input2.tpl');
        }
    }



    public function delete()
    {
        $nid = intval($this->input->get('nid'));

        $where = array("nid" => $nid);
        if ($this->usrpid > 0) {
            $where['pid'] = $this->usrpid;
        }

        $odata = $this->ckeckbyodata("sorder", $where, true);

        $this->db
            ->where($where)
            ->delete('sorder');

        $where = array("cid" => $nid);
        $this->db
            ->where($where)
            ->delete('sorder_products');

        $this->User_model->log("删除" . $this->modeName . ":" . $nid, $odata);
        $this->ajaxSuccess();
    }

    public function delete2()
    {
        $nid = intval($this->input->get('nid'));

        $where = array("nid" => $nid);
        if ($this->usrpid > 0) {
            $where['pid'] = $this->usrpid;
        }

        $odata = $this->ckeckbyodata("sorder_products", $where, true);

        $this->db
            ->where($where)
            ->delete('sorder_products');


        $this->User_model->log("删除产品:" . $nid, $odata);
        $this->ajaxSuccess();
    }



    #设为已生产
    public function dprint()
    {
        $nid = intval($this->input->get('nid'));
        $t = intval($this->input->get('t'));

        $where = array("nid" => $nid);
        if ($this->usrpid > 0) {
            $where['pid'] = $this->usrpid;
        }

        $odata = $this->ckeckbyodata("sorder_products", $where, true);

        if ($t == 3) {

            $this->db
                ->where($where)
                ->update('sorder_products', array("status" => 3, "ptime" => time()));

            $this->User_model->log("已打印产品:" . $nid, $odata);

            //改变订单状态
            $sql = "SELECT nid
            FROM pendy_sorder_products
            WHERE cid={$odata['cid']} AND `status`!=3
            order by  nid
            ";
            //echo $sql;
            $res = $this->db->query($sql);
            $blst = $res->result_array();
            if (empty($blst)) {
                $where2 = array("nid" => $odata['cid']);
                if ($this->usrpid > 0) {
                    $where2['pid'] = $this->usrpid;
                }
                $this->db
                    ->where($where2)
                    ->update('sorder', array("status" => $t, "ptime" => time()));

                $this->User_model->log("打印完成订单:" . $odata['cid']);
            }
        }

        if ($t == -1) {
            $where2 = array("nid" => $odata['cid']);
            $this->db
                ->where($where2)
                ->update('sorder', array("status" => 0, "ptime" => 0));

            $where2 = array("cid" => $odata['cid']);
            $this->db
                ->where($where2)
                ->update('sorder_products', array("status" => 0, "ptime" => 0));
        }

        $this->ajaxSuccess();
    }

    #批量设为已生产
    public function dprintAll()
    {
        $idAll = $this->input->get('idAll');
        
        // 开启事务
        $this->db->trans_start();

        foreach ($idAll as $key => $value) {
            $where = array("nid" => $value);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }
            $odata = $this->ckeckbyodata("sorder_products", $where, true);
            $this->db
                ->where($where)
                ->update('sorder_products', array("status" => 3, "ptime" => time()));

            $this->User_model->log("已打印产品:" . $nid, $odata);

            //改变订单状态
            $sql = "SELECT nid
            FROM pendy_sorder_products
            WHERE cid={$odata['cid']} AND `status`!=3
            order by  nid
            ";
            //echo $sql;
            $res = $this->db->query($sql);
            $blst = $res->result_array();
            if (empty($blst)) {
                $where2 = array("nid" => $odata['cid']);
                if ($this->usrpid > 0) {
                    $where2['pid'] = $this->usrpid;
                }
                $this->db
                    ->where($where2)
                    ->update('sorder', array("status" => $t, "ptime" => time()));

                $this->User_model->log("打印完成订单:" . $odata['cid']);
            }
        }
        // 完成事务
        $this->db->trans_complete();

        if($this->db->trans_status() === FALSE){
            // 如果事务失败，执行回滚
            $this->db->trans_rollback();
            $this->ajaxError();
        }else {
            // 如果事务成功，执行提交
            $this->db->trans_commit();
            $this->ajaxSuccess();
        }
    }

    #订单提交生产
    public function doprod()
    {
        $nid = intval($this->input->get('nid'));

        $where = array("nid" => $nid);
        if ($this->usrpid > 0) {
            $where['pid'] = $this->usrpid;
        }

        $odata = $this->ckeckbyodata("sorder", $where, true);

        $this->db
            ->where($where)
            ->update('sorder', array("status" => 2, "a1" => time()));

        $where2 = array("cid" => $nid);
        if ($this->usrpid > 0) {
            $where2['pid'] = $this->usrpid;
        }

        $this->db
            ->where($where2)
            ->update('sorder_products', array("status" => 2, "a1" => time()));
        $this->ajaxSuccess();
    }

    // 打印所有的酒标签
    public function mprintjiu()
    {
        
        $nid = intval($this->input->get('nid'));
        
        if ($nid > 0) {
            $where = array("nid" => $nid);
            
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }
            $odata = $this->ckeckbyodata("sorder", $where);
            
            $wherepr = "";
            $sql = "SELECT *
            FROM pendy_sorder_products
            WHERE cid={$nid}  {$wherepr}
            order by  nid
            ";
            //echo $sql;
            $res = $this->db->query($sql);
            $odata['lst'] = $res->result_array();

            
            
            $data2 = array('a3' => $odata['a3'] + 1);
            $this->db
                ->where(array('nid' => $nid))
                ->update('sorder', $data2);
                
            $aconfig = $this->getKeyVal("config", "`key` as k,`value` as v", array(), " oid ");
            // $odata['title'];

            // var_dump($c2_arr[$v.cpkg]);
            
            $this->assign('aconfig', $aconfig);

            $arr = [];

            foreach ($odata['lst'] as $key => $value) {
                for($i=0;$i<intval($value['num']);$i++){
                    $arr[] = [
                        'title' => $odata['title'],
                        's1' => $odata['s1'],
                        'cpkg' => $value['cpkg'],
                        'pic' => $value['pic'],
                        'sort' => $i+1
                    ];
                }
            
                
                
            }
            $this->assign('data', $arr);
            // var_dump($arr);
            // die;
            
            $this->display('admin/' . MODULE . '/mprintjiu.tpl');
        }

        die();
    }

    // 批量打印生产文件
    public function mprintjiuAll()
    {
        //搜索条件
        $where = " AND s.`type` = '" . MODULE . "' ";
        $s = array(
            'stitle' => $this->input->get('stitle'),
            'sstatus' => $this->input->get('sstatus'),
            'scplx' => $this->input->get('scplx'),
            'sdddz' => $this->input->get('sdddz'),
            'sstamp' => $this->input->get('sstamp'),

            'stjsjs' => $this->input->get('stjsjs'),
            'stjsje' => $this->input->get('stjsje'),

            'sscsjs' => $this->input->get('sscsjs'),
            'sscsje' => $this->input->get('sscsje'),
            'scpkg' => $this->input->get('scpkg'),
        );

      


        $s['stitle'] = isset($s['stitle']) ? replaysql(trim($s['stitle'])) : '';
        $s['sstatus'] = isset($s['sstatus']) ? intval($s['sstatus']) : '-99';

        $s['scplx'] = isset($s['scplx']) ? intval($s['scplx']) : '-99';
        $s['sdddz'] = isset($s['sdddz']) ? intval($s['sdddz']) : '-99';
        $s['sstamp'] = isset($s['sstamp']) ? intval($s['sstamp']) : '-99';
        $s['scpkg'] = isset($s['scpkg']) ? intval($s['scpkg']) : '-99';


        $s['pid'] = $this->usrpid;

        if (!empty($s['stitle'])) {
            $where .= " AND s.`title` LIKE '%{$s['stitle']}%' ";
        }

        if ($s['sstatus'] > -98) {
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
        }

        if ($s['sdddz'] > -98) {
            $where .= " AND s.`s1` = '{$s['sdddz']}' ";
        }

        #未绑邮票
        if ($s['sstamp'] == 1) {
            $where .= " AND s.`stamp` IS NULL ";
        }

        #已绑邮票
        if ($s['sstamp'] == 2) {
            $where .= " AND s.`stamp` != '' ";
        }


        $wherepr = "";
        if ($s['scplx'] > -98) {
            $wherepr = " AND cplx ='" . $s['scplx'] . "' ";
            $where .= " AND p.`cplx` = '{$s['scplx']}' ";
        }

        // if ($s['scpkg'] > -98) {
        //     $wherepr = " AND cpkg ='" . $s['scpkg'] . "' ";
        //     $where .= " AND p.`cpkg` = '{$s['scpkg']}' ";
        // }
        

        if ($s['stjsjs']) {
            $stjsjs = intval(strtotime($s['stjsjs'] . " 0:0:0"));
            if ($stjsjs > 0) {
                $where .= " AND s.`a1` >= {$stjsjs} ";
            }
        }

        if ($s['stjsje']) {
            $stjsje = intval(strtotime($s['stjsje'] . " 23:59:59"));
            if ($stjsje > 0) {
                $where .= " AND s.`a1` <= {$stjsje} ";
            }
        }

        if ($s['sscsjs']) {
            $sscsjs = intval(strtotime($s['sscsjs'] . " 0:0:0"));
            if ($sscsjs > 0) {
                $where .= " AND s.`ptime` >= {$sscsjs} ";
            }
        }

        if ($s['sscsje']) {
            $sscsje = intval(strtotime($s['sscsje'] . " 23:59:59"));
            if ($sscsje > 0) {
                $where .= " AND s.`ptime` <= {$sscsje} ";
            }
        }
        // echo $s['sscsjs'];
        $where .= " AND s.`status` > 1 ";
        //搜索条件结束
        $lst = $this->getlst($where, $wherepr);

        $arr = [];
        $title = '';
        
        

        foreach ($lst as $k => $v) {
            $sort = 0;
            $title = $v['title'];
            $numSum = $v['numSum'];
            foreach ($v['plst'] as $key => $value){
                
                if($value['cpkg'] == 118 || $value['cpkg'] == 120 || $value['cpkg'] == 121){
                    if($value['num'] > 2){
                        $value['num'] = $value['num'] / 2;
                    }
                }
                    for($i=0;$i<intval($value['num']);$i++){
                        
                        if($value['cpkg'] == 118 || $value['cpkg'] == 120 || $value['cpkg'] == 121){
                            $sort = $sort + 2;
                        }else {
                            $sort = $sort + 1;
                        }
                        $arr[] = [
                            'nid' => $value['nid'],
                            'title' => $title,
                            'cpkg' => $value['cpkg'],
                            'pic' => $value['pic'],
                            'sort' => $sort,
                            'numSum' => $numSum,
                            'cpkg' => $value['cpkg'],
                            'status' => $value['status']
                        ];
                        
                    }
            }

        }
        $arr1 = [];
        // 所有的产品id
        $idAll = [];

        // 提取尺寸类型
        foreach ($arr as $key => $value) {
            if( $s['scpkg'] > -98){
                if($value['cpkg'] == $s['scpkg']){
                    $arr1[] = $value;
                    if(!in_array($value['nid'],$idAll)){
                        $idAll[] = $value['nid'];
                    }
                }
            }else {
                $arr1[] = $value;
                if(!in_array($value['nid'],$idAll)){
                    $idAll[] = $value['nid'];
                }
            }
        }
        

        // 返回所有的产品id
        $idAll = json_encode($idAll);

        
        
        $this->assign('data', $arr1);
        $this->assign('idAll', $idAll);
   
        $this->display('admin/' . MODULE . '/mprintjiu.tpl');
    }

    // 批量打印邮票
    public function mprintStamp() {
        $lstNidArr = $this->input->get('lstNidArr');
        // $lstNidArr = json_encode(explode(",", $this->input->get('lstNidArr')));

      
        $sql = "SELECT stamp
        FROM pendy_sorder
        WHERE nid in ({$lstNidArr})
        order by  nid DESC
        ";
       
        $res = $this->db->query($sql);
        $data = $res->result_array();

        $this->assign('data', $data);
        $this->assign('lstNidArr', json_encode(explode(",", $lstNidArr)));

        $this->display('admin/' . MODULE . '/mprintstamp.tpl');
    }

    public function dprintStamp()
    {
        $lstNidArr = $this->input->get('lstNidArr');
        
        // 开启事务
        $this->db->trans_start();

        foreach ($lstNidArr as $key => $value) {

            $where = array("nid" => $value);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }
            $odata = $this->ckeckbyodata("sorder", $where);


            $data2 = array('a2' => $odata['a2'] + 1);
            $this->db
                ->where(array('nid' => $value))
                ->update('sorder', $data2);

            // $where = array("nid" => $value);
            // $this->db
            //     ->where($where)
            //     ->update('sorder', array("ptime" => time()));

            // $this->User_model->log("打印完成订单:" . $odata['cid']);
        }
        // 完成事务
        $this->db->trans_complete();

        if($this->db->trans_status() === FALSE){
            // 如果事务失败，执行回滚
            $this->db->trans_rollback();
            $this->ajaxError();
        }else {
            // 如果事务成功，执行提交
            $this->db->trans_commit();
            $this->ajaxSuccess();
        }
    }
}
