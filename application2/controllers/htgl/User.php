<?php
defined('BASEPATH') or exit('No direct script access allowed');
require_once "ini.php";

class User extends PY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->assign('modeName', '管理员');

        $where = array('status' => 1);
        if (!$this->isFounder(false)) {
            $where['role_id !='] = 1;
        }

        $role_arr = $this->getKeyVal("user_role", "`role_id` as k,`role_name` as v", $where);
        $this->assign('role_arr', $role_arr);

        $part_arr = $this->getKeyVal("user_part", "`part_id` as k,`part_name` as v", array('status' => 1));
        $this->assign('part_arr', $part_arr);

        $this->assign('slst', $this->config->item('slst'));
    }

    public function index()
    {
    }

    function list() {
        $where = array();
        if (!$this->isFounder(false)) {
            $where['role_id !='] = 1;
        }

        $s = array(
            'spid' => $this->input->get('spid'),
            'srid' => $this->input->get('srid'),
            'skeyw' => replaysql($this->input->get('skeyw')),
        );

        $s['spid'] = isset($s['spid']) ? intval($s['spid']) : -99;
        $s['srid'] = isset($s['srid']) ? intval($s['srid']) : -99;
        $this->assign('s', $s);

        if ($s['spid'] != -99) {
            $where['part_id'] = $s['spid'];
        }

        if ($s['srid'] != -99) {
            $where['role_id'] = $s['srid'];
        }

        if ($s['skeyw']) {
            $where[" nickname like '%{$s['skeyw']}%' "] = null;

        }

        $lst = $this->getPageLst("user", $where, " uid desc ");

        if ($this->isFounder(false)) {
            $this->load->library('CGAuth');
            $authkey = $this->config->item('authkey');
            foreach ($lst as $uk => $uv) {
                $lst[$uk]['Authenticator'] = $this->cgauth->base32_encode($uv['username'] . $uv['salt'] . $authkey);
            }
        }

        $this->assign('lst', $lst);

        $this->assignQX(array("add", "update", "delete", "teacher_add", "teacher_update"));

        $this->display('admin/user/list.tpl');
    }

    public function delete()
    {
        $uid = intval($this->input->get('uid'));
        if ($uid <= 0) {
            $this->ajaxError('用户不存在！');
        }
        $odata = $this->User_model->get_by_uid($uid);
        if (empty($odata)) {
            $this->ajaxError('用户不存在！');
        }

        if ($odata['role_id'] == 1 && !$this->isFounder(false)) {
            $this->ajaxError('您不是创始人，不能删除超级管理员！');
        }

        if ($odata['uid'] == intval(@$_SESSION['user']['uid'])) {
            $this->ajaxError('不能删除自己！');
        }

        if ($uid != $odata['uid']) {
            $this->ajaxError('参数错误！');
        }

        if ($this->User_model->delUser($uid)) {
            $this->User_model->log("删除管理员", $odata);
            $this->ajaxSuccess();
        }

        $this->ajaxError("删除失败");

    }

    public function add()
    {

        $action = $this->input->post('action');
        if ($action == "dosave") {
            $this->dosave("add");
        } else {
            $this->display('admin/user/input.tpl');
        }

    }

    public function teacher_add()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $this->dosave("teacher_add");
        } else {
            $this->display('admin/user/teacher_input.tpl');
        }

    }

    public function teacher_update()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $this->dosave("teacher_update");
        } else {
            $uid = intval($this->input->get('uid'));
            if ($uid <= 0) {
                $this->error('用户不存在！');
            }
            $odata = $this->User_model->get_by_uid($uid);
            if (empty($odata)) {
                $this->error('用户不存在！');
            }

            $res = $this->db
                ->select("*")
                ->from("user_desc")
                ->where(array('uid' => $uid))
                ->get();
            $lst = $res->result_array();
            if (!empty($lst)) {
                $usr = $lst[0];
                $usr['json'] = json_decode($usr['json'], true);
                foreach ($usr as $k => $v) {
                    $odata[$k] = $v;
                }
            }

            $this->assign('data', $odata);

            if ($odata['role_id'] == 1 && !$this->isFounder(false)) {
                $this->error('您不是创始人，不能创建修改超级管理员！');
            }

            if ($odata['uid'] == intval(@$_SESSION['user']['uid'])) {
                $this->error('不能修改自己！');
            }

            $this->display('admin/user/teacher_input.tpl');
        }
    }

    public function update()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $this->dosave("update");
        } else {
            $uid = intval($this->input->get('uid'));
            if ($uid <= 0) {
                $this->error('用户不存在！');
            }
            $odata = $this->User_model->get_by_uid($uid);
            if (empty($odata)) {
                $this->error('用户不存在！');
            }
            $this->assign('data', $odata);

            if ($odata['role_id'] == 1 && !$this->isFounder(false)) {
                $this->error('您不是创始人，不能创建修改超级管理员！');
            }

            if ($odata['uid'] == intval(@$_SESSION['user']['uid'])) {
                $this->error('不能修改自己！');
            }

            $this->display('admin/user/input.tpl');
        }

    }

    private function dosave($act)
    {
        $data = array(
            'uid' => $this->input->post('uid'),
            'username' => $this->input->post('username'),
            'email' => $this->input->post('email'),
            'nickname' => $this->input->post('nickname'),
            'password' => $this->input->post('password'),
            'apassword' => $this->input->post('apassword'),
            'role_id' => $this->input->post('role_id'),
            'part_id' => $this->input->post('part_id'),
            'remark' => $this->input->post('remark'),
            'status' => intval($this->input->post('status')),
        );

        $data['uid'] = intval($data['uid']);
        $data['role_id'] = intval($data['role_id']);
        $data['part_id'] = intval($data['part_id']);

        if ($data['role_id'] == 1 && !$this->isFounder(false)) {
            $this->error('您不是创始人，不能创建修改超级管理员！');
        }

        if ($data['uid'] == intval(@$_SESSION['user']['uid'])) {
            $this->error('不能修改自己！');
        }

        if ($act == "update" || $act == "teacher_update") {
            if ($data['uid'] <= 0) {
                $this->error('用户不存在！');
            }
            $odata = $this->User_model->get_by_uid($data['uid']);
            if (empty($odata)) {
                $this->error('用户不存在！');
            }
            if ($data['uid'] != $odata['uid']) {
                $this->error('参数错误！');
            }
        }

        if ($act == "add" || $act == "teacher_add") {
            //判断用户名
            if (!$this->User_model->check_username($data['username'], 0)) {
                $this->error('用户名错误或已被其他用户注册(用户名只允许使用字母、数字、下划线，且必须需以字母开头，不能小于6个字符和大于15个字符)！');
            }

            //判断密码
            if (empty($data['password']) || empty($data['apassword'])) {
                $this->error('请输入密码！');
            }

        }

        //密码校验
        if (!empty($data['password']) || !empty($data['apassword'])) {
            if ($data['password'] != $data['apassword']) {
                $this->error('两次输入的密码不一致！');
            }

            if (!$this->User_model->check_password($data['password'])) {
                $this->error('密码不能小于6个字符和大于15个字符！');
            }

            $data['salt'] = $this->User_model->salt();

        }

        //检验邮箱
        if (!$this->User_model->check_email($data['email'], $data['uid'])) {
            $this->error('邮箱错误或已被其他用户注册！');
        }
        if ($act == "update" || $act == "teacher_update") {
            if ($this->User_model->updateUser($data, $data['uid'])) {
                $data['password'] = !empty($data['password']) ? '***' : '';
                $data['apassword'] = !empty($data['apassword']) ? '***' : '';
                $data['salt'] = !empty($data['salt']) ? '***' : '';

                $this->User_model->log("修改管理员", $data);

                $uid = $data['uid'];
                if ($act == "teacher_update") {
                    $this->teachersave($uid);
                }
                $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
            }
        }

        if ($act == "add" || $act == "teacher_add") {
            $uid = $this->User_model->addUser($data);
            if ($uid > 0) {
                $data['password'] = !empty($data['password']) ? '***' : '';
                $data['apassword'] = !empty($data['apassword']) ? '***' : '';
                $data['salt'] = !empty($data['salt']) ? '***' : '';

                $this->User_model->log("添加管理员", $data);

                if ($act == "teacher_update") {
                    $this->teachersave($uid);
                }
                $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
            }

        }

        $this->error('操作失败！');
    }

    private function teachersave($uid)
    {
        $pics = array();
        $picsid = $this->input->post('picsid');
        $upics = $this->input->post('pics');
        $picsdesc = $this->input->post('picsdesc');

        //var_dump($picsid);
        //var_dump($upics);
        //var_dump($picsdesc);
        //die();
        if (!empty($picsid)) {
            foreach ($picsid as $k => $v) {
                $p = array();
                $p['id'] = $v;
                $p['pic'] = @$upics[$v];
                $p['desc'] = @$picsdesc[$v];
                $pics[$v] = $p;
            }
        }

        //var_dump($pics);
        $data = array(
            'idcare' => $this->input->post('idcare'),
            'lxdh' => $this->input->post('lxdh'),
            'json' => json_encode($this->input->post('json'), JSON_UNESCAPED_UNICODE),
            'pics' => json_encode($pics, JSON_UNESCAPED_UNICODE),
        );

        $res = $this->db
            ->select("*")
            ->from("user_desc")
            ->where(array('uid' => $uid))
            ->get();
        $lst = $res->result_array();

        if (empty($lst)) {
            //add
            $data['uid'] = $uid;
            $this->db
                ->insert('user_desc', $data);
        } else {
            $this->db
                ->where("uid", $uid)
                ->update('user_desc', $data);

        }
        $this->User_model->log("修改教师信息:" . $uid, $data);
        $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list?srid=5");
        die();
    }

    public function role()
    {
        $where = array();
        if (!$this->isFounder(false)) {
            $where['role_id !='] = 1;
        }

        $res = $this->db
            ->select("*")
            ->from("user_role")
            ->where($where)
            ->order_by("role_id")
            ->get();
        $lst = $res->result_array();
        $this->assign('lst', $lst);

        $this->assignQX(array("role_add", "role_update", "role_delete", "role_auth_setting"));
        $this->display('admin/user/role.tpl');
    }

    public function role_add()
    {
        $data = array(
            'role_name' => $this->input->post('role_name'),
        );

        if (empty($data['role_name'])) {
            $this->error('角色名称不能为空！');
        }

        $this->db->insert('user_role', $data);
        $this->User_model->log("添加管理员角色", $data);
        $this->success('操作成功！', RELROUTE . "/" . MODULE . "/role");

    }

    public function role_update()
    {
        $data = array(
            'role_name' => $this->input->post('role_name'),
            'role_id' => intval($this->input->post('role_id')),
            'status' => intval($this->input->post('status')),
        );

        if (empty($data['role_name'])) {
            $this->error('角色名称不能为空！');
        }

        // 检查
        if ($data['role_id'] <= 0) {
            $this->error('没有该角色！');
        }
        if (($data['role_id'] == 1) && ($_SESSION['user']['uid'] != 1)) {
            $this->error('您没权限修改超级管理员角色信息！');
        }
        if ($data['role_id'] == $_SESSION['user']['role_id']) {
            $this->error('自身角色不能修改！');
        }

        $res = $this->db
            ->select("*")
            ->from("user_role")
            ->where("role_id", $data['role_id'])
            ->get();
        if (!empty($res->result_array())) {
            $odata = $res->result_array()[0];
        } else {
            $this->error('该角色不存在!');
        }

        $this->db
            ->where("role_id", $data['role_id'])
            ->update('user_role', $data);

        $this->User_model->log("修改管理员角色", $data);
        $this->success('操作成功！', RELROUTE . "/" . MODULE . "/role");

    }

    public function role_delete()
    {
        $data = array(
            'role_id' => intval($this->input->get('role_id')),
        );

        if ($data['role_id'] <= 1) {
            $this->ajaxError('不能删除该角色！');
        }

        if ($data['role_id'] == $_SESSION['user']['role_id']) {
            $this->ajaxError('自身角色不能删除！');
        }

        $res = $this->db
            ->select("*")
            ->from("user_role")
            ->where("role_id", $data['role_id'])
            ->get();
        if (!empty($res->result_array())) {
            $odata = $res->result_array()[0];
        } else {
            $this->ajaxError('该角色不存在!');
        }

        $res = $this->db
            ->select("*")
            ->from("user")
            ->where("role_id", $data['role_id'])
            ->get();
        if (!empty($res->result_array())) {
            $this->ajaxError('该角色存在用户，请先修改该角色下的用户为其他角色，或者删除该角色下的用户，您才能删除该角色！');
        }

        $this->db
            ->where("role_id", $data['role_id'])
            ->delete('user_role');
        $this->User_model->log("删除管理员角色", $odata);
        $this->ajaxSuccess();

    }

    public function role_auth_setting()
    {
        $data = array(
            'role_id' => intval($this->input->get('role_id')),
        );

        if ($data['role_id'] <= 0) {
            $this->error('没有该角色！');
        }
        if (($data['role_id'] == 1) && ($_SESSION['user']['uid'] != 1)) {
            $this->error('您没权限修改超级管理员角色信息！');
        }
        if ($data['role_id'] == $_SESSION['user']['role_id']) {
            $this->error('自身角色不能修改！');
        }

        $res = $this->db
            ->select("*")
            ->from("user_role")
            ->where("role_id", $data['role_id'])
            ->get();
        if (!empty($res->result_array())) {
            $odata = $res->result_array()[0];
        } else {
            $this->error('该角色不存在!');
        }

        // 分割角色拥有的权限成数组（如果是超级管理员，拥有所有权限）
        $this->config->load('authaction');
        $_authaction = $this->config->item('authaction');

        if ($odata['role_id'] == 1) {

            $str = '';
            $comma = '';
            foreach ($_authaction as $v) {
                $str .= $comma . $v['app'];
                $comma = ',';
                foreach ($v['children'] as $cv) {
                    $str .= $comma . $v['app'] . '/' . $cv['module'];
                    foreach ($cv['children'] as $ccv) {
                        $str .= $comma . $v['app'] . '/' . $cv['module'] . '/' . $ccv['action'];
                    }
                }
            }
            $role_auth = explode(',', $str);
            unset($str, $comma);
        } else {
            $role_auth = explode(',', $odata['authaction']);
            unset($odata['authaction']);
        }
       
        $this->assign('odata', $odata);
        $this->assign('_authaction', $_authaction);
        $this->assign('role_auth', $role_auth);

        $this->assignQX(array("role_add", "role_update", "role_delete", "role_auth_setting"));

        $this->display('admin/user/role_auth_setting.tpl');
    }

    public function role_auth_update()
    {
        $data = array(
            'role_id' => intval($this->input->post('role_id')),
            'authaction' => $this->input->post('authaction'),
        );

        if ($data['role_id'] <= 0) {
            $this->error('没有该角色！');
        }
        if (($data['role_id'] == 1) && ($_SESSION['user']['uid'] != 1)) {
            $this->error('您没权限修改超级管理员角色信息！');
        }
        if ($data['role_id'] == $_SESSION['user']['role_id']) {
            $this->error('自身角色不能修改！');
        }

        $res = $this->db
            ->select("*")
            ->from("user_role")
            ->where("role_id", $data['role_id'])
            ->get();
        if (!empty($res->result_array())) {
            $odata = $res->result_array()[0];
        } else {
            $this->error('该角色不存在!');
        }

        $data['authaction'] = implode(',', $data['authaction']);
        $this->db
            ->where("role_id", $data['role_id'])
            ->update('user_role', $data);

        $this->User_model->log("修改管理员角色权限", $data);
        $this->success('操作成功！', RELROUTE . "/" . MODULE . "/role");
    }

    public function part()
    {
        $where = array();

        $res = $this->db
            ->select("*")
            ->from("user_part")
            ->where($where)
            ->order_by("part_id")
            ->get();
        $lst = $res->result_array();
        $this->assign('lst', $lst);

        $this->assignQX(array("part_add", "part_update", "part_delete"));
        $this->display('admin/user/part.tpl');
    }

    public function part_add()
    {
        $data = array(
            'part_name' => $this->input->post('part_name'),
        );

        if (empty($data['part_name'])) {
            $this->error('单位名称不能为空！');
        }

        $this->db->insert('user_part', $data);
        $this->User_model->log("添加管理员单位", $data);
        $this->success('操作成功！', RELROUTE . "/" . MODULE . "/part");

    }

    public function part_update()
    {
        $data = array(
            'part_name' => $this->input->post('part_name'),
            'part_id' => intval($this->input->post('part_id')),
            'status' => intval($this->input->post('status')),
        );

        if (empty($data['part_name'])) {
            $this->error('单位名称不能为空！');
        }

        // 检查
        if ($data['part_id'] <= 0) {
            $this->error('没有该单位！');
        }
        if (($data['part_id'] == 1) && ($_SESSION['user']['uid'] != 1)) {
            $this->error('您没权限修改该单位信息！');
        }
        if ($data['part_id'] == $_SESSION['user']['part_id'] && $_SESSION['user']['uid'] != 1) {
            $this->error('自身单位不能修改！');
        }

        $res = $this->db
            ->select("*")
            ->from("user_part")
            ->where("part_id", $data['part_id'])
            ->get();
        if (!empty($res->result_array())) {
            $odata = $res->result_array()[0];
        } else {
            $this->error('该单位不存在!');
        }

        $this->db
            ->where("part_id", $data['part_id'])
            ->update('user_part', $data);

        $this->User_model->log("修改管理员单位", $data);
        $this->success('操作成功！', RELROUTE . "/" . MODULE . "/part");

    }

    public function part_delete()
    {
        $data = array(
            'part_id' => intval($this->input->get('part_id')),
        );

        if ($data['part_id'] <= 1) {
            $this->ajaxError('不能删除该单位！');
        }

        if ($data['part_id'] == $_SESSION['user']['part_id']) {
            $this->ajaxError('自身单位不能删除！');
        }

        $res = $this->db
            ->select("*")
            ->from("user_part")
            ->where("part_id", $data['part_id'])
            ->get();
        if (!empty($res->result_array())) {
            $odata = $res->result_array()[0];
        } else {
            $this->ajaxError('该单位不存在!');
        }

        $res = $this->db
            ->select("*")
            ->from("user")
            ->where("part_id", $data['part_id'])
            ->get();
        if (!empty($res->result_array())) {
            $this->ajaxError('该单位存在用户，请先修改该单位下的用户为其他单位，或者删除该单位下的用户，您才能删除该单位！');
        }

        $this->db
            ->where("part_id", $data['part_id'])
            ->delete('user_part');
        $this->User_model->log("删除管理员单位", $odata);
        $this->ajaxSuccess();

    }
    
    public function product()
    {
        $where = array();
        // if (!$this->isFounder(false)) {
        //     $where['role_id !='] = 1;
        // }
        $uid = intval($this->input->get('uid'));
        if ($uid <= 0) {
            $this->error('请选择用户！');
        }
        $res = $this->db
            ->select("*")
            ->from("user")
            ->where("uid", $uid)
            ->get();
        if (!empty($res->result_array())) {
            $odata = $res->result_array()[0];
        } else {
            $this->error('该角色不存在!');
        }
        if($odata['column']){
            $role_auth = explode(',', $odata['column']);
        }else{
           $role_auth = []; 
        }
      
        $this->assign('role_auth', $role_auth);
        
        $where['tid'] = 0;
        
        $res = $this->db
            ->select("nid,title")
            ->from("column")
            ->where($where)
            ->order_by("nid")
            ->get();
        
        $lst = $res->result_array();
        // echo "<pre>";
        // var_dump($lst);
        
        $this->assign('uid', $uid);
        $this->assign('lst', $lst);
        $this->display('admin/user/product.tpl');
    }
    
    public function product_update()
    {
       
        $data = array(
            'uid' => intval($this->input->post('uid')),
            'authaction' => $this->input->post('authaction'),
        );

        if ($data['uid'] <= 0) {
            $this->error('请选择用户！');
        }
        if (($_SESSION['user']['role_id'] != 1) && ($_SESSION['user']['role_id'] != 2)) {
            $this->error('您没权限修改产品分配权限！');
        }
        
        $res = $this->db
            ->select("*")
            ->from("user")
            ->where("uid", $data['uid'])
            ->get();
        if (!empty($res->result_array())) {
            $odata = $res->result_array()[0];
        } else {
            $this->error('该角色不存在!');
        }
        $udata = [];
        $udata['column'] = implode(',', $data['authaction']);
        $this->db
            ->where("uid", $data['uid'])
            ->update('user', $udata);
        $this->User_model->log("修改产品分配权限", $udata);
        $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
    }
    
   
}
