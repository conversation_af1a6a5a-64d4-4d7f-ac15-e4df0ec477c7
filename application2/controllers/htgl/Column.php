<?php
defined('BASEPATH') or exit('No direct script access allowed');
require_once "ini.php";

class Column extends PY_Controller
{
    public $tid_arr = array(0 => "类型", 1 => "规格", 2 => "店主");
    public $modeName = '参数';
    public $column_arr = array();
    public $c1_arr, $c2_arr, $c2_1_arr, $c3_arr;


    public function __construct()
    {
        parent::__construct();
        $this->assign('modeName', $this->modeName);

        $column_arr = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => MODULE), " oid ");

        $this->assign('column_arr', $column_arr);
        $this->assign('tid_arr', $this->tid_arr);

        $this->c1_arr = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => 0), " oid ");
        $this->assign('c1_arr', $this->c1_arr);
    }

    public function alist()
    {
        //类型
        header('Location: /index.php/htgl/column/list?stid=0');
    }

    public function blist()
    {
        //规格
        header('Location: /index.php/htgl/column/list?stid=1');
    }

    public function clist()
    {
        //客户
        header('Location: /index.php/htgl/column/list?stid=2');
    }

    function list()
    {

        //搜索条件
        $where = array("type" => MODULE);
        $s = array(
            'stid' => $this->input->get('stid'),
            'sgid' => intval($this->input->get('sgid')),
            'stitle' => $this->input->get('stitle'),
        );

        $s['stitle'] = isset($s['stitle']) ? replaysql(trim($s['stitle'])) : '';
        $s['stid'] = isset($s['stid']) ? intval($s['stid']) : -99;

        $this->assign('s', $s);

        if ($s['stid'] != -99) {
            $where['tid'] = $s['stid'];
        }
        if ($s['sgid'] > 0) {
            $where['oid'] = $s['sgid'];
        }
        if ($s['stitle']) {
            $where["title LIKE '%{$s['stitle']}%'"] = NULL;
        }
        //搜索条件结束

        $lst = $this->getPageLst("column", $where, " tid, oid ");

        $this->assignQX(array("add", "update", "delete"));

        $this->assign('lst', $lst);
        $this->display('admin/' . MODULE . '/list.tpl');
    }

    public function add()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $this->dosave("add");
        } else {
            $tid = $this->input->get('tid');
            $data = array();
            $data['tid'] = $tid;
            $this->assign('data', $data);
            $this->display('admin/' . MODULE . '/input.tpl');
        }
    }

    public function update()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $this->dosave("update");
        } else {
            $nid = intval($this->input->get('nid'));
            $odata = $this->ckeckbyodata("column", array('nid' => $nid));

            $this->assign('data', $odata);

            $this->display('admin/' . MODULE . '/input.tpl');
        }
    }

    public function delete()
    {
        $nid = intval($this->input->get('nid'));
        $odata = $this->ckeckbyodata("column", array('nid' => $nid), true);
        $this->db
            ->where("nid", $nid)
            ->delete('column');
        $this->User_model->log("删除" . $this->modeName . ":" . $nid, $odata);
        $this->ajaxSuccess();
    }

    private function dosave($act)
    {

        $data = array(
            'title' => $this->input->post('title'),
            'cid' => 0,
            'tid' => intval($this->input->post('tid')),
            'oid' => intval($this->input->post('oid')),
            'status' => intval($this->input->post('status')),
            'pic' => $this->input->post('pic'),
            'description' => floatval($this->input->post('description')),
            'content' => $this->input->post('content'),
            "type" => MODULE,
            "updatetime" => time(),
            "updater" => $_SESSION['user']['username'],
        );

        //数据校验
        if (empty($data['title']) || $data['title'] == "") {
            $this->error('标题不能为空！');
        }

        if ($act == "update") {
            $nid = intval($this->input->post('nid'));
            $odata = $this->ckeckbyodata("column", array('nid' => $nid));

            $this->db
                ->where("nid", $nid)
                ->update('column', $data);
            $this->User_model->log("修改" . $this->modeName . ":" . $nid, $data);
            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list?stid=" . $data['tid']);
        }

        if ($act == "add") {
            $data['createtime'] = $data['updatetime'];
            $data['creater'] = $data['updater'];
            $this->db->insert('column', $data);
            $nid = $this->db->insert_id();
            $this->User_model->log("添加" . $this->modeName, $data);
            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list?stid=" . $data['tid']);
        }

        $this->error('操作失败！');
    }

    public function config()
    {
        $action = $this->input->post('action');
        if ($action == "doconfig") {
            foreach ($this->input->post() as $k => $v) {
                $data = array(
                    'value' => $v,
                );
                $this->db
                    ->where("key", $k)
                    ->update('config', $data);
            }
            $this->User_model->log("修改面单参配置", $this->input->post());
            $this->success('操作成功！', URI);
        } else {
            $res = $this->db
                ->select("*")
                ->from("config")
                ->where("`key` in('conf_fsheete')")
                ->order_by("oid")
                ->get();
            $lst = $res->result_array();
            
            if (empty($lst)) {
                $data = array(
                    'key' => 'conf_fsheete',
                    'remark' => '面单高度(宽度固定600,请按比例调整高度)',
                    'value' => '900',
                );
                $this->db->insert('config', $data);
                $this->success('操作成功！', URI);
            }

            $this->assign('lst', $lst);
            $this->display('admin/system/config.tpl');
        }
    }
}
