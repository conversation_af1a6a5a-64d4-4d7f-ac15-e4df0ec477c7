<?php
defined('BASEPATH') or exit('No direct script access allowed');
require_once "ini.php";

class Sorder extends PY_Controller
{

    public $modeName = '订单';

    public $c1_arr, $c2_arr, $c2_1_arr, $c3_arr;

    public function __construct()
    {
        parent::__construct();
        $this->assign('modeName', $this->modeName);

        $this->assignQX(array("add", "update", "delete", "add2", "update2", "delete2", "dprint", "update3", "mprint"));

        $this->c1_arr = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => 0), " oid ");
        $this->assign('c1_arr', $this->c1_arr);

        $this->c2_arr = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => 1), " oid ");
        $this->assign('c2_arr', $this->c2_arr);

        $this->c2_1_arr = $this->getKeyVal("column", "`nid` as k,`description` as v", array("type" => "column", "tid" => 1), " oid ");
        $this->assign('c2_1_arr', $this->c2_1_arr);

        $res = $this->db
            ->select("oid,nid,title")
            ->from("column")
            ->where(array("type" => "column", "tid" => 1))
            ->order_by("oid,nid")
            ->get();

        $lst = $res->result_array();
        $rt = array();

        foreach ($lst as $uk => $uv) {
            if (empty($rt[$uv['oid']])) {
                $rt[$uv['oid']] = array();
            }
            $rt[$uv['oid']][$uv['nid']] = $uv['title'];
        }

        $this->assign('c2_json', json_encode($rt));

        $this->c3_arr = $this->getKeyVal("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => 2), " oid ");
        $this->assign('c3_arr', $this->c3_arr);

        //var_dump($this->config_arr);
    }

    public function mprint()
    {
        $nid = intval($this->input->get('nid'));
        if ($nid > 0) {
            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }
            $odata = $this->ckeckbyodata("sorder", $where);

            $wherepr = "";
            $sql = "SELECT *
            FROM pendy_sorder_products
            WHERE cid={$nid}  {$wherepr}
            order by  nid
            ";
            //echo $sql;
            $res = $this->db->query($sql);
            $odata['lst'] = $res->result_array();

            $this->assign('data', $odata);

            $data2 = array('a2' => $odata['a2'] + 1);
            $this->db
                ->where(array('nid' => $nid))
                ->update('sorder', $data2);

            $aconfig = $this->getKeyVal("config", "`key` as k,`value` as v", array(), " oid ");
            $this->assign('aconfig', $aconfig);

            $this->display('admin/' . MODULE . '/mprint.tpl');
        }

        die();
    }

    private function getlst($where, $wherepr)
    {
        $page = array(
            'count' => 0, // 总记录数，用于分页
            'all' => 0, // 总页数
            'current' => 1, // 当前页
            'size' => 100, // 每页显示条数
            'uri_string' => INDEX . $this->uri->uri_string(),
        );
        $c = $this->input->get('page');
        $page['current'] = (isset($c) && !empty($c) && (intval($c) > 0)) ? intval($c) : 1;

        $page['count'] = 0;
        $sql = "SELECT COUNT(DISTINCT s.nid) AS c
        FROM pendy_sorder s
        LEFT JOIN pendy_sorder_products p ON s.nid=p.cid
        WHERE 1=1 {$where}
        ORDER BY s.oid DESC,s.nid DESC";
        $res = $this->db->query($sql);
        if (!empty($res->result_array())) {
            $page['count'] = $res->result_array()[0]['c'];
        }

        $page['all'] = ceil($page['count'] / $page['size']);
        $this->assign('page', $page);

        $sql = "SELECT DISTINCT s.*
        FROM pendy_sorder s
        LEFT JOIN pendy_sorder_products p ON s.nid=p.cid
        WHERE 1=1 {$where}
        ORDER BY s.oid desc,s.nid  desc
        LIMIT " . ($page['current'] - 1) * $page['size'] . "," . $page['size'];

        //echo ($sql);
        //die();
        $res = $this->db->query($sql);
        $alst = $res->result_array();

        $lst = array();
        foreach ($alst as $k => $v) {
            $sql = "SELECT *
            FROM pendy_sorder_products
            WHERE cid={$v['nid']}  {$wherepr}
            order by  nid
            ";
            //echo $sql;
            $res = $this->db->query($sql);

            $blst = $res->result_array();
            $plst = array();
            foreach ($blst as $kk => $vv) {
                $vv['pm'] = $this->getPM($vv['nid'], $vv['cid']);
                $plst[] = $vv;
            }


            $v['plst'] = $plst;
            $lst[] = $v;
        }


        $this->assign('lst', $lst);
    }

    public function rlist()
    {
        //搜索条件
        $where = " AND s.`type` = '" . MODULE . "' ";
        $s = array(
            'stitle' => $this->input->get('stitle'),
            'sstatus' => $this->input->get('sstatus'),
            'scplx' => $this->input->get('scplx'),
            'sdddz' => $this->input->get('sdddz'),
            'sstamp' => $this->input->get('sstamp'),

            'stjsjs' => $this->input->get('stjsjs'),
            'stjsje' => $this->input->get('stjsje'),

            'sscsjs' => $this->input->get('sscsjs'),
            'sscsje' => $this->input->get('sscsje'),
        );


        $s['stitle'] = isset($s['stitle']) ? replaysql(trim($s['stitle'])) : '';
        $s['sstatus'] = isset($s['sstatus']) ? intval($s['sstatus']) : '-99';

        $s['scplx'] = isset($s['scplx']) ? intval($s['scplx']) : '-99';
        $s['sdddz'] = isset($s['sdddz']) ? intval($s['sdddz']) : '-99';
        $s['sstamp'] = isset($s['sstamp']) ? intval($s['sstamp']) : '-99';


        $s['pid'] = $this->usrpid;

        if (!empty($s['stitle'])) {
            $where .= " AND s.`title` LIKE '%{$s['stitle']}%' ";
        }

        if ($s['sstatus'] > -98) {
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
        }

        if ($s['sdddz'] > -98) {
            $where .= " AND s.`s1` = '{$s['sdddz']}' ";
        }

        #未绑邮票
        if ($s['sstamp'] == 1) {
            $where .= " AND s.`stamp` IS NULL ";
        }

        #已绑邮票
        if ($s['sstamp'] == 2) {
            $where .= " AND s.`stamp` != '' ";
        }


        $wherepr = "";
        if ($s['scplx'] > -98) {
            $wherepr = " AND cplx ='" . $s['scplx'] . "' ";
            $where .= " AND p.`cplx` = '{$s['scplx']}' ";
        }

        if ($s['stjsjs']) {
            $stjsjs = intval(strtotime($s['stjsjs'] . " 0:0:0"));
            if ($stjsjs > 0) {
                $where .= " AND s.`a1` >= {$stjsjs} ";
            }
        }

        if ($s['stjsje']) {
            $stjsje = intval(strtotime($s['stjsje'] . " 23:59:59"));
            if ($stjsje > 0) {
                $where .= " AND s.`a1` <= {$stjsje} ";
            }
        }

        if ($s['sscsjs']) {
            $sscsjs = intval(strtotime($s['sscsjs'] . " 0:0:0"));
            if ($sscsjs > 0) {
                $where .= " AND s.`ptime` >= {$sscsjs} ";
            }
        }

        if ($s['sscsje']) {
            $sscsje = intval(strtotime($s['sscsje'] . " 23:59:59"));
            if ($sscsje > 0) {
                $where .= " AND s.`ptime` <= {$sscsje} ";
            }
        }

        $where .= " AND s.`status` > 1 ";
        //搜索条件结束
        $this->assign('s', $s);
        $this->getlst($where, $wherepr);
        $this->display('admin/' . MODULE . '/rlist.tpl');
    }


    private function getPM($nid, $cid)
    {
        $sql = "SELECT nid
        FROM pendy_sorder_products
        WHERE cid={$cid}
        ORDER BY nid";
        $res = $this->db->query($sql);
        $lst = $res->result_array();
        $i = 1;
        $p = 0;
        foreach ($lst as $k => $v) {
            if ($v['nid'] == $nid) {
                $p = $i;
            }
            $i = $i + 1;
        }
        return $p . '/' . count($lst);
    }


    function list()
    {
        //搜索条件
        $where = " AND s.`type` = '" . MODULE . "' ";
        $s = array(
            'stitle' => $this->input->get('stitle'),
            'sstatus' => $this->input->get('sstatus'),
            'scplx' => $this->input->get('scplx'),
            'sdddz' => $this->input->get('sdddz'),
            'sstamp' => $this->input->get('sstamp'),

            'stjsjs' => $this->input->get('stjsjs'),
            'stjsje' => $this->input->get('stjsje'),

            'sscsjs' => $this->input->get('sscsjs'),
            'sscsje' => $this->input->get('sscsje'),
        );


        $s['stitle'] = isset($s['stitle']) ? replaysql(trim($s['stitle'])) : '';
        $s['sstatus'] = isset($s['sstatus']) ? intval($s['sstatus']) : '-99';

        $s['scplx'] = isset($s['scplx']) ? intval($s['scplx']) : '-99';
        $s['sdddz'] = isset($s['sdddz']) ? intval($s['sdddz']) : '-99';
        $s['sstamp'] = isset($s['sstamp']) ? intval($s['sstamp']) : '-99';


        $s['pid'] = $this->usrpid;

        if (!empty($s['stitle'])) {
            $where .= " AND s.`title` LIKE '%{$s['stitle']}%' ";
        }

        if ($s['sstatus'] > -98) {
            $where .= " AND s.`status` = '{$s['sstatus']}' ";
        }

        if ($s['sdddz'] > -98) {
            $where .= " AND s.`s1` = '{$s['sdddz']}' ";
        }

        #未绑邮票
        if ($s['sstamp'] == 1) {
            $where .= " AND s.`stamp` IS NULL ";
        }

        #已绑邮票
        if ($s['sstamp'] == 2) {
            $where .= " AND s.`stamp` != '' ";
        }


        $wherepr = "";
        if ($s['scplx'] > -98) {
            $wherepr = " AND cplx ='" . $s['scplx'] . "' ";
            $where .= " AND p.`cplx` = '{$s['scplx']}' ";
        }

        if ($s['stjsjs']) {
            $stjsjs = intval(strtotime($s['stjsjs'] . " 0:0:0"));
            if ($stjsjs > 0) {
                $where .= " AND s.`a1` >= {$stjsjs} ";
            }
        }

        if ($s['stjsje']) {
            $stjsje = intval(strtotime($s['stjsje'] . " 23:59:59"));
            if ($stjsje > 0) {
                $where .= " AND s.`a1` <= {$stjsje} ";
            }
        }

        if ($s['sscsjs']) {
            $sscsjs = intval(strtotime($s['sscsjs'] . " 0:0:0"));
            if ($sscsjs > 0) {
                $where .= " AND s.`ptime` >= {$sscsjs} ";
            }
        }

        if ($s['sscsje']) {
            $sscsje = intval(strtotime($s['sscsje'] . " 23:59:59"));
            if ($sscsje > 0) {
                $where .= " AND s.`ptime` <= {$sscsje} ";
            }
        }
        //搜索条件结束

        $this->assign('s', $s);
        $this->getlst($where, $wherepr);
        $this->display('admin/' . MODULE . '/list.tpl');
    }

    public function add()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data = array(
                'cid' => 0,
                'tid' => 0,
                'oid' => $this->input->post('oid'),
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'cont' => $this->input->post('cont'),
                's1' => intval($this->input->post('s1')),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );

            $data['createtime'] = $data['updatetime'];
            $data['creater'] = $data['updater'];
            $data['pid'] = intval($this->usrpid);

            //数据校验
            $data['oid'] = intval(strtotime($data['oid']));
            if ($data['oid'] <= 0) {
                $data['oid'] = time();
            }


            $this->db->insert('sorder', $data);
            $nid = $this->db->insert_id();
            $this->User_model->log("添加" . $this->modeName, $data);

            $data2 = array(
                'cid' => $nid,
                'pic' => $this->input->post('pic'),
                'cpkg' => intval($this->input->post('cpkg')),
                'cplx' => intval($this->input->post('cplx')),
                'num' => intval($this->input->post('num')),
                's1' => $this->input->post('ss1'),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );
            $data2['createtime'] = $data['updatetime'];
            $data2['creater'] = $data['updater'];
            $data2['pid'] = intval($this->usrpid);

            $this->db->insert('sorder_products', $data2);
            $this->User_model->log("添加产品", $data2);


            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");


            $this->error('操作失败！');
        } else {

            $this->display('admin/' . MODULE . '/input.tpl');
        }
    }

    public function update()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data = array(
                'cid' => 0,
                'tid' => 0,
                'oid' => $this->input->post('oid'),
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'cont' => $this->input->post('cont'),
                's1' => intval($this->input->post('s1')),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );


            //数据校验
            $data['oid'] = intval(strtotime($data['oid']));
            if ($data['oid'] <= 0) {
                $data['oid'] = time();
            }


            $nid = intval($this->input->post('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder", $where);

            $this->db
                ->where($where)
                ->update('sorder', $data);

            $this->User_model->log("修改" . $this->modeName . ":" . $nid, $data);

            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
        } else {
            $nid = intval($this->input->get('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            //var_dump($where);
            //die();

            $odata = $this->ckeckbyodata("sorder", $where);
            $odata['oid'] = date("Y-m-d H:i", $odata['oid']);

            $this->assign('data', $odata);
            $this->display('admin/' . MODULE . '/input.tpl');
        }
    }


    public function update3()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data = array(
                'stamp' => $this->input->post('stamp'),
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );


            //数据校验
            $nid = intval($this->input->post('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder", $where);

            $this->db
                ->where($where)
                ->update('sorder', $data);

            $this->User_model->log("更新邮票:" . $nid, $data);

            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
        } else {
            $nid = intval($this->input->get('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder", $where);
            $odata['oid'] = date("Y-m-d H:i", $odata['oid']);

            $this->assign('data', $odata);
            $this->display('admin/' . MODULE . '/input3.tpl');
        }
    }

    public function add2()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data2 = array(
                'pic' => $this->input->post('pic'),
                'cpkg' => intval($this->input->post('cpkg')),
                'cplx' => intval($this->input->post('cplx')),
                'num' => intval($this->input->post('num')),
                'cid' => intval($this->input->post('cid')),
                's1' => $this->input->post('ss1'),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );

            $data2['createtime'] = $data2['updatetime'];
            $data2['creater'] = $data2['updater'];
            $data2['pid'] = intval($this->usrpid);

            //数据校验
            if ($data2['cid'] <= 0) {
                $this->error('订单错误!');
            }


            $this->db->insert('sorder_products', $data2);
            $this->User_model->log("添加产品", $data2);


            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");


            $this->error('操作失败！');
        } else {
            $cid = $this->input->get('cid');
            $odata = array('cid' => $cid);
            $this->assign('data', $odata);
            $this->display('admin/' . MODULE . '/input2.tpl');
        }
    }


    public function update2()
    {
        $action = $this->input->post('action');
        if ($action == "dosave") {
            $data2 = array(
                'pic' => $this->input->post('pic'),
                'cpkg' => intval($this->input->post('cpkg')),
                'cplx' => intval($this->input->post('cplx')),
                'num' => intval($this->input->post('num')),
                's1' => $this->input->post('ss1'),
                "type" => MODULE,
                "updatetime" => time(),
                "updater" => $_SESSION['user']['username'],
            );


            //数据校验
            $nid = intval($this->input->post('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder_products", $where);

            $this->db
                ->where($where)
                ->update('sorder_products', $data2);

            $this->User_model->log("修改产品:" . $nid, $data2);

            $this->success('操作成功！', RELROUTE . "/" . MODULE . "/list");
        } else {
            $nid = intval($this->input->get('nid'));

            $where = array("nid" => $nid);
            if ($this->usrpid > 0) {
                $where['pid'] = $this->usrpid;
            }

            $odata = $this->ckeckbyodata("sorder_products", $where);
            //$odata['oid'] = date("Y-m-d H:i", $odata['oid']);

            $this->assign('data', $odata);
            $this->display('admin/' . MODULE . '/input2.tpl');
        }
    }



    public function delete()
    {
        $nid = intval($this->input->get('nid'));

        $where = array("nid" => $nid);
        if ($this->usrpid > 0) {
            $where['pid'] = $this->usrpid;
        }

        $odata = $this->ckeckbyodata("sorder", $where, true);

        $this->db
            ->where($where)
            ->delete('sorder');

        $where = array("cid" => $nid);
        $this->db
            ->where($where)
            ->delete('sorder_products');

        $this->User_model->log("删除" . $this->modeName . ":" . $nid, $odata);
        $this->ajaxSuccess();
    }

    public function delete2()
    {
        $nid = intval($this->input->get('nid'));

        $where = array("nid" => $nid);
        if ($this->usrpid > 0) {
            $where['pid'] = $this->usrpid;
        }

        $odata = $this->ckeckbyodata("sorder_products", $where, true);

        $this->db
            ->where($where)
            ->delete('sorder_products');


        $this->User_model->log("删除产品:" . $nid, $odata);
        $this->ajaxSuccess();
    }



    #设为已生产
    public function dprint()
    {
        $nid = intval($this->input->get('nid'));
        $t = intval($this->input->get('t'));

        $where = array("nid" => $nid);
        if ($this->usrpid > 0) {
            $where['pid'] = $this->usrpid;
        }

        $odata = $this->ckeckbyodata("sorder_products", $where, true);

        if ($t == 3) {

            $this->db
                ->where($where)
                ->update('sorder_products', array("status" => 3, "ptime" => time()));

            $this->User_model->log("已打印产品:" . $nid, $odata);

            //改变订单状态
            $sql = "SELECT nid
            FROM pendy_sorder_products
            WHERE cid={$odata['cid']} AND `status`!=3
            order by  nid
            ";
            //echo $sql;
            $res = $this->db->query($sql);
            $blst = $res->result_array();
            if (empty($blst)) {
                $where2 = array("nid" => $odata['cid']);
                if ($this->usrpid > 0) {
                    $where2['pid'] = $this->usrpid;
                }
                $this->db
                    ->where($where2)
                    ->update('sorder', array("status" => $t, "ptime" => time()));

                $this->User_model->log("打印完成订单:" . $odata['cid']);
            }
        }

        if ($t == -1) {
            $where2 = array("nid" => $odata['cid']);
            $this->db
                ->where($where2)
                ->update('sorder', array("status" => 0, "ptime" => 0));

            $where2 = array("cid" => $odata['cid']);
            $this->db
                ->where($where2)
                ->update('sorder_products', array("status" => 0, "ptime" => 0));
        }

        $this->ajaxSuccess();
    }

    #订单提交生产
    public function doprod()
    {
        $nid = intval($this->input->get('nid'));

        $where = array("nid" => $nid);
        if ($this->usrpid > 0) {
            $where['pid'] = $this->usrpid;
        }

        $odata = $this->ckeckbyodata("sorder", $where, true);

        $this->db
            ->where($where)
            ->update('sorder', array("status" => 2, "a1" => time()));

        $where2 = array("cid" => $nid);
        if ($this->usrpid > 0) {
            $where2['pid'] = $this->usrpid;
        }

        $this->db
            ->where($where2)
            ->update('sorder_products', array("status" => 2, "a1" => time()));
        $this->ajaxSuccess();
    }
}
