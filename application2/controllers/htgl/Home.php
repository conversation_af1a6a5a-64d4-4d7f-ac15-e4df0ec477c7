<?php
defined('BASEPATH') or exit('No direct script access allowed');
require_once "ini.php";

class Home extends PY_Controller
{
    public function index()
    {
        $menu = $this->User_model->menu_list();
        $this->assign('menu', $menu);
        $this->assign('PushMenu', @$_COOKIE["PushMenu"]);
        $this->display('admin/home/<USER>');
    }

    public function main()
    {

        //////////////删除缓存/////////////////////
        $env = $this->config->item('env');
        $filedele = $env['filedele'];
        $directory = FCPATH . '/data/uploads'; // 替换为你的目录路径

        // 获取指定目录下的所有项（包括文件和子目录）
        $entries = scandir($directory);
        // 过滤出子目录
        $directories = array_filter($entries, function ($entry) use ($directory) {
            return is_dir("$directory/$entry") && $entry != '.' && $entry != '..';
        });
        $ntmie = time();
        $deletea = array();
        foreach ($directories as $k => $v) {
            $st = str_replace('_', "-", $v);
            $timestamp = strtotime($st);
            //86400
            if ($ntmie - $timestamp > 86400 * $filedele) {
                //echo ($v);
                //echo ('<br/>');
                $this->deleteDir($directory . '/' . $v);
                $deletea[] = $v;
            }
        }

        if (!empty($deletea)) {
            $this->User_model->log("自动删除过期文件", $deletea);
        }
        //var_dump($directories);
        //die();
        ///////////////////////////////////////////
        $data['server_info'] = PHP_OS . ' / PHP v' . PHP_VERSION;
        $data['server_info'] .= @ini_get('safe_mode') ? ' Safe Mode' : null;

        $data['server_soft'] = $_SERVER['SERVER_SOFTWARE'];
        $data['file_upload'] = @ini_get('file_uploads') ? ini_get('upload_max_filesize') : 0;

        $data['server_time'] = date(" Y-m-d H:i", time());

        $this->assign('data', $data);
        $this->display('admin/home/<USER>');
    }

    private function deleteDir($dirPath)
    {
        if (!is_dir($dirPath)) {
            return;
        }

        $files = glob($dirPath . '/*');
        foreach ($files as $file) {
            is_dir($file) ? $this->deleteDir($file) : unlink($file);
        }

        rmdir($dirPath);
    }

    private function getChidids($cid)
    {
        $sql = " select nid,cid from " . $this->db->dbprefix("column") . "
        where cid={$cid} ";

        $res = $this->db->query($sql);
        $lst = $res->result_array();

        $ids = "0";
        foreach ($lst as $k => $v) {
            $ids = $ids . "," . $v['nid'];
            $ids = $ids . "," . $this->getChidids($v['nid']);
        }

        return $ids;
    }

    public function self()
    {

        $action = $this->input->post('action');
        if ($action == "doself") {
            $data = array(
                'email' => $this->input->post('email'),
                'nickname' => $this->input->post('nickname'),
                'opassword' => $this->input->post('opassword'),
                'password' => $this->input->post('password'),
                'apassword' => $this->input->post('apassword'),
            );

            //判断密码
            if ((!empty($data['password']) || !empty($data['apassword'])) && empty($data['opassword'])) {
                $this->error('旧密码错误！');
            }

            $user_info = $this->User_model->get_by_uid(@$_SESSION['user']['uid'], true);
            if (empty($user_info) || !isset($user_info['uid']) || intval($user_info['uid']) <= 0) {
                $this->error("参数错误");
            }
            $uid = intval($user_info['uid']);

            $data['role_id'] = intval($user_info['role_id']);
            $data['part_id'] = intval($user_info['part_id']);

            //密码校验
            if (!empty($data['password']) || !empty($data['apassword'])) {
                if ($data['password'] != $data['apassword']) {
                    $this->error('两次输入的密码不一致！');
                }

                if ($data['password'] == $data['opassword']) {
                    $this->error('新旧密码不能输入一样！');
                }

                if (!$this->User_model->check_password($data['password'])) {
                    $this->error('密码不能小于6个字符和大于15个字符！');
                }

                if (!$this->User_model->verifyPassw($data['opassword'], $user_info['salt'], $user_info['password'])) {
                    $this->error('旧密码错误！');
                }

                $data['salt'] = $this->User_model->salt();
            }

            //检验邮箱
            if (!$this->User_model->check_email($data['email'], $uid)) {
                $this->error('邮箱错误或已被其他用户注册！');
            }

            //var_dump($data);
            if ($this->User_model->updateUserSelf($data, $uid)) {
                $data['password'] = !empty($data['password']) ? '***' : '';
                $data['opassword'] = !empty($data['opassword']) ? '***' : '';
                $data['apassword'] = !empty($data['apassword']) ? '***' : '';
                $data['salt'] = !empty($data['salt']) ? '***' : '';

                $this->User_model->log("修改个人资料", $data);
                $this->success('操作成功！', URI);
            } else {
                $this->error('操作失败！');
            }
        } else {
            $this->assign('user_info', $this->User_model->get_by_uid(@$_SESSION['user']['uid']));
            $this->display('admin/home/<USER>');
        }
    }

    public function getjson()
    {
        $a = $this->input->get('a');
        $id = $this->input->get('id');
        if ($a == "column") {
            $column_arr = $this->getKeyVals("column", "`nid` as k,`title` as v", array("type" => $a, "tid" => $id, "status" => 1), " oid ");
            array_unshift($column_arr, array("k" => 0, "v" => "===顶级==="));
            echo json_encode($column_arr);
        }
    }
}
