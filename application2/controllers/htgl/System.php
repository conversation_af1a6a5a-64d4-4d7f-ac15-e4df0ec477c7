<?php
defined('BASEPATH') or exit('No direct script access allowed');
require_once "ini.php";

class System extends PY_Controller
{
    public function index()
    {
    }

    public function config()
    {
        $action = $this->input->post('action');
        if ($action == "doconfig") {
            foreach ($this->input->post() as $k => $v) {
                $data = array(
                    'value' => $v,
                );
                $this->db
                    ->where("key", $k)
                    ->update('config', $data);
            }
            $this->User_model->log("修改网站配置", $this->input->post());
            $this->success('操作成功！', URI);
        } else {
            $res = $this->db
                ->select("*")
                ->from("config")
                ->order_by("oid")
                ->get();
            $lst = $res->result_array();
            $this->assign('lst', $lst);
            $this->display('admin/system/config.tpl');
        }
    }

    public function log_action()
    {
        $this->assignQX(array("add", "update", "delete"));
        $action = $this->input->post('action');
        if ($action == "dodelete") {
            // 只允许创始人删除管理日志
            $this->isFounder(true);

            $ids = $this->input->post("ids");

            // 删除管理日志
            if (!isset($ids) || empty($ids) || $ids == "") {
                $this->error('删除管理日志操作无效！');
            }

            $this->db
                ->where_in('log_id', $ids)
                ->delete('user_admin_log');

            $this->User_model->log("删除管理日志", $ids);
            $this->success('操作成功！', URI);

        } else {
            $lst = $this->getPageLst("user_admin_log", array(), " log_id desc ");
            $this->assign('lst', $lst);
            $this->display('admin/system/log_action.tpl');
        }
    }

    //文件比较
    public function compfile()
    {
        $act = $this->input->get('act');
        $this->load->library('CCompfile');
        if ($act == 'file') {
            $this->isFounder(true);
            header("Content-Type: text/html;charset=utf-8");
            $this->ccompfile->getMd5text($this->config->item('authkey'));
            $this->User_model->log("下载文件md5", "");
            die();
        } else {
            $this->assignQX(array("add", "update", "delete"));

            $t = $this->ccompfile->start($this->config->item('md5url'), $this->config->item('authkey'));
            $this->assign('t', $t);
            $this->display('admin/system/compfile.tpl');
        }

    }
}
