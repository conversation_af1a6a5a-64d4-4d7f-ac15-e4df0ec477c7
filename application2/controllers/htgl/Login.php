<?php
defined('BASEPATH') or exit('No direct script access allowed');
require_once "ini.php";

class Login extends PY_Controller
{
    public function index()
    {
        $this->display('admin/login.tpl');
    }

    public function suplogin()
    {
        $this->index();
    }

    //执行登录
    public function clogin()
    {
        //echo('cklogin');
        $username = $this->input->post("username");
        $password = $this->input->post("password");
        $idcode = $this->input->post("idcode");
        $captcha = $this->input->post("captcha");

        if (empty($username)) {
            $this->error(sprintf($this->lang->line('msg_f_isneed'), $this->lang->line('msg_p_username')), RELROUTE . '/login');
        } elseif (empty($password)) {
            $this->error(sprintf($this->lang->line('msg_f_isneed'), $this->lang->line('msg_p_password')), RELROUTE . '/login');
        }

        /***
        if (ENVIRONMENT != 'development') {
        if (empty($captcha) || empty($_SESSION['captcha']) || strtolower($captcha) != strtolower($_SESSION['captcha'])) {
        $_SESSION['captcha'] = null;
        $this->error($this->lang->line('msg_login_error_captcha'));
        }
        }
         */

        $result = $this->User_model->login($username, $password, $idcode);
        switch ($result) {
            case 1:
                $this->User_model->log("登陆成功");
                exit('<script language="javascript">top.location.href="' . RELROUTE . '/home"</script>');
                //header('Location: '.RELROUTE.'/home');
                die();
                break;
            case -1:
                $this->error($this->lang->line('msg_login_error_password'));
                break;
            case -2:
                $this->error($this->lang->line('msg_login_error_unamepassword'));
                break;
            case -3:
                $this->error($this->lang->line('msg_login_error_15min'));
                break;
            case -4:
                $this->error($this->lang->line('msg_login_error_idcode'));
                break;
            case -5:
                $this->error($this->lang->line('msg_login_error_noaccess'));
                break;
        }

        header('Location: ' . RELROUTE . '/login');
        die();
    }

    public function logout()
    {
        $this->User_model->logout();
        exit('<script language="javascript">top.location.href="' . RELROUTE . '/login"</script>');
        die();
    }

    //验证码
    public function getcodes()
    {
        $this->load->library('captcha');
        $code = $this->captcha->getCaptcha();
        $this->session->set_userdata('code', $code);
        $this->captcha->showImg();

    }
}
