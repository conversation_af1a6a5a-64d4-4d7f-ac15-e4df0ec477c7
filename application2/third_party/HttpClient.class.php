<?php
defined('BASEPATH') or exit('No direct script access allowed');

class HttpClient
{
    private static $_instance = null;
    private $_msg = null;
    /**
     * 单例创建http类
     * @return \Tools\Http
     */
    public static function getInstance()
    {
        if (!self::$_instance instanceof self) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }
    /**
     * 禁止使用new创建类
     */
    private function __construct()
    {}

    /**
     * 发送http请求，并获取返回结果
     * @param unknown $url
     * @param unknown $getArray
     * @param unknown $postArray
     * @return mixed
     */
    public function getHttpResult($url, $getArray = array(), $postArray = array())
    {
        return $this->getHttpRequest($url, $getArray, $postArray, false);
    }

    /**
     * 发送https请求，并获取返回结果
     * @param unknown $url
     * @param unknown $getArray
     * @param unknown $postArray
     * @return mixed
     */
    public function getHttpsResult($url, $getArray = array(), $postArray = array())
    {
        return $this->getHttpRequest($url, $getArray, $postArray, true);
    }

    /**
     * 使用curl发送http和https请求
     * @param unknown $url
     * @param unknown $getArray
     * @param unknown $postArray
     * @param string $isHttps
     * @return mixed
     */
    private function getHttpRequest($url, $getArray = array(), $postArray = array(), $isHttps = false)
    {
        $ch = curl_init();
        $options = array(
            CURLOPT_RETURNTRANSFER => true, // return web page
            CURLOPT_HEADER => false, // don't return headers
            CURLOPT_FOLLOWLOCATION => true, // follow redirects
            CURLOPT_ENCODING => "", // handle all encodings
            CURLOPT_USERAGENT => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.115 Safari/537.36", // who am i
            CURLOPT_AUTOREFERER => true, // set referer on redirect
            CURLOPT_CONNECTTIMEOUT => 30, // timeout on connect
            CURLOPT_TIMEOUT => 30, // timeout on response
            CURLOPT_MAXREDIRS => 2, // stop after 10 redirects
            CURLOPT_VERBOSE => 1, //
        );

        if (!empty($postArray)) {
            $options[CURLOPT_POST] = 1; // i am sending post data
            $options[CURLOPT_POSTFIELDS] = $this->getUrlStr($postArray);
        }
        if (!empty($getArray)) {
            $url .= $this->getUrlStr($getArray);
        }
        if ($isHttps) {
            $options[CURLOPT_SSL_VERIFYHOST] = 0; // don't verify ssl
            $options[CURLOPT_SSL_VERIFYPEER] = false; //
        }
        $options[CURLOPT_URL] = $url;
        curl_setopt_array($ch, $options);
        $content = curl_exec($ch);
        $this->_msg = null;
        if ($content === false) {
            $this->_msg = array(
                'errno' => curl_errno($ch),
                'error' => curl_error($ch),
                'getinfo' => curl_getinfo($ch),
            );
        }
        curl_close($ch);
        return $content;
    }
    /**
     * 根据数组key和value组成url参数
     * @param array $dataArray
     * @return string
     */
    private function getUrlStr($dataArray)
    {
        $postdata = '';
        if (!empty($dataArray)) {
            foreach ($dataArray as $key => $val) {
                $postdata .= '&' . $key . '=' . urlencode($val);
            }
        }
        return $postdata;
    }

    /**
     * 获取失败原因
     * @return array
     */
    public function getLastError()
    {
        return $this->_msg;
    }

    public function getHttpsRequest2($url, $postArray)
    {
        $ch = curl_init();
        $options = array(
            CURLOPT_RETURNTRANSFER => true, // return web page
            CURLOPT_HEADER => false, // don't return headers
            CURLOPT_FOLLOWLOCATION => true, // follow redirects
            CURLOPT_ENCODING => "", // handle all encodings
            CURLOPT_USERAGENT => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.115 Safari/537.36", // who am i
            CURLOPT_AUTOREFERER => true, // set referer on redirect
            CURLOPT_CONNECTTIMEOUT => 30, // timeout on connect
            CURLOPT_TIMEOUT => 30, // timeout on response
            CURLOPT_MAXREDIRS => 2, // stop after 10 redirects
            CURLOPT_VERBOSE => 1, //
        );

        if (!empty($postArray)) {
            $options[CURLOPT_POST] = 1; // i am sending post data
            $options[CURLOPT_POSTFIELDS] = $postArray;
        }

        $options[CURLOPT_SSL_VERIFYHOST] = 0; // don't verify ssl
        $options[CURLOPT_SSL_VERIFYPEER] = false; //

        $options[CURLOPT_URL] = $url;
        curl_setopt_array($ch, $options);
        $content = curl_exec($ch);
        $this->_msg = null;
        if ($content === false) {
            $this->_msg = array(
                'errno' => curl_errno($ch),
                'error' => curl_error($ch),
                'getinfo' => curl_getinfo($ch),
            );
        }
        curl_close($ch);
        return $content;
    }
}
