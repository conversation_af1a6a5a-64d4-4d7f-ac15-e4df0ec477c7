<?php

class TP_yyStackEntry
{
    public $stateno;       /* The state-number */
    public $major;         /* The major token value.  This is the code
                            * number for the token at this stack level
                            */
    public $minor;          /* The user-supplied minor token value.  This
                            * is the value of the token
                            */
}

// line 11 "../smarty/lexer/smarty_internal_templateparser.y"

/**
 * Smarty Template Parser Class
 *
 * This is the template parser.
 * It is generated from the smarty_internal_templateparser.y file
 *
 * <AUTHOR> Tews <<EMAIL>>
 */
class Smarty_Internal_Templateparser
{
    // line 23 "../smarty/lexer/smarty_internal_templateparser.y"
    const ERR1                      = 'Security error: Call to private object member not allowed';
    const ERR2                      = 'Security error: Call to dynamic object member not allowed';
    const ERR3                      = 'PHP in template not allowed. Use SmartyBC to enable it';
    const TP_VERT                   = 1;
    const TP_COLON                  = 2;
    const TP_UNIMATH                = 3;
    const TP_PHP                    = 4;
    const TP_TEXT                   = 5;
    const TP_STRIPON                = 6;
    const TP_STRIPOFF               = 7;
    const TP_LITERALSTART           = 8;
    const TP_LITERALEND             = 9;
    const TP_LITERAL                = 10;
    const TP_SIMPELOUTPUT           = 11;
    const TP_SIMPLETAG              = 12;
    const TP_SMARTYBLOCKCHILDPARENT = 13;
    const TP_LDEL                   = 14;
    const TP_RDEL                   = 15;
    const TP_DOLLARID               = 16;
    const TP_EQUAL                  = 17;
    const TP_ID                     = 18;
    const TP_PTR                    = 19;
    const TP_LDELMAKENOCACHE        = 20;
    const TP_LDELIF                 = 21;
    const TP_LDELFOR                = 22;
    const TP_SEMICOLON              = 23;
    const TP_INCDEC                 = 24;
    const TP_TO                     = 25;
    const TP_STEP                   = 26;
    const TP_LDELFOREACH            = 27;
    const TP_SPACE                  = 28;
    const TP_AS                     = 29;
    const TP_APTR                   = 30;
    const TP_LDELSETFILTER          = 31;
    const TP_CLOSETAG               = 32;
    const TP_LDELSLASH              = 33;
    const TP_ATTR                   = 34;
    const TP_INTEGER                = 35;
    const TP_COMMA                  = 36;
    const TP_OPENP                  = 37;
    const TP_CLOSEP                 = 38;
    const TP_MATH                   = 39;
    const TP_ISIN                   = 40;
    const TP_QMARK                  = 41;
    const TP_NOT                    = 42;
    const TP_TYPECAST               = 43;
    const TP_HEX                    = 44;
    const TP_DOT                    = 45;
    const TP_INSTANCEOF             = 46;
    const TP_SINGLEQUOTESTRING      = 47;
    const TP_DOUBLECOLON            = 48;
    const TP_NAMESPACE              = 49;
    const TP_AT                     = 50;
    const TP_HATCH                  = 51;
    const TP_OPENB                  = 52;
    const TP_CLOSEB                 = 53;
    const TP_DOLLAR                 = 54;
    const TP_LOGOP                  = 55;
    const TP_SLOGOP                 = 56;
    const TP_TLOGOP                 = 57;
    const TP_SINGLECOND             = 58;
    const TP_QUOTE                  = 59;
    const TP_BACKTICK               = 60;
    const YY_NO_ACTION              = 511;
    const YY_ACCEPT_ACTION          = 510;
    const YY_ERROR_ACTION           = 509;
    const YY_SZ_ACTTAB              = 2076;
    const YY_SHIFT_USE_DFLT         = -23;
    const YY_SHIFT_MAX              = 227;
    const YY_REDUCE_USE_DFLT        = -68;
    const YY_REDUCE_MAX             = 176;
    const YYNOCODE                  = 108;
    const YYSTACKDEPTH              = 500;
    const YYNSTATE                  = 323;
    const YYNRULE                   = 186;
    const YYERRORSYMBOL             = 61;
    const YYERRSYMDT                = 'yy0';
    const YYFALLBACK                = 0;

    public static $yy_action        = array(
        42, 266, 267, 379, 115, 202, 27, 204, 260, 235,
        237, 1, 17, 125, 94, 182, 379, 215, 10, 79,
        317, 168, 379, 12, 107, 425, 308, 318, 224, 298,
        218, 129, 189, 292, 21, 203, 425, 27, 11, 39,
        38, 299, 219, 17, 213, 385, 191, 245, 77, 3,
        303, 315, 42, 385, 160, 385, 75, 29, 385, 95,
        260, 235, 237, 1, 385, 126, 385, 193, 385, 215,
        10, 79, 80, 290, 145, 226, 107, 148, 172, 150,
        224, 298, 218, 85, 217, 315, 21, 280, 101, 280,
        141, 39, 38, 299, 219, 20, 287, 183, 191, 232,
        77, 3, 42, 315, 16, 176, 316, 172, 75, 275,
        260, 235, 237, 1, 167, 128, 236, 193, 319, 215,
        10, 79, 345, 40, 14, 257, 107, 319, 345, 5,
        224, 298, 218, 89, 217, 315, 30, 292, 172, 203,
        74, 39, 38, 299, 219, 132, 287, 205, 191, 74,
        77, 3, 42, 315, 210, 194, 310, 99, 75, 345,
        260, 235, 237, 1, 425, 126, 87, 179, 319, 215,
        10, 79, 345, 95, 195, 425, 107, 272, 345, 176,
        224, 298, 218, 315, 199, 115, 21, 128, 278, 209,
        74, 39, 38, 299, 219, 94, 287, 226, 191, 129,
        77, 3, 42, 315, 277, 309, 11, 308, 75, 13,
        260, 235, 237, 1, 163, 127, 425, 193, 319, 215,
        10, 79, 77, 254, 19, 315, 107, 425, 137, 34,
        224, 298, 218, 196, 217, 33, 21, 220, 280, 159,
        74, 39, 38, 299, 219, 196, 287, 8, 191, 162,
        77, 3, 42, 315, 294, 222, 196, 438, 75, 378,
        260, 235, 237, 1, 438, 126, 16, 193, 271, 215,
        10, 79, 378, 172, 302, 315, 107, 175, 378, 267,
        224, 298, 218, 27, 178, 252, 21, 164, 296, 17,
        83, 39, 38, 299, 219, 196, 287, 205, 191, 170,
        77, 3, 42, 315, 270, 18, 144, 99, 75, 346,
        260, 235, 237, 1, 142, 126, 280, 177, 84, 215,
        10, 79, 346, 172, 280, 4, 107, 95, 346, 321,
        224, 298, 218, 438, 217, 131, 21, 321, 426, 24,
        438, 39, 38, 299, 219, 196, 287, 205, 191, 426,
        77, 3, 42, 315, 201, 9, 101, 99, 75, 381,
        260, 235, 237, 1, 149, 124, 102, 193, 22, 215,
        10, 79, 381, 315, 99, 231, 107, 311, 381, 425,
        224, 298, 218, 23, 217, 319, 7, 207, 196, 17,
        425, 39, 38, 299, 219, 307, 287, 36, 191, 154,
        77, 3, 42, 315, 161, 296, 227, 74, 75, 280,
        260, 235, 237, 1, 16, 91, 273, 76, 312, 215,
        10, 79, 317, 208, 190, 12, 107, 176, 196, 318,
        224, 298, 218, 135, 217, 321, 21, 196, 35, 95,
        263, 39, 38, 299, 219, 157, 287, 111, 191, 88,
        77, 3, 42, 315, 169, 280, 225, 15, 75, 285,
        260, 235, 237, 1, 155, 126, 226, 184, 101, 215,
        10, 79, 454, 172, 280, 454, 107, 246, 253, 454,
        224, 298, 218, 152, 217, 111, 21, 161, 296, 265,
        6, 39, 38, 299, 219, 269, 287, 203, 191, 119,
        77, 3, 42, 315, 158, 262, 321, 274, 75, 97,
        260, 235, 237, 1, 153, 128, 165, 193, 151, 215,
        10, 79, 317, 43, 280, 12, 107, 320, 280, 318,
        224, 298, 218, 8, 217, 171, 30, 306, 196, 36,
        172, 39, 38, 299, 219, 264, 287, 256, 191, 128,
        77, 288, 78, 315, 510, 90, 166, 296, 75, 41,
        37, 223, 104, 228, 250, 251, 255, 122, 226, 289,
        260, 235, 237, 1, 239, 233, 238, 240, 241, 215,
        10, 79, 229, 305, 77, 304, 107, 315, 281, 300,
        224, 298, 218, 261, 211, 203, 314, 28, 86, 108,
        140, 181, 96, 61, 214, 247, 317, 454, 94, 12,
        454, 297, 322, 318, 454, 29, 259, 192, 249, 248,
        308, 313, 138, 27, 302, 143, 130, 82, 95, 17,
        261, 211, 203, 314, 252, 86, 108, 286, 180, 96,
        50, 136, 139, 100, 152, 94, 454, 81, 297, 322,
        295, 321, 146, 259, 192, 249, 295, 308, 261, 295,
        203, 295, 295, 110, 295, 295, 197, 105, 64, 295,
        295, 295, 295, 94, 295, 295, 297, 322, 295, 295,
        295, 259, 192, 249, 261, 308, 203, 276, 295, 110,
        108, 295, 181, 96, 61, 187, 282, 295, 317, 94,
        295, 12, 297, 322, 295, 318, 295, 259, 192, 249,
        295, 308, 295, 291, 295, 295, 295, 295, 295, 260,
        235, 237, 2, 295, 293, 295, 295, 295, 215, 10,
        79, 295, 295, 295, 295, 107, 291, 206, 295, 224,
        298, 218, 260, 235, 237, 2, 295, 293, 295, 295,
        295, 215, 10, 79, 295, 295, 295, 295, 107, 295,
        295, 295, 224, 298, 218, 295, 295, 295, 26, 261,
        295, 203, 295, 295, 110, 295, 295, 197, 113, 60,
        295, 295, 295, 295, 94, 156, 295, 297, 322, 167,
        284, 26, 259, 192, 249, 280, 308, 295, 40, 14,
        257, 295, 261, 200, 203, 295, 295, 110, 295, 295,
        197, 105, 64, 172, 295, 295, 295, 94, 295, 295,
        297, 322, 295, 295, 295, 259, 192, 249, 295, 308,
        295, 295, 295, 295, 261, 295, 203, 295, 295, 98,
        283, 295, 197, 113, 51, 295, 201, 295, 295, 94,
        295, 295, 297, 322, 295, 295, 295, 259, 192, 249,
        261, 308, 203, 295, 295, 110, 295, 295, 197, 113,
        60, 295, 295, 295, 295, 94, 295, 295, 297, 322,
        295, 295, 295, 259, 192, 249, 295, 308, 261, 295,
        203, 295, 295, 110, 188, 295, 197, 113, 60, 196,
        31, 43, 295, 94, 295, 295, 297, 322, 295, 295,
        295, 259, 192, 249, 295, 308, 261, 295, 203, 295,
        295, 98, 198, 295, 197, 113, 45, 295, 109, 295,
        295, 94, 295, 295, 297, 322, 295, 41, 37, 259,
        192, 249, 261, 308, 203, 295, 295, 110, 295, 295,
        197, 113, 67, 233, 238, 240, 241, 94, 295, 295,
        297, 322, 295, 295, 295, 259, 192, 249, 295, 308,
        261, 295, 203, 295, 295, 110, 295, 295, 197, 113,
        57, 196, 295, 43, 295, 94, 295, 295, 297, 322,
        295, 295, 295, 259, 192, 249, 295, 308, 261, 295,
        203, 295, 295, 110, 295, 295, 197, 113, 46, 295,
        295, 295, 295, 94, 295, 295, 297, 322, 295, 41,
        37, 259, 192, 249, 261, 308, 203, 295, 295, 110,
        295, 295, 197, 113, 66, 233, 238, 240, 241, 94,
        301, 295, 297, 322, 295, 295, 295, 259, 192, 249,
        295, 308, 261, 295, 203, 295, 295, 110, 295, 295,
        197, 113, 72, 196, 295, 43, 295, 94, 295, 295,
        297, 322, 295, 295, 295, 259, 192, 249, 295, 308,
        261, 295, 203, 295, 295, 110, 295, 295, 197, 113,
        53, 295, 295, 295, 295, 94, 295, 295, 297, 322,
        230, 41, 37, 259, 192, 249, 261, 308, 203, 295,
        295, 110, 295, 295, 197, 113, 48, 233, 238, 240,
        241, 94, 295, 295, 297, 322, 295, 295, 295, 259,
        192, 249, 295, 308, 261, 295, 203, 295, 295, 110,
        295, 295, 185, 103, 49, 196, 295, 43, 295, 94,
        295, 295, 297, 322, 295, 295, 295, 259, 192, 249,
        295, 308, 261, 295, 203, 295, 295, 110, 295, 295,
        197, 113, 55, 134, 295, 295, 295, 94, 295, 295,
        297, 322, 295, 41, 37, 259, 192, 249, 261, 308,
        203, 295, 295, 110, 295, 295, 197, 113, 71, 233,
        238, 240, 241, 94, 295, 295, 297, 322, 295, 295,
        295, 259, 192, 249, 295, 308, 261, 295, 203, 295,
        295, 110, 295, 295, 197, 113, 59, 196, 295, 43,
        295, 94, 295, 295, 297, 322, 295, 295, 295, 259,
        192, 249, 295, 308, 261, 295, 203, 295, 295, 110,
        295, 295, 197, 113, 63, 295, 295, 295, 295, 94,
        295, 295, 297, 322, 216, 41, 37, 259, 192, 249,
        261, 308, 203, 295, 295, 110, 295, 295, 197, 113,
        62, 233, 238, 240, 241, 94, 295, 295, 297, 322,
        295, 295, 295, 259, 192, 249, 295, 308, 261, 295,
        203, 295, 295, 110, 295, 295, 197, 92, 69, 196,
        295, 43, 295, 94, 295, 295, 297, 322, 295, 295,
        295, 259, 192, 249, 295, 308, 261, 295, 203, 295,
        295, 110, 295, 295, 197, 113, 52, 295, 295, 295,
        295, 94, 295, 295, 297, 322, 295, 41, 37, 259,
        192, 249, 261, 308, 203, 295, 295, 110, 295, 295,
        197, 113, 65, 233, 238, 240, 241, 94, 295, 295,
        297, 322, 295, 196, 295, 259, 192, 249, 295, 308,
        261, 295, 203, 295, 295, 110, 295, 349, 197, 113,
        58, 221, 295, 295, 295, 94, 295, 295, 297, 322,
        27, 295, 295, 259, 192, 249, 17, 308, 261, 425,
        203, 295, 295, 110, 295, 295, 197, 113, 56, 295,
        425, 295, 295, 94, 295, 295, 297, 322, 295, 295,
        295, 259, 192, 249, 261, 308, 203, 295, 295, 110,
        295, 295, 197, 113, 44, 295, 295, 295, 295, 94,
        295, 295, 297, 322, 295, 295, 295, 259, 192, 249,
        295, 308, 261, 295, 203, 295, 295, 110, 295, 295,
        197, 93, 70, 295, 295, 295, 295, 94, 295, 295,
        297, 322, 295, 295, 295, 259, 192, 249, 295, 308,
        261, 295, 203, 295, 295, 110, 295, 295, 186, 113,
        54, 295, 295, 295, 295, 94, 295, 295, 297, 322,
        295, 295, 295, 259, 192, 249, 261, 308, 203, 295,
        295, 110, 295, 295, 197, 113, 73, 295, 295, 295,
        295, 94, 295, 295, 297, 322, 295, 295, 295, 259,
        192, 249, 295, 308, 261, 295, 203, 295, 295, 110,
        295, 295, 197, 113, 68, 295, 295, 295, 295, 94,
        295, 295, 297, 322, 295, 295, 295, 259, 192, 249,
        295, 308, 261, 295, 203, 295, 295, 110, 295, 295,
        197, 93, 47, 295, 295, 295, 295, 94, 295, 295,
        297, 322, 391, 391, 391, 259, 192, 249, 261, 308,
        203, 295, 295, 110, 295, 295, 197, 113, 51, 295,
        295, 295, 295, 94, 295, 295, 297, 322, 196, 295,
        43, 259, 192, 249, 295, 308, 295, 295, 425, 295,
        391, 391, 295, 295, 295, 261, 295, 203, 295, 425,
        110, 295, 295, 197, 118, 27, 391, 391, 391, 391,
        94, 17, 295, 295, 258, 295, 41, 37, 259, 192,
        249, 261, 308, 203, 295, 196, 110, 43, 295, 197,
        120, 295, 233, 238, 240, 241, 94, 295, 295, 295,
        243, 295, 295, 295, 259, 192, 249, 295, 308, 295,
        32, 295, 27, 212, 295, 295, 295, 295, 17, 295,
        295, 295, 454, 41, 37, 454, 295, 295, 295, 454,
        438, 295, 295, 295, 295, 295, 295, 295, 295, 233,
        238, 240, 241, 295, 295, 261, 295, 203, 295, 295,
        110, 295, 295, 197, 112, 295, 438, 295, 295, 438,
        94, 454, 212, 438, 268, 295, 295, 295, 259, 192,
        249, 454, 308, 212, 454, 295, 295, 34, 454, 438,
        295, 295, 454, 295, 295, 454, 295, 133, 4, 454,
        438, 167, 295, 295, 295, 295, 295, 280, 295, 295,
        40, 14, 257, 295, 295, 438, 295, 295, 438, 261,
        454, 203, 438, 295, 110, 172, 438, 197, 121, 438,
        261, 454, 203, 438, 94, 110, 295, 295, 197, 117,
        295, 295, 259, 192, 249, 94, 308, 295, 295, 295,
        295, 295, 295, 259, 192, 249, 261, 308, 203, 295,
        295, 110, 295, 295, 197, 116, 295, 261, 295, 203,
        295, 94, 110, 295, 295, 197, 114, 295, 295, 259,
        192, 249, 94, 308, 196, 295, 43, 295, 295, 295,
        259, 192, 249, 261, 308, 203, 295, 196, 110, 43,
        295, 197, 123, 295, 295, 295, 106, 295, 94, 295,
        196, 174, 43, 295, 295, 295, 259, 192, 249, 196,
        308, 43, 41, 37, 244, 295, 295, 295, 295, 295,
        295, 295, 295, 234, 295, 41, 37, 295, 233, 238,
        240, 241, 295, 295, 295, 295, 295, 295, 41, 37,
        295, 233, 238, 240, 241, 295, 295, 41, 37, 295,
        295, 295, 295, 295, 233, 238, 240, 241, 25, 196,
        295, 43, 295, 233, 238, 240, 241, 454, 295, 295,
        454, 295, 295, 279, 454, 438, 212, 295, 295, 295,
        295, 295, 295, 295, 295, 454, 295, 295, 454, 295,
        295, 295, 454, 438, 196, 295, 43, 41, 37, 295,
        295, 438, 295, 196, 438, 43, 454, 295, 438, 295,
        295, 295, 295, 233, 238, 240, 241, 173, 295, 438,
        295, 295, 438, 295, 454, 295, 438, 454, 295, 295,
        454, 295, 41, 37, 454, 438, 295, 295, 295, 295,
        295, 41, 37, 295, 295, 295, 242, 295, 233, 238,
        240, 241, 295, 295, 295, 295, 295, 233, 238, 240,
        241, 438, 295, 295, 438, 295, 454, 147, 438, 295,
        295, 167, 295, 295, 295, 295, 295, 280, 295, 295,
        40, 14, 257, 295, 295, 295, 295, 295, 295, 295,
        295, 295, 295, 295, 295, 172,
    );

    public static $yy_lookahead     = array(
        3, 9, 10, 15, 71, 17, 28, 74, 11, 12,
        13, 14, 34, 16, 81, 18, 28, 20, 21, 22,
        11, 82, 34, 14, 27, 37, 93, 18, 31, 32,
        33, 45, 35, 66, 37, 68, 48, 28, 52, 42,
        43, 44, 45, 34, 47, 15, 49, 16, 51, 52,
        53, 54, 3, 23, 77, 25, 59, 17, 28, 19,
        11, 12, 13, 14, 34, 16, 36, 18, 38, 20,
        21, 22, 105, 106, 94, 45, 27, 73, 101, 73,
        31, 32, 33, 77, 35, 54, 37, 83, 48, 83,
        94, 42, 43, 44, 45, 14, 47, 16, 49, 18,
        51, 52, 3, 54, 36, 101, 38, 101, 59, 15,
        11, 12, 13, 14, 77, 16, 35, 18, 24, 20,
        21, 22, 28, 86, 87, 88, 27, 24, 34, 37,
        31, 32, 33, 82, 35, 54, 37, 66, 101, 68,
        46, 42, 43, 44, 45, 16, 47, 71, 49, 46,
        51, 52, 3, 54, 78, 79, 53, 81, 59, 15,
        11, 12, 13, 14, 37, 16, 37, 18, 24, 20,
        21, 22, 28, 19, 65, 48, 27, 106, 34, 101,
        31, 32, 33, 54, 35, 71, 37, 16, 74, 18,
        46, 42, 43, 44, 45, 81, 47, 45, 49, 45,
        51, 52, 3, 54, 90, 53, 52, 93, 59, 30,
        11, 12, 13, 14, 82, 16, 37, 18, 24, 20,
        21, 22, 51, 18, 14, 54, 27, 48, 73, 17,
        31, 32, 33, 1, 35, 14, 37, 16, 83, 18,
        46, 42, 43, 44, 45, 1, 47, 37, 49, 77,
        51, 52, 3, 54, 60, 50, 1, 45, 59, 15,
        11, 12, 13, 14, 52, 16, 36, 18, 38, 20,
        21, 22, 28, 101, 102, 54, 27, 8, 34, 10,
        31, 32, 33, 28, 35, 95, 37, 97, 98, 34,
        94, 42, 43, 44, 45, 1, 47, 71, 49, 77,
        51, 52, 3, 54, 78, 23, 73, 81, 59, 15,
        11, 12, 13, 14, 73, 16, 83, 18, 36, 20,
        21, 22, 28, 101, 83, 17, 27, 19, 34, 96,
        31, 32, 33, 45, 35, 16, 37, 96, 37, 41,
        52, 42, 43, 44, 45, 1, 47, 71, 49, 48,
        51, 52, 3, 54, 78, 37, 48, 81, 59, 15,
        11, 12, 13, 14, 71, 16, 48, 18, 17, 20,
        21, 22, 28, 54, 81, 24, 27, 70, 34, 37,
        31, 32, 33, 28, 35, 24, 37, 45, 1, 34,
        48, 42, 43, 44, 45, 53, 47, 2, 49, 73,
        51, 52, 3, 54, 97, 98, 19, 46, 59, 83,
        11, 12, 13, 14, 36, 16, 38, 18, 98, 20,
        21, 22, 11, 64, 65, 14, 27, 101, 1, 18,
        31, 32, 33, 94, 35, 96, 37, 1, 17, 19,
        92, 42, 43, 44, 45, 73, 47, 99, 49, 77,
        51, 52, 3, 54, 16, 83, 18, 30, 59, 70,
        11, 12, 13, 14, 73, 16, 45, 18, 48, 20,
        21, 22, 11, 101, 83, 14, 27, 35, 92, 18,
        31, 32, 33, 94, 35, 99, 37, 97, 98, 53,
        36, 42, 43, 44, 45, 66, 47, 68, 49, 18,
        51, 52, 3, 54, 94, 38, 96, 53, 59, 81,
        11, 12, 13, 14, 73, 16, 77, 18, 73, 20,
        21, 22, 11, 3, 83, 14, 27, 99, 83, 18,
        31, 32, 33, 37, 35, 18, 37, 18, 1, 2,
        101, 42, 43, 44, 45, 35, 47, 18, 49, 16,
        51, 18, 18, 54, 62, 63, 97, 98, 59, 39,
        40, 50, 18, 4, 5, 6, 7, 8, 45, 16,
        11, 12, 13, 14, 18, 55, 56, 57, 58, 20,
        21, 22, 49, 53, 51, 53, 27, 54, 18, 15,
        31, 32, 33, 66, 67, 68, 69, 25, 71, 72,
        51, 74, 75, 76, 18, 18, 11, 11, 81, 14,
        14, 84, 85, 18, 18, 17, 89, 90, 91, 9,
        93, 15, 51, 28, 102, 30, 81, 81, 19, 34,
        66, 67, 68, 69, 95, 71, 72, 83, 74, 75,
        76, 94, 94, 80, 94, 81, 50, 81, 84, 85,
        107, 96, 94, 89, 90, 91, 107, 93, 66, 107,
        68, 107, 107, 71, 107, 107, 74, 75, 76, 107,
        107, 107, 107, 81, 107, 107, 84, 85, 107, 107,
        107, 89, 90, 91, 66, 93, 68, 69, 107, 71,
        72, 107, 74, 75, 76, 103, 104, 107, 11, 81,
        107, 14, 84, 85, 107, 18, 107, 89, 90, 91,
        107, 93, 107, 5, 107, 107, 107, 107, 107, 11,
        12, 13, 14, 107, 16, 107, 107, 107, 20, 21,
        22, 107, 107, 107, 107, 27, 5, 50, 107, 31,
        32, 33, 11, 12, 13, 14, 107, 16, 107, 107,
        107, 20, 21, 22, 107, 107, 107, 107, 27, 107,
        107, 107, 31, 32, 33, 107, 107, 59, 60, 66,
        107, 68, 107, 107, 71, 107, 107, 74, 75, 76,
        107, 107, 107, 107, 81, 73, 107, 84, 85, 77,
        59, 60, 89, 90, 91, 83, 93, 107, 86, 87,
        88, 107, 66, 100, 68, 107, 107, 71, 107, 107,
        74, 75, 76, 101, 107, 107, 107, 81, 107, 107,
        84, 85, 107, 107, 107, 89, 90, 91, 107, 93,
        107, 107, 107, 107, 66, 107, 68, 107, 107, 71,
        104, 107, 74, 75, 76, 107, 78, 107, 107, 81,
        107, 107, 84, 85, 107, 107, 107, 89, 90, 91,
        66, 93, 68, 107, 107, 71, 107, 107, 74, 75,
        76, 107, 107, 107, 107, 81, 107, 107, 84, 85,
        107, 107, 107, 89, 90, 91, 107, 93, 66, 107,
        68, 107, 107, 71, 100, 107, 74, 75, 76, 1,
        2, 3, 107, 81, 107, 107, 84, 85, 107, 107,
        107, 89, 90, 91, 107, 93, 66, 107, 68, 107,
        107, 71, 100, 107, 74, 75, 76, 107, 78, 107,
        107, 81, 107, 107, 84, 85, 107, 39, 40, 89,
        90, 91, 66, 93, 68, 107, 107, 71, 107, 107,
        74, 75, 76, 55, 56, 57, 58, 81, 107, 107,
        84, 85, 107, 107, 107, 89, 90, 91, 107, 93,
        66, 107, 68, 107, 107, 71, 107, 107, 74, 75,
        76, 1, 107, 3, 107, 81, 107, 107, 84, 85,
        107, 107, 107, 89, 90, 91, 107, 93, 66, 107,
        68, 107, 107, 71, 107, 107, 74, 75, 76, 107,
        107, 107, 107, 81, 107, 107, 84, 85, 107, 39,
        40, 89, 90, 91, 66, 93, 68, 107, 107, 71,
        107, 107, 74, 75, 76, 55, 56, 57, 58, 81,
        60, 107, 84, 85, 107, 107, 107, 89, 90, 91,
        107, 93, 66, 107, 68, 107, 107, 71, 107, 107,
        74, 75, 76, 1, 107, 3, 107, 81, 107, 107,
        84, 85, 107, 107, 107, 89, 90, 91, 107, 93,
        66, 107, 68, 107, 107, 71, 107, 107, 74, 75,
        76, 107, 107, 107, 107, 81, 107, 107, 84, 85,
        38, 39, 40, 89, 90, 91, 66, 93, 68, 107,
        107, 71, 107, 107, 74, 75, 76, 55, 56, 57,
        58, 81, 107, 107, 84, 85, 107, 107, 107, 89,
        90, 91, 107, 93, 66, 107, 68, 107, 107, 71,
        107, 107, 74, 75, 76, 1, 107, 3, 107, 81,
        107, 107, 84, 85, 107, 107, 107, 89, 90, 91,
        107, 93, 66, 107, 68, 107, 107, 71, 107, 107,
        74, 75, 76, 29, 107, 107, 107, 81, 107, 107,
        84, 85, 107, 39, 40, 89, 90, 91, 66, 93,
        68, 107, 107, 71, 107, 107, 74, 75, 76, 55,
        56, 57, 58, 81, 107, 107, 84, 85, 107, 107,
        107, 89, 90, 91, 107, 93, 66, 107, 68, 107,
        107, 71, 107, 107, 74, 75, 76, 1, 107, 3,
        107, 81, 107, 107, 84, 85, 107, 107, 107, 89,
        90, 91, 107, 93, 66, 107, 68, 107, 107, 71,
        107, 107, 74, 75, 76, 107, 107, 107, 107, 81,
        107, 107, 84, 85, 38, 39, 40, 89, 90, 91,
        66, 93, 68, 107, 107, 71, 107, 107, 74, 75,
        76, 55, 56, 57, 58, 81, 107, 107, 84, 85,
        107, 107, 107, 89, 90, 91, 107, 93, 66, 107,
        68, 107, 107, 71, 107, 107, 74, 75, 76, 1,
        107, 3, 107, 81, 107, 107, 84, 85, 107, 107,
        107, 89, 90, 91, 107, 93, 66, 107, 68, 107,
        107, 71, 107, 107, 74, 75, 76, 107, 107, 107,
        107, 81, 107, 107, 84, 85, 107, 39, 40, 89,
        90, 91, 66, 93, 68, 107, 107, 71, 107, 107,
        74, 75, 76, 55, 56, 57, 58, 81, 107, 107,
        84, 85, 107, 1, 107, 89, 90, 91, 107, 93,
        66, 107, 68, 107, 107, 71, 107, 15, 74, 75,
        76, 19, 107, 107, 107, 81, 107, 107, 84, 85,
        28, 107, 107, 89, 90, 91, 34, 93, 66, 37,
        68, 107, 107, 71, 107, 107, 74, 75, 76, 107,
        48, 107, 107, 81, 107, 107, 84, 85, 107, 107,
        107, 89, 90, 91, 66, 93, 68, 107, 107, 71,
        107, 107, 74, 75, 76, 107, 107, 107, 107, 81,
        107, 107, 84, 85, 107, 107, 107, 89, 90, 91,
        107, 93, 66, 107, 68, 107, 107, 71, 107, 107,
        74, 75, 76, 107, 107, 107, 107, 81, 107, 107,
        84, 85, 107, 107, 107, 89, 90, 91, 107, 93,
        66, 107, 68, 107, 107, 71, 107, 107, 74, 75,
        76, 107, 107, 107, 107, 81, 107, 107, 84, 85,
        107, 107, 107, 89, 90, 91, 66, 93, 68, 107,
        107, 71, 107, 107, 74, 75, 76, 107, 107, 107,
        107, 81, 107, 107, 84, 85, 107, 107, 107, 89,
        90, 91, 107, 93, 66, 107, 68, 107, 107, 71,
        107, 107, 74, 75, 76, 107, 107, 107, 107, 81,
        107, 107, 84, 85, 107, 107, 107, 89, 90, 91,
        107, 93, 66, 107, 68, 107, 107, 71, 107, 107,
        74, 75, 76, 107, 107, 107, 107, 81, 107, 107,
        84, 85, 1, 2, 3, 89, 90, 91, 66, 93,
        68, 107, 107, 71, 107, 107, 74, 75, 76, 107,
        107, 107, 107, 81, 107, 107, 84, 85, 1, 107,
        3, 89, 90, 91, 107, 93, 107, 107, 37, 107,
        39, 40, 107, 107, 107, 66, 107, 68, 107, 48,
        71, 107, 107, 74, 75, 28, 55, 56, 57, 58,
        81, 34, 107, 107, 85, 107, 39, 40, 89, 90,
        91, 66, 93, 68, 107, 1, 71, 3, 107, 74,
        75, 107, 55, 56, 57, 58, 81, 107, 107, 107,
        85, 107, 107, 107, 89, 90, 91, 107, 93, 107,
        26, 107, 28, 2, 107, 107, 107, 107, 34, 107,
        107, 107, 11, 39, 40, 14, 107, 107, 107, 18,
        19, 107, 107, 107, 107, 107, 107, 107, 107, 55,
        56, 57, 58, 107, 107, 66, 107, 68, 107, 107,
        71, 107, 107, 74, 75, 107, 45, 107, 107, 48,
        81, 50, 2, 52, 53, 107, 107, 107, 89, 90,
        91, 11, 93, 2, 14, 107, 107, 17, 18, 19,
        107, 107, 11, 107, 107, 14, 107, 73, 17, 18,
        19, 77, 107, 107, 107, 107, 107, 83, 107, 107,
        86, 87, 88, 107, 107, 45, 107, 107, 48, 66,
        50, 68, 52, 107, 71, 101, 45, 74, 75, 48,
        66, 50, 68, 52, 81, 71, 107, 107, 74, 75,
        107, 107, 89, 90, 91, 81, 93, 107, 107, 107,
        107, 107, 107, 89, 90, 91, 66, 93, 68, 107,
        107, 71, 107, 107, 74, 75, 107, 66, 107, 68,
        107, 81, 71, 107, 107, 74, 75, 107, 107, 89,
        90, 91, 81, 93, 1, 107, 3, 107, 107, 107,
        89, 90, 91, 66, 93, 68, 107, 1, 71, 3,
        107, 74, 75, 107, 107, 107, 23, 107, 81, 107,
        1, 15, 3, 107, 107, 107, 89, 90, 91, 1,
        93, 3, 39, 40, 15, 107, 107, 107, 107, 107,
        107, 107, 107, 15, 107, 39, 40, 107, 55, 56,
        57, 58, 107, 107, 107, 107, 107, 107, 39, 40,
        107, 55, 56, 57, 58, 107, 107, 39, 40, 107,
        107, 107, 107, 107, 55, 56, 57, 58, 2, 1,
        107, 3, 107, 55, 56, 57, 58, 11, 107, 107,
        14, 107, 107, 15, 18, 19, 2, 107, 107, 107,
        107, 107, 107, 107, 107, 11, 107, 107, 14, 107,
        107, 107, 18, 19, 1, 107, 3, 39, 40, 107,
        107, 45, 107, 1, 48, 3, 50, 107, 52, 107,
        107, 107, 107, 55, 56, 57, 58, 15, 107, 45,
        107, 107, 48, 107, 50, 107, 52, 11, 107, 107,
        14, 107, 39, 40, 18, 19, 107, 107, 107, 107,
        107, 39, 40, 107, 107, 107, 53, 107, 55, 56,
        57, 58, 107, 107, 107, 107, 107, 55, 56, 57,
        58, 45, 107, 107, 48, 107, 50, 73, 52, 107,
        107, 77, 107, 107, 107, 107, 107, 83, 107, 107,
        86, 87, 88, 107, 107, 107, 107, 107, 107, 107,
        107, 107, 107, 107, 107, 101,
    );

    public static $yy_shift_ofst    = array(
        -23, 399, 399, 449, 49, 49, 449, 349, 49, 49,
        349, -3, 49, 49, 49, 49, 49, 49, 49, 49,
        49, 49, 49, 149, 199, 299, 49, 149, 49, 49,
        49, 49, 49, 49, 249, 49, 99, 99, 499, 499,
        499, 499, 499, 499, 1664, 1617, 1617, 1144, 1982, 1973,
        1938, 1226, 1853, 1062, 980, 1879, 898, 1866, 1888, 1308,
        1308, 1308, 1308, 1308, 1308, 1308, 1308, 1308, 1308, 1308,
        1308, 1308, 520, 520, 533, 731, 1372, 171, 255, 129,
        708, 595, 9, 154, 129, 255, 308, 129, 255, 537,
        559, 1751, 244, 344, 511, 221, 294, 411, 40, 411,
        -22, 438, 438, 436, 387, 427, 319, 355, -22, -22,
        420, 609, 232, 232, 232, 609, 232, 232, 232, 232,
        -23, -23, -23, -23, 1740, 1691, 1954, 1936, 1996, 81,
        687, 461, 212, -22, 31, -14, -14, -22, 288, -14,
        288, -14, -22, 31, -22, -14, -14, -22, -22, 351,
        -22, -22, -14, -22, -22, -22, -22, -22, -14, 210,
        232, 609, 232, 395, 609, 232, 609, 232, 395, 92,
        232, -23, -23, -23, -23, -23, -23, 1591, 30, -12,
        94, 144, 342, 596, 179, 103, 194, 454, 230, 152,
        269, 301, 318, 127, 282, -8, 205, 361, 378, 421,
        68, 467, 556, 606, 571, 598, 587, 586, 610, 549,
        572, 574, 570, 532, 530, 553, 298, 523, 544, 510,
        92, 534, 529, 519, 517, 496, 442, 481,
    );

    public static $yy_reduce_ofst   = array(
        492, 527, 564, 592, 618, 703, 736, 768, 794, 822,
        850, 1068, 1096, 1122, 1150, 1286, 1204, 1232, 1260, 1040,
        1314, 1532, 1478, 1506, 1342, 1450, 1424, 1396, 1368, 1178,
        1014, 986, 932, 904, 876, 958, 1595, 1569, 1771, 1659,
        1760, 1734, 1723, 1797, 712, 1694, 1974, 37, 37, 37,
        37, 37, 37, 37, 37, 37, 37, 37, 37, 37,
        37, 37, 37, 37, 37, 37, 37, 37, 37, 37,
        37, 37, 37, 37, 114, -33, 372, -67, 6, 76,
        71, 233, 241, 190, 226, 4, 307, 276, 326, 172,
        429, 389, -23, -23, 339, 428, -23, 410, 390, 339,
        391, 386, 348, -23, 222, -23, 293, 155, 441, 445,
        390, 459, -23, -23, -23, 390, -23, -23, -23, 439,
        -23, -23, 359, -23, 550, 550, 550, 550, 550, 545,
        555, 550, 550, 554, 566, 539, 539, 554, 547, 539,
        548, 539, 554, 546, 554, 539, 539, 554, 554, 563,
        554, 554, 539, 554, 554, 554, 554, 554, 539, 558,
        78, 320, 78, 522, 320, 78, 320, 78, 522, 196,
        78, 51, -61, -20, -4, 109, 132,
    );

    public static $yyExpectedTokens = array(
        array(),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(
            3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 53, 54, 59,
        ),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 54, 59,),
        array(3, 11, 12, 13, 14, 16, 18, 20, 21, 22, 27, 31, 32, 33, 35, 37, 42, 43, 44, 45, 47, 49, 51, 54, 59,),
        array(1, 3, 26, 28, 34, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 28, 34, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 28, 34, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 29, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 15, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 53, 55, 56, 57, 58,),
        array(1, 3, 15, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 38, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 23, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 38, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58, 60,),
        array(1, 3, 15, 39, 40, 55, 56, 57, 58,),
        array(1, 2, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 15, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 15, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(1, 3, 39, 40, 55, 56, 57, 58,),
        array(3, 39, 40, 55, 56, 57, 58,),
        array(3, 39, 40, 55, 56, 57, 58,),
        array(16, 18, 49, 51, 54,),
        array(5, 11, 12, 13, 14, 16, 20, 21, 22, 27, 31, 32, 33, 59, 60,),
        array(1, 15, 19, 28, 34, 37, 48,),
        array(16, 18, 51, 54,),
        array(1, 28, 34,),
        array(16, 37, 54,),
        array(5, 11, 12, 13, 14, 16, 20, 21, 22, 27, 31, 32, 33, 59, 60,),
        array(11, 14, 18, 28, 30, 34,),
        array(11, 14, 18, 28, 34,),
        array(19, 45, 52,),
        array(16, 37, 54,),
        array(1, 28, 34,),
        array(17, 19, 48,),
        array(16, 37, 54,),
        array(1, 28, 34,),
        array(1, 2,),
        array(4, 5, 6, 7, 8, 11, 12, 13, 14, 20, 21, 22, 27, 31, 32, 33,),
        array(2, 11, 14, 17, 18, 19, 45, 48, 50, 52,),
        array(1, 15, 28, 34,),
        array(1, 15, 28, 34,),
        array(11, 14, 18, 50,),
        array(14, 16, 18, 54,),
        array(1, 15, 28, 34,),
        array(11, 14, 18,),
        array(17, 19, 48,),
        array(11, 14, 18,),
        array(28, 34,),
        array(16, 18,),
        array(16, 18,),
        array(1, 53,),
        array(1, 19,),
        array(1, 30,),
        array(16, 54,),
        array(28, 34,),
        array(28, 34,),
        array(28, 34,),
        array(19, 48,),
        array(19,),
        array(1,),
        array(1,),
        array(1,),
        array(19,),
        array(1,),
        array(1,),
        array(1,),
        array(1,),
        array(),
        array(),
        array(),
        array(),
        array(2, 11, 14, 17, 18, 19, 45, 48, 50, 52,),
        array(2, 11, 14, 18, 19, 45, 48, 50, 52, 53,),
        array(2, 11, 14, 18, 19, 45, 48, 50, 52,),
        array(2, 11, 14, 18, 19, 45, 48, 50, 52,),
        array(11, 14, 18, 19, 45, 48, 50, 52,),
        array(14, 16, 18, 35, 54,),
        array(11, 14, 18, 50,),
        array(11, 14, 18,),
        array(17, 45, 52,),
        array(28, 34,),
        array(16, 54,),
        array(45, 52,),
        array(45, 52,),
        array(28, 34,),
        array(45, 52,),
        array(45, 52,),
        array(45, 52,),
        array(45, 52,),
        array(28, 34,),
        array(16, 54,),
        array(28, 34,),
        array(45, 52,),
        array(45, 52,),
        array(28, 34,),
        array(28, 34,),
        array(17, 24,),
        array(28, 34,),
        array(28, 34,),
        array(45, 52,),
        array(28, 34,),
        array(28, 34,),
        array(28, 34,),
        array(28, 34,),
        array(28, 34,),
        array(45, 52,),
        array(14, 37,),
        array(1,),
        array(19,),
        array(1,),
        array(2,),
        array(19,),
        array(1,),
        array(19,),
        array(1,),
        array(2,),
        array(37,),
        array(1,),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(1, 2, 3, 37, 39, 40, 48, 55, 56, 57, 58,),
        array(15, 23, 25, 28, 34, 36, 38, 45,),
        array(15, 17, 28, 34, 37, 48,),
        array(15, 24, 28, 34, 46,),
        array(15, 24, 28, 34, 46,),
        array(37, 45, 48, 53,),
        array(11, 14, 18, 50,),
        array(30, 37, 48,),
        array(24, 46, 53,),
        array(24, 46, 60,),
        array(36, 53,),
        array(36, 38,),
        array(45, 53,),
        array(8, 10,),
        array(37, 48,),
        array(37, 48,),
        array(37, 48,),
        array(23, 36,),
        array(9, 10,),
        array(18, 50,),
        array(24, 46,),
        array(36, 38,),
        array(17, 45,),
        array(36, 38,),
        array(38,),
        array(18,),
        array(15,),
        array(51,),
        array(17,),
        array(18,),
        array(18,),
        array(9,),
        array(51,),
        array(25,),
        array(15,),
        array(18,),
        array(53,),
        array(53,),
        array(16,),
        array(41,),
        array(45,),
        array(18,),
        array(35,),
        array(37,),
        array(18,),
        array(18,),
        array(18,),
        array(18,),
        array(37,),
        array(35,),
        array(18,),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
    );

    public static $yy_default       = array(
        334, 509, 509, 494, 509, 473, 509, 509, 473, 473,
        509, 509, 509, 509, 509, 509, 509, 509, 509, 509,
        509, 509, 509, 509, 509, 509, 509, 509, 509, 509,
        509, 509, 509, 509, 509, 509, 509, 509, 509, 509,
        509, 509, 509, 509, 375, 354, 375, 380, 509, 509,
        347, 509, 509, 509, 509, 509, 509, 509, 509, 397,
        472, 347, 471, 387, 497, 495, 382, 386, 359, 377,
        380, 496, 402, 401, 509, 509, 413, 509, 375, 509,
        509, 375, 375, 485, 509, 375, 428, 509, 375, 366,
        323, 427, 389, 389, 438, 509, 389, 438, 428, 438,
        375, 509, 509, 389, 369, 389, 509, 375, 375, 356,
        428, 482, 405, 389, 406, 428, 396, 392, 400, 371,
        480, 404, 332, 393, 427, 427, 427, 427, 427, 509,
        440, 438, 454, 355, 509, 436, 434, 365, 433, 432,
        431, 465, 364, 509, 363, 466, 463, 362, 352, 509,
        351, 357, 435, 344, 350, 358, 361, 348, 464, 438,
        422, 460, 367, 474, 486, 372, 483, 395, 475, 438,
        370, 479, 479, 438, 438, 332, 479, 413, 409, 413,
        403, 403, 413, 439, 413, 403, 403, 509, 509, 409,
        330, 423, 509, 413, 509, 509, 509, 403, 509, 409,
        509, 509, 509, 509, 509, 509, 509, 509, 509, 509,
        383, 509, 509, 418, 509, 509, 415, 409, 509, 509,
        454, 509, 509, 509, 509, 484, 411, 509, 324, 426,
        415, 360, 442, 487, 444, 336, 443, 337, 488, 376,
        489, 490, 452, 481, 459, 454, 410, 441, 328, 419,
        325, 326, 437, 420, 477, 327, 476, 398, 399, 414,
        335, 421, 388, 424, 412, 451, 329, 331, 449, 333,
        384, 469, 500, 468, 491, 505, 343, 416, 417, 506,
        374, 391, 492, 493, 498, 341, 373, 418, 425, 353,
        501, 508, 507, 504, 502, 499, 461, 390, 368, 408,
        338, 503, 478, 453, 447, 446, 429, 445, 430, 448,
        450, 342, 462, 339, 340, 455, 470, 458, 457, 407,
        467, 456, 394,
    );

    public static $yyFallback       = array();

    public static $yyRuleName       = array(
        'start ::= template',
        'template ::= template PHP',
        'template ::= template TEXT',
        'template ::= template STRIPON',
        'template ::= template STRIPOFF',
        'template ::= template LITERALSTART literal_e2 LITERALEND',
        'literal_e2 ::= literal_e1 LITERALSTART literal_e1 LITERALEND',
        'literal_e2 ::= literal_e1',
        'literal_e1 ::= literal_e1 LITERAL',
        'literal_e1 ::=',
        'template ::= template smartytag',
        'template ::=',
        'smartytag ::= SIMPELOUTPUT',
        'smartytag ::= SIMPLETAG',
        'smartytag ::= SMARTYBLOCKCHILDPARENT',
        'smartytag ::= LDEL tagbody RDEL',
        'smartytag ::= tag RDEL',
        'tagbody ::= outattr',
        'tagbody ::= DOLLARID eqoutattr',
        'tagbody ::= varindexed eqoutattr',
        'eqoutattr ::= EQUAL outattr',
        'outattr ::= output attributes',
        'output ::= variable',
        'output ::= value',
        'output ::= expr',
        'tag ::= LDEL ID attributes',
        'tag ::= LDEL ID',
        'tag ::= LDEL ID modifierlist attributes',
        'tag ::= LDEL ID PTR ID attributes',
        'tag ::= LDEL ID PTR ID modifierlist attributes',
        'tag ::= LDELMAKENOCACHE DOLLARID',
        'tag ::= LDELIF expr',
        'tag ::= LDELIF expr attributes',
        'tag ::= LDELIF statement',
        'tag ::= LDELIF statement attributes',
        'tag ::= LDELFOR statements SEMICOLON expr SEMICOLON varindexed foraction attributes',
        'foraction ::= EQUAL expr',
        'foraction ::= INCDEC',
        'tag ::= LDELFOR statement TO expr attributes',
        'tag ::= LDELFOR statement TO expr STEP expr attributes',
        'tag ::= LDELFOREACH SPACE expr AS varvar attributes',
        'tag ::= LDELFOREACH SPACE expr AS varvar APTR varvar attributes',
        'tag ::= LDELFOREACH attributes',
        'tag ::= LDELSETFILTER ID modparameters',
        'tag ::= LDELSETFILTER ID modparameters modifierlist',
        'smartytag ::= CLOSETAG',
        'tag ::= LDELSLASH ID',
        'tag ::= LDELSLASH ID modifierlist',
        'tag ::= LDELSLASH ID PTR ID',
        'tag ::= LDELSLASH ID PTR ID modifierlist',
        'attributes ::= attributes attribute',
        'attributes ::= attribute',
        'attributes ::=',
        'attribute ::= SPACE ID EQUAL ID',
        'attribute ::= ATTR expr',
        'attribute ::= ATTR value',
        'attribute ::= SPACE ID',
        'attribute ::= SPACE expr',
        'attribute ::= SPACE value',
        'attribute ::= SPACE INTEGER EQUAL expr',
        'statements ::= statement',
        'statements ::= statements COMMA statement',
        'statement ::= DOLLARID EQUAL INTEGER',
        'statement ::= DOLLARID EQUAL expr',
        'statement ::= varindexed EQUAL expr',
        'statement ::= OPENP statement CLOSEP',
        'expr ::= value',
        'expr ::= ternary',
        'expr ::= DOLLARID COLON ID',
        'expr ::= expr MATH value',
        'expr ::= expr UNIMATH value',
        'expr ::= array',
        'expr ::= expr modifierlist',
        'expr ::= expr tlop value',
        'expr ::= expr lop expr',
        'expr ::= expr scond',
        'expr ::= expr ISIN array',
        'expr ::= expr ISIN value',
        'ternary ::= OPENP expr CLOSEP QMARK DOLLARID COLON expr',
        'ternary ::= OPENP expr CLOSEP QMARK expr COLON expr',
        'value ::= variable',
        'value ::= UNIMATH value',
        'value ::= NOT value',
        'value ::= TYPECAST value',
        'value ::= variable INCDEC',
        'value ::= HEX',
        'value ::= INTEGER',
        'value ::= INTEGER DOT INTEGER',
        'value ::= INTEGER DOT',
        'value ::= DOT INTEGER',
        'value ::= ID',
        'value ::= function',
        'value ::= OPENP expr CLOSEP',
        'value ::= variable INSTANCEOF ns1',
        'value ::= variable INSTANCEOF variable',
        'value ::= SINGLEQUOTESTRING',
        'value ::= doublequoted_with_quotes',
        'value ::= varindexed DOUBLECOLON static_class_access',
        'value ::= smartytag',
        'value ::= value modifierlist',
        'value ::= NAMESPACE',
        'value ::= ns1 DOUBLECOLON static_class_access',
        'ns1 ::= ID',
        'ns1 ::= NAMESPACE',
        'variable ::= DOLLARID',
        'variable ::= varindexed',
        'variable ::= varvar AT ID',
        'variable ::= object',
        'variable ::= HATCH ID HATCH',
        'variable ::= HATCH ID HATCH arrayindex',
        'variable ::= HATCH variable HATCH',
        'variable ::= HATCH variable HATCH arrayindex',
        'varindexed ::= DOLLARID arrayindex',
        'varindexed ::= varvar arrayindex',
        'arrayindex ::= arrayindex indexdef',
        'arrayindex ::=',
        'indexdef ::= DOT DOLLARID',
        'indexdef ::= DOT varvar',
        'indexdef ::= DOT varvar AT ID',
        'indexdef ::= DOT ID',
        'indexdef ::= DOT INTEGER',
        'indexdef ::= DOT LDEL expr RDEL',
        'indexdef ::= OPENB ID CLOSEB',
        'indexdef ::= OPENB ID DOT ID CLOSEB',
        'indexdef ::= OPENB SINGLEQUOTESTRING CLOSEB',
        'indexdef ::= OPENB INTEGER CLOSEB',
        'indexdef ::= OPENB DOLLARID CLOSEB',
        'indexdef ::= OPENB variable CLOSEB',
        'indexdef ::= OPENB value CLOSEB',
        'indexdef ::= OPENB expr CLOSEB',
        'indexdef ::= OPENB CLOSEB',
        'varvar ::= DOLLARID',
        'varvar ::= DOLLAR',
        'varvar ::= varvar varvarele',
        'varvarele ::= ID',
        'varvarele ::= SIMPELOUTPUT',
        'varvarele ::= LDEL expr RDEL',
        'object ::= varindexed objectchain',
        'objectchain ::= objectelement',
        'objectchain ::= objectchain objectelement',
        'objectelement ::= PTR ID arrayindex',
        'objectelement ::= PTR varvar arrayindex',
        'objectelement ::= PTR LDEL expr RDEL arrayindex',
        'objectelement ::= PTR ID LDEL expr RDEL arrayindex',
        'objectelement ::= PTR method',
        'function ::= ns1 OPENP params CLOSEP',
        'method ::= ID OPENP params CLOSEP',
        'method ::= DOLLARID OPENP params CLOSEP',
        'params ::= params COMMA expr',
        'params ::= expr',
        'params ::=',
        'modifierlist ::= modifierlist modifier modparameters',
        'modifierlist ::= modifier modparameters',
        'modifier ::= VERT AT ID',
        'modifier ::= VERT ID',
        'modparameters ::= modparameters modparameter',
        'modparameters ::=',
        'modparameter ::= COLON value',
        'modparameter ::= COLON array',
        'static_class_access ::= method',
        'static_class_access ::= method objectchain',
        'static_class_access ::= ID',
        'static_class_access ::= DOLLARID arrayindex',
        'static_class_access ::= DOLLARID arrayindex objectchain',
        'lop ::= LOGOP',
        'lop ::= SLOGOP',
        'tlop ::= TLOGOP',
        'scond ::= SINGLECOND',
        'array ::= OPENB arrayelements CLOSEB',
        'arrayelements ::= arrayelement',
        'arrayelements ::= arrayelements COMMA arrayelement',
        'arrayelements ::=',
        'arrayelement ::= value APTR expr',
        'arrayelement ::= ID APTR expr',
        'arrayelement ::= expr',
        'doublequoted_with_quotes ::= QUOTE QUOTE',
        'doublequoted_with_quotes ::= QUOTE doublequoted QUOTE',
        'doublequoted ::= doublequoted doublequotedcontent',
        'doublequoted ::= doublequotedcontent',
        'doublequotedcontent ::= BACKTICK variable BACKTICK',
        'doublequotedcontent ::= BACKTICK expr BACKTICK',
        'doublequotedcontent ::= DOLLARID',
        'doublequotedcontent ::= LDEL variable RDEL',
        'doublequotedcontent ::= LDEL expr RDEL',
        'doublequotedcontent ::= smartytag',
        'doublequotedcontent ::= TEXT',
    );

    public static $yyRuleInfo       = array(
        array(0 => 62, 1 => 1),
        array(0 => 63, 1 => 2),
        array(0 => 63, 1 => 2),
        array(0 => 63, 1 => 2),
        array(0 => 63, 1 => 2),
        array(0 => 63, 1 => 4),
        array(0 => 64, 1 => 4),
        array(0 => 64, 1 => 1),
        array(0 => 65, 1 => 2),
        array(0 => 65, 1 => 0),
        array(0 => 63, 1 => 2),
        array(0 => 63, 1 => 0),
        array(0 => 66, 1 => 1),
        array(0 => 66, 1 => 1),
        array(0 => 66, 1 => 1),
        array(0 => 66, 1 => 3),
        array(0 => 66, 1 => 2),
        array(0 => 67, 1 => 1),
        array(0 => 67, 1 => 2),
        array(0 => 67, 1 => 2),
        array(0 => 70, 1 => 2),
        array(0 => 69, 1 => 2),
        array(0 => 72, 1 => 1),
        array(0 => 72, 1 => 1),
        array(0 => 72, 1 => 1),
        array(0 => 68, 1 => 3),
        array(0 => 68, 1 => 2),
        array(0 => 68, 1 => 4),
        array(0 => 68, 1 => 5),
        array(0 => 68, 1 => 6),
        array(0 => 68, 1 => 2),
        array(0 => 68, 1 => 2),
        array(0 => 68, 1 => 3),
        array(0 => 68, 1 => 2),
        array(0 => 68, 1 => 3),
        array(0 => 68, 1 => 8),
        array(0 => 80, 1 => 2),
        array(0 => 80, 1 => 1),
        array(0 => 68, 1 => 5),
        array(0 => 68, 1 => 7),
        array(0 => 68, 1 => 6),
        array(0 => 68, 1 => 8),
        array(0 => 68, 1 => 2),
        array(0 => 68, 1 => 3),
        array(0 => 68, 1 => 4),
        array(0 => 66, 1 => 1),
        array(0 => 68, 1 => 2),
        array(0 => 68, 1 => 3),
        array(0 => 68, 1 => 4),
        array(0 => 68, 1 => 5),
        array(0 => 73, 1 => 2),
        array(0 => 73, 1 => 1),
        array(0 => 73, 1 => 0),
        array(0 => 83, 1 => 4),
        array(0 => 83, 1 => 2),
        array(0 => 83, 1 => 2),
        array(0 => 83, 1 => 2),
        array(0 => 83, 1 => 2),
        array(0 => 83, 1 => 2),
        array(0 => 83, 1 => 4),
        array(0 => 79, 1 => 1),
        array(0 => 79, 1 => 3),
        array(0 => 78, 1 => 3),
        array(0 => 78, 1 => 3),
        array(0 => 78, 1 => 3),
        array(0 => 78, 1 => 3),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 3),
        array(0 => 84, 1 => 7),
        array(0 => 84, 1 => 7),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 2),
        array(0 => 75, 1 => 2),
        array(0 => 75, 1 => 2),
        array(0 => 75, 1 => 2),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 3),
        array(0 => 75, 1 => 2),
        array(0 => 75, 1 => 2),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 3),
        array(0 => 75, 1 => 3),
        array(0 => 75, 1 => 3),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 3),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 2),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 3),
        array(0 => 90, 1 => 1),
        array(0 => 90, 1 => 1),
        array(0 => 74, 1 => 1),
        array(0 => 74, 1 => 1),
        array(0 => 74, 1 => 3),
        array(0 => 74, 1 => 1),
        array(0 => 74, 1 => 3),
        array(0 => 74, 1 => 4),
        array(0 => 74, 1 => 3),
        array(0 => 74, 1 => 4),
        array(0 => 71, 1 => 2),
        array(0 => 71, 1 => 2),
        array(0 => 94, 1 => 2),
        array(0 => 94, 1 => 0),
        array(0 => 95, 1 => 2),
        array(0 => 95, 1 => 2),
        array(0 => 95, 1 => 4),
        array(0 => 95, 1 => 2),
        array(0 => 95, 1 => 2),
        array(0 => 95, 1 => 4),
        array(0 => 95, 1 => 3),
        array(0 => 95, 1 => 5),
        array(0 => 95, 1 => 3),
        array(0 => 95, 1 => 3),
        array(0 => 95, 1 => 3),
        array(0 => 95, 1 => 3),
        array(0 => 95, 1 => 3),
        array(0 => 95, 1 => 3),
        array(0 => 95, 1 => 2),
        array(0 => 81, 1 => 1),
        array(0 => 81, 1 => 1),
        array(0 => 81, 1 => 2),
        array(0 => 96, 1 => 1),
        array(0 => 96, 1 => 1),
        array(0 => 96, 1 => 3),
        array(0 => 93, 1 => 2),
        array(0 => 97, 1 => 1),
        array(0 => 97, 1 => 2),
        array(0 => 98, 1 => 3),
        array(0 => 98, 1 => 3),
        array(0 => 98, 1 => 5),
        array(0 => 98, 1 => 6),
        array(0 => 98, 1 => 2),
        array(0 => 89, 1 => 4),
        array(0 => 99, 1 => 4),
        array(0 => 99, 1 => 4),
        array(0 => 100, 1 => 3),
        array(0 => 100, 1 => 1),
        array(0 => 100, 1 => 0),
        array(0 => 77, 1 => 3),
        array(0 => 77, 1 => 2),
        array(0 => 101, 1 => 3),
        array(0 => 101, 1 => 2),
        array(0 => 82, 1 => 2),
        array(0 => 82, 1 => 0),
        array(0 => 102, 1 => 2),
        array(0 => 102, 1 => 2),
        array(0 => 92, 1 => 1),
        array(0 => 92, 1 => 2),
        array(0 => 92, 1 => 1),
        array(0 => 92, 1 => 2),
        array(0 => 92, 1 => 3),
        array(0 => 87, 1 => 1),
        array(0 => 87, 1 => 1),
        array(0 => 86, 1 => 1),
        array(0 => 88, 1 => 1),
        array(0 => 85, 1 => 3),
        array(0 => 103, 1 => 1),
        array(0 => 103, 1 => 3),
        array(0 => 103, 1 => 0),
        array(0 => 104, 1 => 3),
        array(0 => 104, 1 => 3),
        array(0 => 104, 1 => 1),
        array(0 => 91, 1 => 2),
        array(0 => 91, 1 => 3),
        array(0 => 105, 1 => 2),
        array(0 => 105, 1 => 1),
        array(0 => 106, 1 => 3),
        array(0 => 106, 1 => 3),
        array(0 => 106, 1 => 1),
        array(0 => 106, 1 => 3),
        array(0 => 106, 1 => 3),
        array(0 => 106, 1 => 1),
        array(0 => 106, 1 => 1),
    );

    public static $yyReduceMap      = array(
        0   => 0,
        1   => 1,
        2   => 2,
        3   => 3,
        4   => 4,
        5   => 5,
        6   => 6,
        7   => 7,
        22  => 7,
        23  => 7,
        24  => 7,
        37  => 7,
        57  => 7,
        58  => 7,
        66  => 7,
        67  => 7,
        71  => 7,
        80  => 7,
        85  => 7,
        86  => 7,
        91  => 7,
        95  => 7,
        96  => 7,
        100 => 7,
        102 => 7,
        107 => 7,
        169 => 7,
        174 => 7,
        8   => 8,
        9   => 9,
        10  => 10,
        12  => 12,
        13  => 13,
        14  => 14,
        15  => 15,
        16  => 16,
        17  => 17,
        18  => 18,
        19  => 19,
        20  => 20,
        21  => 21,
        25  => 25,
        26  => 26,
        27  => 27,
        28  => 28,
        29  => 29,
        30  => 30,
        31  => 31,
        32  => 32,
        34  => 32,
        33  => 33,
        35  => 35,
        36  => 36,
        38  => 38,
        39  => 39,
        40  => 40,
        41  => 41,
        42  => 42,
        43  => 43,
        44  => 44,
        45  => 45,
        46  => 46,
        47  => 47,
        48  => 48,
        49  => 49,
        50  => 50,
        51  => 51,
        60  => 51,
        149 => 51,
        153 => 51,
        157 => 51,
        158 => 51,
        52  => 52,
        150 => 52,
        156 => 52,
        53  => 53,
        54  => 54,
        55  => 54,
        56  => 56,
        134 => 56,
        59  => 59,
        61  => 61,
        62  => 62,
        63  => 62,
        64  => 64,
        65  => 65,
        68  => 68,
        69  => 69,
        70  => 69,
        72  => 72,
        99  => 72,
        73  => 73,
        74  => 74,
        75  => 75,
        76  => 76,
        77  => 77,
        78  => 78,
        79  => 79,
        81  => 81,
        83  => 81,
        84  => 81,
        114 => 81,
        82  => 82,
        87  => 87,
        88  => 88,
        89  => 89,
        90  => 90,
        92  => 92,
        93  => 93,
        94  => 93,
        97  => 97,
        98  => 98,
        101 => 101,
        103 => 103,
        104 => 104,
        105 => 105,
        106 => 106,
        108 => 108,
        109 => 109,
        110 => 110,
        111 => 111,
        112 => 112,
        113 => 113,
        115 => 115,
        171 => 115,
        116 => 116,
        117 => 117,
        118 => 118,
        119 => 119,
        120 => 120,
        121 => 121,
        129 => 121,
        122 => 122,
        123 => 123,
        124 => 124,
        125 => 124,
        127 => 124,
        128 => 124,
        126 => 126,
        130 => 130,
        131 => 131,
        132 => 132,
        175 => 132,
        133 => 133,
        135 => 135,
        136 => 136,
        137 => 137,
        138 => 138,
        139 => 139,
        140 => 140,
        141 => 141,
        142 => 142,
        143 => 143,
        144 => 144,
        145 => 145,
        146 => 146,
        147 => 147,
        148 => 148,
        151 => 151,
        152 => 152,
        154 => 154,
        155 => 155,
        159 => 159,
        160 => 160,
        161 => 161,
        162 => 162,
        163 => 163,
        164 => 164,
        165 => 165,
        166 => 166,
        167 => 167,
        168 => 168,
        170 => 170,
        172 => 172,
        173 => 173,
        176 => 176,
        177 => 177,
        178 => 178,
        179 => 179,
        182 => 179,
        180 => 180,
        183 => 180,
        181 => 181,
        184 => 184,
        185 => 185,
    );

    /**
     * result status
     *
     * @var bool
     */
    public $successful = true;

    /**
     * return value
     *
     * @var mixed
     */
    public $retvalue = 0;

    /**
     * @var
     */
    public $yymajor;

    /**
     * last index of array variable
     *
     * @var mixed
     */
    public $last_index;

    /**
     * last variable name
     *
     * @var string
     */
    public $last_variable;

    /**
     * root parse tree buffer
     *
     * @var Smarty_Internal_ParseTree_Template
     */
    public $root_buffer;

    /**
     * current parse tree object
     *
     * @var Smarty_Internal_ParseTree
     */
    public $current_buffer;

    /**
     * lexer object
     *
     * @var Smarty_Internal_Templatelexer
     */
    public $lex;

    /**
     * {strip} status
     *
     * @var bool
     */
    public $strip = false;

    /**
     * compiler object
     *
     * @var Smarty_Internal_TemplateCompilerBase
     */
    public $compiler = null;

    /**
     * smarty object
     *
     * @var Smarty
     */
    public $smarty = null;

    /**
     * template object
     *
     * @var Smarty_Internal_Template
     */
    public $template = null;

    /**
     * block nesting level
     *
     * @var int
     */
    public $block_nesting_level = 0;

    /**
     * security object
     *
     * @var Smarty_Security
     */
    public $security = null;

    /**
     * template prefix array
     *
     * @var \Smarty_Internal_ParseTree[]
     */
    public $template_prefix = array();

    /**
     * template prefix array
     *
     * @var \Smarty_Internal_ParseTree[]
     */
    public $template_postfix = array();

    public $yyTraceFILE;

    public $yyTracePrompt;

    public $yyidx;

    public $yyerrcnt;

    public $yystack          = array();

    public $yyTokenName      = array(
        '$', 'VERT', 'COLON', 'UNIMATH',
        'PHP', 'TEXT', 'STRIPON', 'STRIPOFF',
        'LITERALSTART', 'LITERALEND', 'LITERAL', 'SIMPELOUTPUT',
        'SIMPLETAG', 'SMARTYBLOCKCHILDPARENT', 'LDEL', 'RDEL',
        'DOLLARID', 'EQUAL', 'ID', 'PTR',
        'LDELMAKENOCACHE', 'LDELIF', 'LDELFOR', 'SEMICOLON',
        'INCDEC', 'TO', 'STEP', 'LDELFOREACH',
        'SPACE', 'AS', 'APTR', 'LDELSETFILTER',
        'CLOSETAG', 'LDELSLASH', 'ATTR', 'INTEGER',
        'COMMA', 'OPENP', 'CLOSEP', 'MATH',
        'ISIN', 'QMARK', 'NOT', 'TYPECAST',
        'HEX', 'DOT', 'INSTANCEOF', 'SINGLEQUOTESTRING',
        'DOUBLECOLON', 'NAMESPACE', 'AT', 'HATCH',
        'OPENB', 'CLOSEB', 'DOLLAR', 'LOGOP',
        'SLOGOP', 'TLOGOP', 'SINGLECOND', 'QUOTE',
        'BACKTICK', 'error', 'start', 'template',
        'literal_e2', 'literal_e1', 'smartytag', 'tagbody',
        'tag', 'outattr', 'eqoutattr', 'varindexed',
        'output', 'attributes', 'variable', 'value',
        'expr', 'modifierlist', 'statement', 'statements',
        'foraction', 'varvar', 'modparameters', 'attribute',
        'ternary', 'array', 'tlop', 'lop',
        'scond', 'function', 'ns1', 'doublequoted_with_quotes',
        'static_class_access', 'object', 'arrayindex', 'indexdef',
        'varvarele', 'objectchain', 'objectelement', 'method',
        'params', 'modifier', 'modparameter', 'arrayelements',
        'arrayelement', 'doublequoted', 'doublequotedcontent',
    );

    /**
     * internal error flag
     *
     * @var bool
     */
    private $internalError = false;                    /* Index of top element in stack */
    private $_retvalue;                 /* Shifts left before out of the error */
    /**
     * constructor
     *
     * @param Smarty_Internal_Templatelexer        $lex
     * @param Smarty_Internal_TemplateCompilerBase $compiler
     */
    public function __construct(Smarty_Internal_Templatelexer $lex, Smarty_Internal_TemplateCompilerBase $compiler)
    {
        $this->lex = $lex;
        $this->compiler = $compiler;
        $this->template = $this->compiler->template;
        $this->smarty = $this->template->smarty;
        $this->security = isset($this->smarty->security_policy) ? $this->smarty->security_policy : false;
        $this->current_buffer = $this->root_buffer = new Smarty_Internal_ParseTree_Template();
    }  /* The parser's stack */
    public static function yy_destructor($yymajor, $yypminor)
    {
        switch ($yymajor) {
            default:
                break;   /* If no destructor action specified: do nothing */
        }
    }

    /**
     * insert PHP code in current buffer
     *
     * @param string $code
     */
    public function insertPhpCode($code)
    {
        $this->current_buffer->append_subtree($this, new Smarty_Internal_ParseTree_Tag($this, $code));
    }

    /**
     * error rundown
     *
     */
    public function errorRunDown()
    {
        while ($this->yystack !== array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    /**
     *  merge PHP code with prefix code and return parse tree tag object
     *
     * @param string $code
     *
     * @return Smarty_Internal_ParseTree_Tag
     */
    public function mergePrefixCode($code)
    {
        $tmp = '';
        foreach ($this->compiler->prefix_code as $preCode) {
            $tmp .= $preCode;
        }
        $this->compiler->prefix_code = array();
        $tmp .= $code;
        return new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp, true));
    }

    public function Trace($TraceFILE, $zTracePrompt)
    {
        if (!$TraceFILE) {
            $zTracePrompt = 0;
        } elseif (!$zTracePrompt) {
            $TraceFILE = 0;
        }
        $this->yyTraceFILE = $TraceFILE;
        $this->yyTracePrompt = $zTracePrompt;
    }

    public function PrintTrace()
    {
        $this->yyTraceFILE = fopen('php://output', 'w');
        $this->yyTracePrompt = '<br>';
    }

    public function tokenName($tokenType)
    {
        if ($tokenType === 0) {
            return 'End of Input';
        }
        if ($tokenType > 0 && $tokenType < count($this->yyTokenName)) {
            return $this->yyTokenName[ $tokenType ];
        } else {
            return 'Unknown';
        }
    }

    public function yy_pop_parser_stack()
    {
        if (empty($this->yystack)) {
            return;
        }
        $yytos = array_pop($this->yystack);
        if ($this->yyTraceFILE && $this->yyidx >= 0) {
            fwrite(
                $this->yyTraceFILE,
                $this->yyTracePrompt . 'Popping ' . $this->yyTokenName[ $yytos->major ] .
                "\n"
            );
        }
        $yymajor = $yytos->major;
        self::yy_destructor($yymajor, $yytos->minor);
        $this->yyidx--;
        return $yymajor;
    }

    public function __destruct()
    {
        while ($this->yystack !== array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    public function yy_get_expected_tokens($token)
    {
        static $res3 = array();
        static $res4 = array();
        $state = $this->yystack[ $this->yyidx ]->stateno;
        $expected = self::$yyExpectedTokens[ $state ];
        if (isset($res3[ $state ][ $token ])) {
            if ($res3[ $state ][ $token ]) {
                return $expected;
            }
        } else {
            if ($res3[ $state ][ $token ] = in_array($token, self::$yyExpectedTokens[ $state ], true)) {
                return $expected;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done++ === 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return array_unique($expected);
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[ $yyruleno ][ 1 ];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[ $this->yyidx ]->stateno,
                        self::$yyRuleInfo[ $yyruleno ][ 0 ]
                    );
                    if (isset(self::$yyExpectedTokens[ $nextstate ])) {
                        $expected = array_merge($expected, self::$yyExpectedTokens[ $nextstate ]);
                        if (isset($res4[ $nextstate ][ $token ])) {
                            if ($res4[ $nextstate ][ $token ]) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        } else {
                            if ($res4[ $nextstate ][ $token ] =
                                in_array($token, self::$yyExpectedTokens[ $nextstate ], true)) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[ $yyruleno ][ 0 ];
                        $this->yystack[ $this->yyidx ] = $x;
                        continue 2;
                    } elseif ($nextstate === self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return array_unique($expected);
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return $expected;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;
        return array_unique($expected);
    }

    public function yy_is_expected_token($token)
    {
        static $res = array();
        static $res2 = array();
        if ($token === 0) {
            return true; // 0 is not part of this
        }
        $state = $this->yystack[ $this->yyidx ]->stateno;
        if (isset($res[ $state ][ $token ])) {
            if ($res[ $state ][ $token ]) {
                return true;
            }
        } else {
            if ($res[ $state ][ $token ] = in_array($token, self::$yyExpectedTokens[ $state ], true)) {
                return true;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done++ === 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return true;
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[ $yyruleno ][ 1 ];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[ $this->yyidx ]->stateno,
                        self::$yyRuleInfo[ $yyruleno ][ 0 ]
                    );
                    if (isset($res2[ $nextstate ][ $token ])) {
                        if ($res2[ $nextstate ][ $token ]) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    } else {
                        if ($res2[ $nextstate ][ $token ] =
                            (isset(self::$yyExpectedTokens[ $nextstate ]) &&
                             in_array($token, self::$yyExpectedTokens[ $nextstate ], true))) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[ $yyruleno ][ 0 ];
                        $this->yystack[ $this->yyidx ] = $x;
                        continue 2;
                    } elseif ($nextstate === self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        if (!$token) {
                            // end of input: this is valid
                            return true;
                        }
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return false;
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return true;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;
        return true;
    }

    public function yy_find_shift_action($iLookAhead)
    {
        $stateno = $this->yystack[ $this->yyidx ]->stateno;
        /* if ($this->yyidx < 0) return self::YY_NO_ACTION;  */
        if (!isset(self::$yy_shift_ofst[ $stateno ])) {
            // no shift actions
            return self::$yy_default[ $stateno ];
        }
        $i = self::$yy_shift_ofst[ $stateno ];
        if ($i === self::YY_SHIFT_USE_DFLT) {
            return self::$yy_default[ $stateno ];
        }
        if ($iLookAhead === self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
            self::$yy_lookahead[ $i ] != $iLookAhead) {
            if (count(self::$yyFallback) && $iLookAhead < count(self::$yyFallback)
                && ($iFallback = self::$yyFallback[ $iLookAhead ]) != 0) {
                if ($this->yyTraceFILE) {
                    fwrite($this->yyTraceFILE, $this->yyTracePrompt . 'FALLBACK ' .
                                               $this->yyTokenName[ $iLookAhead ] . ' => ' .
                                               $this->yyTokenName[ $iFallback ] . "\n");
                }
                return $this->yy_find_shift_action($iFallback);
            }
            return self::$yy_default[ $stateno ];
        } else {
            return self::$yy_action[ $i ];
        }
    }

    public function yy_find_reduce_action($stateno, $iLookAhead)
    {
        /* $stateno = $this->yystack[$this->yyidx]->stateno; */
        if (!isset(self::$yy_reduce_ofst[ $stateno ])) {
            return self::$yy_default[ $stateno ];
        }
        $i = self::$yy_reduce_ofst[ $stateno ];
        if ($i === self::YY_REDUCE_USE_DFLT) {
            return self::$yy_default[ $stateno ];
        }
        if ($iLookAhead === self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
            self::$yy_lookahead[ $i ] != $iLookAhead) {
            return self::$yy_default[ $stateno ];
        } else {
            return self::$yy_action[ $i ];
        }
    }

    // line 234 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_shift($yyNewState, $yyMajor, $yypMinor)
    {
        $this->yyidx++;
        if ($this->yyidx >= self::YYSTACKDEPTH) {
            $this->yyidx--;
            if ($this->yyTraceFILE) {
                fprintf($this->yyTraceFILE, "%sStack Overflow!\n", $this->yyTracePrompt);
            }
            while ($this->yyidx >= 0) {
                $this->yy_pop_parser_stack();
            }
            // line 221 "../smarty/lexer/smarty_internal_templateparser.y"
            $this->internalError = true;
            $this->compiler->trigger_template_error('Stack overflow in template parser');
            return;
        }
        $yytos = new TP_yyStackEntry;
        $yytos->stateno = $yyNewState;
        $yytos->major = $yyMajor;
        $yytos->minor = $yypMinor;
        $this->yystack[] = $yytos;
        if ($this->yyTraceFILE && $this->yyidx > 0) {
            fprintf(
                $this->yyTraceFILE,
                "%sShift %d\n",
                $this->yyTracePrompt,
                $yyNewState
            );
            fprintf($this->yyTraceFILE, "%sStack:", $this->yyTracePrompt);
            for ($i = 1; $i <= $this->yyidx; $i++) {
                fprintf(
                    $this->yyTraceFILE,
                    " %s",
                    $this->yyTokenName[ $this->yystack[ $i ]->major ]
                );
            }
            fwrite($this->yyTraceFILE, "\n");
        }
    }

    // line 242 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r0()
    {
        $this->root_buffer->prepend_array($this, $this->template_prefix);
        $this->root_buffer->append_array($this, $this->template_postfix);
        $this->_retvalue = $this->root_buffer->to_smarty_php($this);
    }

    // line 251 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r1()
    {
        $code =
            $this->compiler->compileTag('private_php',
                array(array('code' => $this->yystack[ $this->yyidx + 0 ]->minor), array('type' => $this->lex->phpType)),
                array());
        if ($this->compiler->has_code && !empty($code)) {
            $tmp = '';
            foreach ($this->compiler->prefix_code as $code) {
                $tmp .= $code;
            }
            $this->compiler->prefix_code = array();
            $this->current_buffer->append_subtree($this,
                new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp . $code, true)));
        }
    }

    // line 255 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r2()
    {
        $this->current_buffer->append_subtree($this,
            $this->compiler->processText($this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 259 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r3()
    {
        $this->strip = true;
    }

    // line 264 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r4()
    {
        $this->strip = false;
    }

    // line 269 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r5()
    {
        $this->current_buffer->append_subtree($this,
            new Smarty_Internal_ParseTree_Text($this->yystack[ $this->yyidx + -1 ]->minor));
    }

    // line 272 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r6()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -3 ]->minor . $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 276 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r7()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 281 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r8()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 285 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r9()
    {
        $this->_retvalue = '';
    }

    // line 297 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r10()
    {
        if ($this->compiler->has_code) {
            $this->current_buffer->append_subtree($this,
                $this->mergePrefixCode($this->yystack[ $this->yyidx + 0 ]->minor));
        }
        $this->compiler->has_variable_string = false;
        $this->block_nesting_level = count($this->compiler->_tag_stack);
    }

    // line 307 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r12()
    {
        $var =
            trim(substr($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler->getLdelLength(),
                -$this->compiler->getRdelLength()), ' $');
        if (preg_match('/^(.*)(\s+nocache)$/', $var, $match)) {
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', array('nocache'),
                    array('value' => $this->compiler->compileVariable('\'' . $match[ 1 ] . '\'')));
        } else {
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', array(),
                    array('value' => $this->compiler->compileVariable('\'' . $var . '\'')));
        }
    }

    // line 328 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r13()
    {
        $tag =
            trim(substr($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler->getLdelLength(),
                -$this->compiler->getRdelLength()));
        if ($tag == 'strip') {
            $this->strip = true;
            $this->_retvalue = null;;
        } else {
            if (defined($tag)) {
                if ($this->security) {
                    $this->security->isTrustedConstant($tag, $this->compiler);
                }
                $this->_retvalue =
                    $this->compiler->compileTag('private_print_expression', array(), array('value' => $tag));
            } else {
                if (preg_match('/^(.*)(\s+nocache)$/', $tag, $match)) {
                    $this->_retvalue = $this->compiler->compileTag($match[ 1 ], array('\'nocache\''));
                } else {
                    $this->_retvalue = $this->compiler->compileTag($tag, array());
                }
            }
        }
    }

    // line 339 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r14()
    {
        $j = strrpos($this->yystack[ $this->yyidx + 0 ]->minor, '.');
        if ($this->yystack[ $this->yyidx + 0 ]->minor[ $j + 1 ] == 'c') {
            // {$smarty.block.child}
            $this->_retvalue =
                $this->compiler->compileTag('child', array(), array($this->yystack[ $this->yyidx + 0 ]->minor));
        } else {
            // {$smarty.block.parent}
            $this->_retvalue =
                $this->compiler->compileTag('parent', array(), array($this->yystack[ $this->yyidx + 0 ]->minor));
        }
    }

    // line 343 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r15()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 347 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r16()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 356 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r17()
    {
        $this->_retvalue =
            $this->compiler->compileTag('private_print_expression', $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ],
                array('value' => $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ]));
    }

    // line 360 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r18()
    {
        $this->_retvalue =
            $this->compiler->compileTag('assign', array_merge(array(
                array('value' => $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ]),
                array('var' => '\'' . substr($this->yystack[ $this->yyidx + -1 ]->minor, 1) . '\'')
            ), $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ]));
    }

    // line 364 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r19()
    {
        $this->_retvalue =
            $this->compiler->compileTag('assign', array_merge(array(
                array('value' => $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ]),
                array('var' => $this->yystack[ $this->yyidx + -1 ]->minor[ 'var' ])
            ), $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ]), array(
                'smarty_internal_index' => $this->yystack[ $this->yyidx +
                                                           -1 ]->minor[ 'smarty_internal_index' ]
            ));
    }

    // line 368 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r20()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 383 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r21()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 393 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r25()
    {
        if (defined($this->yystack[ $this->yyidx + -1 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + -1 ]->minor, $this->compiler);
            }
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', $this->yystack[ $this->yyidx + 0 ]->minor,
                    array('value' => $this->yystack[ $this->yyidx + -1 ]->minor));
        } else {
            $this->_retvalue =
                $this->compiler->compileTag($this->yystack[ $this->yyidx + -1 ]->minor,
                    $this->yystack[ $this->yyidx + 0 ]->minor);
        }
    }

    // line 406 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r26()
    {
        if (defined($this->yystack[ $this->yyidx + 0 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler);
            }
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', array(),
                    array('value' => $this->yystack[ $this->yyidx + 0 ]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[ $this->yyidx + 0 ]->minor, array());
        }
    }

    // line 418 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r27()
    {
        if (defined($this->yystack[ $this->yyidx + -2 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + -2 ]->minor, $this->compiler);
            }
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', $this->yystack[ $this->yyidx + 0 ]->minor,
                    array(
                        'value'        => $this->yystack[ $this->yyidx + -2 ]->minor,
                        'modifierlist' => $this->yystack[ $this->yyidx + -1 ]->minor
                    ));
        } else {
            $this->_retvalue =
                $this->compiler->compileTag($this->yystack[ $this->yyidx + -2 ]->minor,
                    $this->yystack[ $this->yyidx + 0 ]->minor,
                    array('modifierlist' => $this->yystack[ $this->yyidx + -1 ]->minor));
        }
    }

    // line 423 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r28()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -3 ]->minor,
                $this->yystack[ $this->yyidx + 0 ]->minor,
                array('object_method' => $this->yystack[ $this->yyidx + -1 ]->minor));
    }

    // line 428 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r29()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -4 ]->minor,
                $this->yystack[ $this->yyidx + 0 ]->minor, array(
                    'modifierlist'  => $this->yystack[ $this->yyidx + -1 ]->minor,
                    'object_method' => $this->yystack[ $this->yyidx + -2 ]->minor
                ));
    }

    // line 433 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r30()
    {
        $this->_retvalue =
            $this->compiler->compileTag('make_nocache',
                array(array('var' => '\'' . substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) . '\'')));
    }

    // line 438 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r31()
    {
        $tag = trim(substr($this->yystack[ $this->yyidx + -1 ]->minor, $this->compiler->getLdelLength()));
        $this->_retvalue =
            $this->compiler->compileTag(($tag === 'else if') ? 'elseif' : $tag, array(),
                array('if condition' => $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 443 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r32()
    {
        $tag = trim(substr($this->yystack[ $this->yyidx + -2 ]->minor, $this->compiler->getLdelLength()));
        $this->_retvalue =
            $this->compiler->compileTag(($tag === 'else if') ? 'elseif' : $tag,
                $this->yystack[ $this->yyidx + 0 ]->minor,
                array('if condition' => $this->yystack[ $this->yyidx + -1 ]->minor));
    }

    // line 454 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r33()
    {
        $tag = trim(substr($this->yystack[ $this->yyidx + -1 ]->minor, $this->compiler->getLdelLength()));
        $this->_retvalue =
            $this->compiler->compileTag(($tag === 'else if') ? 'elseif' : $tag, array(),
                array('if condition' => $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 458 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r35()
    {
        $this->_retvalue =
            $this->compiler->compileTag('for', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('start' => $this->yystack[ $this->yyidx + -6 ]->minor),
                array('ifexp' => $this->yystack[ $this->yyidx + -4 ]->minor),
                array('var' => $this->yystack[ $this->yyidx + -2 ]->minor),
                array('step' => $this->yystack[ $this->yyidx + -1 ]->minor)
            )), 1);
    }

    // line 466 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r36()
    {
        $this->_retvalue = '=' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 470 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r38()
    {
        $this->_retvalue =
            $this->compiler->compileTag('for', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('start' => $this->yystack[ $this->yyidx + -3 ]->minor),
                array('to' => $this->yystack[ $this->yyidx + -1 ]->minor)
            )), 0);
    }

    // line 475 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r39()
    {
        $this->_retvalue =
            $this->compiler->compileTag('for', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('start' => $this->yystack[ $this->yyidx + -5 ]->minor),
                array('to' => $this->yystack[ $this->yyidx + -3 ]->minor),
                array('step' => $this->yystack[ $this->yyidx + -1 ]->minor)
            )), 0);
    }

    // line 479 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r40()
    {
        $this->_retvalue =
            $this->compiler->compileTag('foreach', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('from' => $this->yystack[ $this->yyidx + -3 ]->minor),
                array('item' => $this->yystack[ $this->yyidx + -1 ]->minor)
            )));
    }

    // line 482 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r41()
    {
        $this->_retvalue =
            $this->compiler->compileTag('foreach', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('from' => $this->yystack[ $this->yyidx + -5 ]->minor),
                array('item' => $this->yystack[ $this->yyidx + -1 ]->minor),
                array('key' => $this->yystack[ $this->yyidx + -3 ]->minor)
            )));
    }

    // line 487 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r42()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 491 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r43()
    {
        $this->_retvalue =
            $this->compiler->compileTag('setfilter', array(), array(
                'modifier_list' => array(
                    array_merge(array($this->yystack[ $this->yyidx + -1 ]->minor),
                        $this->yystack[ $this->yyidx + 0 ]->minor)
                )
            ));
    }

    // line 497 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r44()
    {
        $this->_retvalue =
            $this->compiler->compileTag('setfilter', array(), array(
                'modifier_list' => array_merge(array(
                    array_merge(array(
                        $this->yystack[ $this->yyidx +
                                        -2 ]->minor
                    ), $this->yystack[ $this->yyidx + -1 ]->minor)
                ), $this->yystack[ $this->yyidx + 0 ]->minor)
            ));
    }

    // line 506 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r45()
    {
        $tag =
            trim(substr($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler->getLdelLength(),
                -$this->compiler->getRdelLength()), ' /');
        if ($tag === 'strip') {
            $this->strip = false;
            $this->_retvalue = null;
        } else {
            $this->_retvalue = $this->compiler->compileTag($tag . 'close', array());
        }
    }

    // line 510 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r46()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[ $this->yyidx + 0 ]->minor . 'close', array());
    }

    // line 515 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r47()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -1 ]->minor . 'close', array(),
                array('modifier_list' => $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 519 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r48()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -2 ]->minor . 'close', array(),
                array('object_method' => $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 527 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r49()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -3 ]->minor . 'close', array(), array(
                'object_method' => $this->yystack[ $this->yyidx + -1 ]->minor,
                'modifier_list' => $this->yystack[ $this->yyidx + 0 ]->minor
            ));
    }

    // line 533 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r50()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
        $this->_retvalue[] = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 538 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r51()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 543 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r52()
    {
        $this->_retvalue = array();
    }

    // line 554 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r53()
    {
        if (defined($this->yystack[ $this->yyidx + 0 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler);
            }
            $this->_retvalue =
                array($this->yystack[ $this->yyidx + -2 ]->minor => $this->yystack[ $this->yyidx + 0 ]->minor);
        } else {
            $this->_retvalue =
                array(
                    $this->yystack[ $this->yyidx + -2 ]->minor => '\'' .
                                                                  $this->yystack[ $this->yyidx + 0 ]->minor .
                                                                  '\''
                );
        }
    }

    // line 562 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r54()
    {
        $this->_retvalue =
            array(
                trim($this->yystack[ $this->yyidx + -1 ]->minor, " =\n\r\t") => $this->yystack[ $this->yyidx +
                                                                                                0 ]->minor
            );
    }

    // line 574 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r56()
    {
        $this->_retvalue = '\'' . $this->yystack[ $this->yyidx + 0 ]->minor . '\'';
    }

    // line 587 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r59()
    {
        $this->_retvalue =
            array($this->yystack[ $this->yyidx + -2 ]->minor => $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 592 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r61()
    {
        $this->yystack[ $this->yyidx + -2 ]->minor[] = $this->yystack[ $this->yyidx + 0 ]->minor;
        $this->_retvalue = $this->yystack[ $this->yyidx + -2 ]->minor;
    }

    // line 599 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r62()
    {
        $this->_retvalue =
            array(
                'var'   => '\'' . substr($this->yystack[ $this->yyidx + -2 ]->minor, 1) . '\'',
                'value' => $this->yystack[ $this->yyidx + 0 ]->minor
            );
    }

    // line 603 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r64()
    {
        $this->_retvalue =
            array(
                'var'   => $this->yystack[ $this->yyidx + -2 ]->minor,
                'value' => $this->yystack[ $this->yyidx + 0 ]->minor
            );
    }

    // line 623 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r65()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 628 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r68()
    {
        $this->_retvalue =
            '$_smarty_tpl->getStreamVariable(\'' .
            substr($this->yystack[ $this->yyidx + -2 ]->minor, 1) .
            '://' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            '\')';
    }

    // line 642 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r69()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -2 ]->minor .
            trim($this->yystack[ $this->yyidx + -1 ]->minor) .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 648 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r72()
    {
        $this->_retvalue =
            $this->compiler->compileTag('private_modifier', array(), array(
                'value'        => $this->yystack[ $this->yyidx + -1 ]->minor,
                'modifierlist' => $this->yystack[ $this->yyidx + 0 ]->minor
            ));
    }

    // line 652 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r73()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -1 ]->minor[ 'pre' ] .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            $this->yystack[ $this->yyidx + -1 ]->minor[ 'op' ] .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ')';
    }

    // line 656 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r74()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -2 ]->minor .
            $this->yystack[ $this->yyidx + -1 ]->minor .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 660 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r75()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor . $this->yystack[ $this->yyidx + -1 ]->minor . ')';
    }

    // line 664 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r76()
    {
        $this->_retvalue =
            'in_array(' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            ',' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ')';
    }

    // line 672 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r77()
    {
        $this->_retvalue =
            'in_array(' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            ',(array)' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ')';
    }

    // line 676 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r78()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -5 ]->minor .
            ' ? ' .
            $this->compiler->compileVariable('\'' . substr($this->yystack[ $this->yyidx + -2 ]->minor, 1) . '\'') .
            ' : ' .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 686 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r79()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -5 ]->minor .
            ' ? ' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            ' : ' .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 691 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r81()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 712 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r82()
    {
        $this->_retvalue = '!' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 716 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r87()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -2 ]->minor . '.' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 720 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r88()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . '.';
    }

    // line 725 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r89()
    {
        $this->_retvalue = '.' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 742 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r90()
    {
        if (defined($this->yystack[ $this->yyidx + 0 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler);
            }
            $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
        } else {
            $this->_retvalue = '\'' . $this->yystack[ $this->yyidx + 0 ]->minor . '\'';
        }
    }

    // line 746 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r92()
    {
        $this->_retvalue = '(' . $this->yystack[ $this->yyidx + -1 ]->minor . ')';
    }

    // line 764 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r93()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -2 ]->minor .
            $this->yystack[ $this->yyidx + -1 ]->minor .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 775 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r97()
    {
        $prefixVar = $this->compiler->getNewPrefixVariable();
        if ($this->yystack[ $this->yyidx + -2 ]->minor[ 'var' ] === '\'smarty\'') {
            $this->compiler->appendPrefixCode("<?php {$prefixVar} = " .
                                              $this->compiler->compileTag('private_special_variable', array(),
                                                  $this->yystack[ $this->yyidx +
                                                                  -2 ]->minor[ 'smarty_internal_index' ]) .
                                              ';?>');
        } else {
            $this->compiler->appendPrefixCode("<?php  {$prefixVar} = " .
                                              $this->compiler->compileVariable($this->yystack[ $this->yyidx +
                                                                                               -2 ]->minor[ 'var' ]) .
                                              $this->yystack[ $this->yyidx + -2 ]->minor[ 'smarty_internal_index' ] .
                                              ';?>');
        }
        $this->_retvalue =
            $prefixVar .
            '::' .
            $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ] .
            $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ];
    }

    // line 792 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r98()
    {
        $prefixVar = $this->compiler->getNewPrefixVariable();
        $tmp = $this->compiler->appendCode('<?php ob_start();?>', $this->yystack[ $this->yyidx + 0 ]->minor);
        $this->compiler->appendPrefixCode($this->compiler->appendCode($tmp, "<?php {$prefixVar} = ob_get_clean();?>"));
        $this->_retvalue = $prefixVar;
    }

    // line 811 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r101()
    {
        if (!in_array(strtolower($this->yystack[ $this->yyidx + -2 ]->minor), array('self', 'parent')) &&
            (!$this->security ||
             $this->security->isTrustedStaticClassAccess($this->yystack[ $this->yyidx + -2 ]->minor,
                 $this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler))) {
            if (isset($this->smarty->registered_classes[ $this->yystack[ $this->yyidx + -2 ]->minor ])) {
                $this->_retvalue =
                    $this->smarty->registered_classes[ $this->yystack[ $this->yyidx + -2 ]->minor ] .
                    '::' .
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ] .
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ];
            } else {
                $this->_retvalue =
                    $this->yystack[ $this->yyidx + -2 ]->minor .
                    '::' .
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ] .
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ];
            }
        } else {
            $this->compiler->trigger_template_error('static class \'' .
                                                    $this->yystack[ $this->yyidx + -2 ]->minor .
                                                    '\' is undefined or not allowed by security setting');
        }
    }

    // line 822 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r103()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 825 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r104()
    {
        $this->_retvalue =
            $this->compiler->compileVariable('\'' . substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) . '\'');
    }

    // line 838 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r105()
    {
        if ($this->yystack[ $this->yyidx + 0 ]->minor[ 'var' ] === '\'smarty\'') {
            $smarty_var =
                $this->compiler->compileTag('private_special_variable', array(),
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 'smarty_internal_index' ]);
            $this->_retvalue = $smarty_var;
        } else {
            // used for array reset,next,prev,end,current
            $this->last_variable = $this->yystack[ $this->yyidx + 0 ]->minor[ 'var' ];
            $this->last_index = $this->yystack[ $this->yyidx + 0 ]->minor[ 'smarty_internal_index' ];
            $this->_retvalue =
                $this->compiler->compileVariable($this->yystack[ $this->yyidx + 0 ]->minor[ 'var' ]) .
                $this->yystack[ $this->yyidx + 0 ]->minor[ 'smarty_internal_index' ];
        }
    }

    // line 848 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r106()
    {
        $this->_retvalue =
            '$_smarty_tpl->tpl_vars[' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            ']->' .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 852 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r108()
    {
        $this->_retvalue =
            $this->compiler->compileConfigVariable('\'' . $this->yystack[ $this->yyidx + -1 ]->minor . '\'');
    }

    // line 856 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r109()
    {
        $this->_retvalue =
            '(is_array($tmp = ' .
            $this->compiler->compileConfigVariable('\'' . $this->yystack[ $this->yyidx + -2 ]->minor . '\'') .
            ') ? $tmp' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ' :null)';
    }

    // line 860 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r110()
    {
        $this->_retvalue = $this->compiler->compileConfigVariable($this->yystack[ $this->yyidx + -1 ]->minor);
    }

    // line 864 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r111()
    {
        $this->_retvalue =
            '(is_array($tmp = ' .
            $this->compiler->compileConfigVariable($this->yystack[ $this->yyidx + -2 ]->minor) .
            ') ? $tmp' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ' : null)';
    }

    // line 867 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r112()
    {
        $this->_retvalue =
            array(
                'var'                   => '\'' . substr($this->yystack[ $this->yyidx + -1 ]->minor, 1) . '\'',
                'smarty_internal_index' => $this->yystack[ $this->yyidx + 0 ]->minor
            );
    }

    // line 880 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r113()
    {
        $this->_retvalue =
            array(
                'var'                   => $this->yystack[ $this->yyidx + -1 ]->minor,
                'smarty_internal_index' => $this->yystack[ $this->yyidx + 0 ]->minor
            );
    }

    // line 886 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r115()
    {
        return;
    }

    // line 889 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r116()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileVariable('\'' . substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) . '\'') .
            ']';
    }

    // line 893 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r117()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable($this->yystack[ $this->yyidx + 0 ]->minor) . ']';
    }

    // line 897 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r118()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileVariable($this->yystack[ $this->yyidx + -2 ]->minor) .
            '->' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ']';
    }

    // line 901 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r119()
    {
        $this->_retvalue = '[\'' . $this->yystack[ $this->yyidx + 0 ]->minor . '\']';
    }

    // line 906 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r120()
    {
        $this->_retvalue = '[' . $this->yystack[ $this->yyidx + 0 ]->minor . ']';
    }

    // line 911 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r121()
    {
        $this->_retvalue = '[' . $this->yystack[ $this->yyidx + -1 ]->minor . ']';
    }

    // line 915 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r122()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileTag('private_special_variable', array(),
                '[\'section\'][\'' . $this->yystack[ $this->yyidx + -1 ]->minor . '\'][\'index\']') .
            ']';
    }

    // line 918 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r123()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileTag('private_special_variable', array(), '[\'section\'][\'' .
                                                                             $this->yystack[ $this->yyidx +
                                                                                             -3 ]->minor .
                                                                             '\'][\'' .
                                                                             $this->yystack[ $this->yyidx +
                                                                                             -1 ]->minor .
                                                                             '\']') .
            ']';
    }

    // line 924 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r124()
    {
        $this->_retvalue = '[' . $this->yystack[ $this->yyidx + -1 ]->minor . ']';
    }

    // line 940 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r126()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileVariable('\'' . substr($this->yystack[ $this->yyidx + -1 ]->minor, 1) . '\'') .
            ']';
    }

    // line 950 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r130()
    {
        $this->_retvalue = '[]';
    }

    // line 954 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r131()
    {
        $this->_retvalue = '\'' . substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) . '\'';
    }

    // line 959 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r132()
    {
        $this->_retvalue = '\'\'';
    }

    // line 967 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r133()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . '.' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 973 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r135()
    {
        $var =
            trim(substr($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler->getLdelLength(),
                -$this->compiler->getRdelLength()), ' $');
        $this->_retvalue = $this->compiler->compileVariable('\'' . $var . '\'');
    }

    // line 980 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r136()
    {
        $this->_retvalue = '(' . $this->yystack[ $this->yyidx + -1 ]->minor . ')';
    }

    // line 989 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r137()
    {
        if ($this->yystack[ $this->yyidx + -1 ]->minor[ 'var' ] === '\'smarty\'') {
            $this->_retvalue =
                $this->compiler->compileTag('private_special_variable', array(),
                    $this->yystack[ $this->yyidx + -1 ]->minor[ 'smarty_internal_index' ]) .
                $this->yystack[ $this->yyidx + 0 ]->minor;
        } else {
            $this->_retvalue =
                $this->compiler->compileVariable($this->yystack[ $this->yyidx + -1 ]->minor[ 'var' ]) .
                $this->yystack[ $this->yyidx + -1 ]->minor[ 'smarty_internal_index' ] .
                $this->yystack[ $this->yyidx + 0 ]->minor;
        }
    }

    // line 994 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r138()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 999 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r139()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1006 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r140()
    {
        if ($this->security && substr($this->yystack[ $this->yyidx + -1 ]->minor, 0, 1) === '_') {
            $this->compiler->trigger_template_error(self::ERR1);
        }
        $this->_retvalue =
            '->' . $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1013 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r141()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::ERR2);
        }
        $this->_retvalue =
            '->{' .
            $this->compiler->compileVariable($this->yystack[ $this->yyidx + -1 ]->minor) .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            '}';
    }

    // line 1020 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r142()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::ERR2);
        }
        $this->_retvalue =
            '->{' . $this->yystack[ $this->yyidx + -2 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor . '}';
    }

    // line 1028 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r143()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::ERR2);
        }
        $this->_retvalue =
            '->{\'' .
            $this->yystack[ $this->yyidx + -4 ]->minor .
            '\'.' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            '}';
    }

    // line 1036 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r144()
    {
        $this->_retvalue = '->' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1044 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r145()
    {
        $this->_retvalue =
            $this->compiler->compilePHPFunctionCall($this->yystack[ $this->yyidx + -3 ]->minor,
                $this->yystack[ $this->yyidx + -1 ]->minor);
    }

    // line 1051 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r146()
    {
        if ($this->security && substr($this->yystack[ $this->yyidx + -3 ]->minor, 0, 1) === '_') {
            $this->compiler->trigger_template_error(self::ERR1);
        }
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -3 ]->minor .
            '(' .
            implode(',', $this->yystack[ $this->yyidx + -1 ]->minor) .
            ')';
    }

    // line 1062 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r147()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::ERR2);
        }
        $prefixVar = $this->compiler->getNewPrefixVariable();
        $this->compiler->appendPrefixCode("<?php {$prefixVar} = " .
                                          $this->compiler->compileVariable('\'' .
                                                                           substr($this->yystack[ $this->yyidx +
                                                                                                  -3 ]->minor, 1) .
                                                                           '\'') .
                                          ';?>');
        $this->_retvalue = $prefixVar . '(' . implode(',', $this->yystack[ $this->yyidx + -1 ]->minor) . ')';
    }

    // line 1079 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r148()
    {
        $this->_retvalue =
            array_merge($this->yystack[ $this->yyidx + -2 ]->minor, array($this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 1083 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r151()
    {
        $this->_retvalue =
            array_merge($this->yystack[ $this->yyidx + -2 ]->minor, array(
                array_merge($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor)
            ));
    }

    // line 1091 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r152()
    {
        $this->_retvalue =
            array(array_merge($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 1099 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r154()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 1118 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r155()
    {
        $this->_retvalue =
            array_merge($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 1123 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r159()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + 0 ]->minor, '', 'method');
    }

    // line 1128 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r160()
    {
        $this->_retvalue =
            array($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor, 'method');
    }

    // line 1133 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r161()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + 0 ]->minor, '');
    }

    // line 1138 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r162()
    {
        $this->_retvalue =
            array($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor, 'property');
    }

    // line 1144 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r163()
    {
        $this->_retvalue =
            array(
                $this->yystack[ $this->yyidx + -2 ]->minor,
                $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor, 'property'
            );
    }

    // line 1148 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r164()
    {
        $this->_retvalue = ' ' . trim($this->yystack[ $this->yyidx + 0 ]->minor) . ' ';
    }

    // line 1167 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r165()
    {
        static $lops = array(
            'eq'  => ' == ',
            'ne'  => ' != ',
            'neq' => ' != ',
            'gt'  => ' > ',
            'ge'  => ' >= ',
            'gte' => ' >= ',
            'lt'  => ' < ',
            'le'  => ' <= ',
            'lte' => ' <= ',
            'mod' => ' % ',
            'and' => ' && ',
            'or'  => ' || ',
            'xor' => ' xor ',
        );
        $op = strtolower(preg_replace('/\s*/', '', $this->yystack[ $this->yyidx + 0 ]->minor));
        $this->_retvalue = $lops[ $op ];
    }

    // line 1180 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r166()
    {
        static $tlops = array(
            'isdivby'     => array('op' => ' % ', 'pre' => '!('),
            'isnotdivby'  => array('op' => ' % ', 'pre' => '('),
            'isevenby'    => array('op' => ' / ', 'pre' => '!(1 & '),
            'isnotevenby' => array('op' => ' / ', 'pre' => '(1 & '),
            'isoddby'     => array('op' => ' / ', 'pre' => '(1 & '),
            'isnotoddby'  => array('op' => ' / ', 'pre' => '!(1 & '),
        );
        $op = strtolower(preg_replace('/\s*/', '', $this->yystack[ $this->yyidx + 0 ]->minor));
        $this->_retvalue = $tlops[ $op ];
    }

    // line 1194 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r167()
    {
        static $scond = array(
            'iseven'    => '!(1 & ',
            'isnoteven' => '(1 & ',
            'isodd'     => '(1 & ',
            'isnotodd'  => '!(1 & ',
        );
        $op = strtolower(str_replace(' ', '', $this->yystack[ $this->yyidx + 0 ]->minor));
        $this->_retvalue = $scond[ $op ];
    }

    // line 1202 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r168()
    {
        $this->_retvalue = 'array(' . $this->yystack[ $this->yyidx + -1 ]->minor . ')';
    }

    // line 1210 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r170()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -2 ]->minor . ',' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1214 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r172()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -2 ]->minor . '=>' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1230 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r173()
    {
        $this->_retvalue =
            '\'' . $this->yystack[ $this->yyidx + -2 ]->minor . '\'=>' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1236 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r176()
    {
        $this->compiler->leaveDoubleQuote();
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor->to_smarty_php($this);
    }

    // line 1241 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r177()
    {
        $this->yystack[ $this->yyidx + -1 ]->minor->append_subtree($this, $this->yystack[ $this->yyidx + 0 ]->minor);
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 1245 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r178()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Dq($this, $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 1249 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r179()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Code('(string)' . $this->yystack[ $this->yyidx + -1 ]->minor);
    }

    // line 1253 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r180()
    {
        $this->_retvalue =
            new Smarty_Internal_ParseTree_Code('(string)(' . $this->yystack[ $this->yyidx + -1 ]->minor . ')');
    }

    // line 1265 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r181()
    {
        $this->_retvalue =
            new Smarty_Internal_ParseTree_Code('(string)$_smarty_tpl->tpl_vars[\'' .
                                               substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) .
                                               '\']->value');
    }

    // line 1269 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r184()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Tag($this, $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    public function yy_r185()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_DqContent($this->yystack[ $this->yyidx + 0 ]->minor);
    }

    public function yy_reduce($yyruleno)
    {
        if ($this->yyTraceFILE && $yyruleno >= 0
            && $yyruleno < count(self::$yyRuleName)) {
            fprintf(
                $this->yyTraceFILE,
                "%sReduce (%d) [%s].\n",
                $this->yyTracePrompt,
                $yyruleno,
                self::$yyRuleName[ $yyruleno ]
            );
        }
        $this->_retvalue = $yy_lefthand_side = null;
        if (isset(self::$yyReduceMap[ $yyruleno ])) {
            // call the action
            $this->_retvalue = null;
            $this->{'yy_r' . self::$yyReduceMap[ $yyruleno ]}();
            $yy_lefthand_side = $this->_retvalue;
        }
        $yygoto = self::$yyRuleInfo[ $yyruleno ][ 0 ];
        $yysize = self::$yyRuleInfo[ $yyruleno ][ 1 ];
        $this->yyidx -= $yysize;
        for ($i = $yysize; $i; $i--) {
            // pop all of the right-hand side parameters
            array_pop($this->yystack);
        }
        $yyact = $this->yy_find_reduce_action($this->yystack[ $this->yyidx ]->stateno, $yygoto);
        if ($yyact < self::YYNSTATE) {
            if (!$this->yyTraceFILE && $yysize) {
                $this->yyidx++;
                $x = new TP_yyStackEntry;
                $x->stateno = $yyact;
                $x->major = $yygoto;
                $x->minor = $yy_lefthand_side;
                $this->yystack[ $this->yyidx ] = $x;
            } else {
                $this->yy_shift($yyact, $yygoto, $yy_lefthand_side);
            }
        } elseif ($yyact === self::YYNSTATE + self::YYNRULE + 1) {
            $this->yy_accept();
        }
    }

    public function yy_parse_failed()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sFail!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
    }

    public function yy_syntax_error($yymajor, $TOKEN)
    {
        // line 214 "../smarty/lexer/smarty_internal_templateparser.y"
        $this->internalError = true;
        $this->yymajor = $yymajor;
        $this->compiler->trigger_template_error();
    }

    public function yy_accept()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sAccept!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
        // line 207 "../smarty/lexer/smarty_internal_templateparser.y"
        $this->successful = !$this->internalError;
        $this->internalError = false;
        $this->retvalue = $this->_retvalue;
    }

    public function doParse($yymajor, $yytokenvalue)
    {
        $yyerrorhit = 0;   /* True if yymajor has invoked an error */
        if ($this->yyidx === null || $this->yyidx < 0) {
            $this->yyidx = 0;
            $this->yyerrcnt = -1;
            $x = new TP_yyStackEntry;
            $x->stateno = 0;
            $x->major = 0;
            $this->yystack = array();
            $this->yystack[] = $x;
        }
        $yyendofinput = ($yymajor == 0);
        if ($this->yyTraceFILE) {
            fprintf(
                $this->yyTraceFILE,
                "%sInput %s\n",
                $this->yyTracePrompt,
                $this->yyTokenName[ $yymajor ]
            );
        }
        do {
            $yyact = $this->yy_find_shift_action($yymajor);
            if ($yymajor < self::YYERRORSYMBOL &&
                !$this->yy_is_expected_token($yymajor)) {
                // force a syntax error
                $yyact = self::YY_ERROR_ACTION;
            }
            if ($yyact < self::YYNSTATE) {
                $this->yy_shift($yyact, $yymajor, $yytokenvalue);
                $this->yyerrcnt--;
                if ($yyendofinput && $this->yyidx >= 0) {
                    $yymajor = 0;
                } else {
                    $yymajor = self::YYNOCODE;
                }
            } elseif ($yyact < self::YYNSTATE + self::YYNRULE) {
                $this->yy_reduce($yyact - self::YYNSTATE);
            } elseif ($yyact === self::YY_ERROR_ACTION) {
                if ($this->yyTraceFILE) {
                    fprintf(
                        $this->yyTraceFILE,
                        "%sSyntax Error!\n",
                        $this->yyTracePrompt
                    );
                }
                if (self::YYERRORSYMBOL) {
                    if ($this->yyerrcnt < 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $yymx = $this->yystack[ $this->yyidx ]->major;
                    if ($yymx === self::YYERRORSYMBOL || $yyerrorhit) {
                        if ($this->yyTraceFILE) {
                            fprintf(
                                $this->yyTraceFILE,
                                "%sDiscard input token %s\n",
                                $this->yyTracePrompt,
                                $this->yyTokenName[ $yymajor ]
                            );
                        }
                        $this->yy_destructor($yymajor, $yytokenvalue);
                        $yymajor = self::YYNOCODE;
                    } else {
                        while ($this->yyidx >= 0 &&
                               $yymx !== self::YYERRORSYMBOL &&
                               ($yyact = $this->yy_find_shift_action(self::YYERRORSYMBOL)) >= self::YYNSTATE
                        ) {
                            $this->yy_pop_parser_stack();
                        }
                        if ($this->yyidx < 0 || $yymajor == 0) {
                            $this->yy_destructor($yymajor, $yytokenvalue);
                            $this->yy_parse_failed();
                            $yymajor = self::YYNOCODE;
                        } elseif ($yymx !== self::YYERRORSYMBOL) {
                            $u2 = 0;
                            $this->yy_shift($yyact, self::YYERRORSYMBOL, $u2);
                        }
                    }
                    $this->yyerrcnt = 3;
                    $yyerrorhit = 1;
                } else {
                    if ($this->yyerrcnt <= 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $this->yyerrcnt = 3;
                    $this->yy_destructor($yymajor, $yytokenvalue);
                    if ($yyendofinput) {
                        $this->yy_parse_failed();
                    }
                    $yymajor = self::YYNOCODE;
                }
            } else {
                $this->yy_accept();
                $yymajor = self::YYNOCODE;
            }
        } while ($yymajor !== self::YYNOCODE && $this->yyidx >= 0);
    }
}
