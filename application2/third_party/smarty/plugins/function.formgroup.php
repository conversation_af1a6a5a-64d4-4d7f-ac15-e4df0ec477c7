<?php

function smarty_function_formgroup($params, Smarty_Internal_Template $template)
{
    $t = isset($params['t']) ? trim($params['t']) : '';
    $desc = isset($params['desc']) ? trim($params['desc']) : '';
    $value = isset($params['value']) ? trim($params['value']) : '';
    $validator = isset($params['validator']) ? trim($params['validator']) : '';

    $name = isset($params['name']) ? trim($params['name']) : '';
    $id = isset($params['id']) ? trim($params['id']) : '';
    $type = isset($params['type']) ? trim($params['type']) : '';

    $isbtn = isset($params['isbtn']) ? $params['isbtn'] : true;
    $url = isset($params['url']) ? trim($params['url']) : '';
    $icon = isset($params['icon']) ? trim($params['icon']) : '';

    switch ($t) {
        case "desc":
            $lst = isset($params['lst']) ? $params['lst'] : array();
            return showdesc($desc, $value, $lst);
        case "input":
            return showinput($desc, $name, $id, $value, $type, $validator);
        case "select":
            $lst = isset($params['lst']) ? $params['lst'] : array();
            return showselect($desc, $name, $id, $lst, $value, $validator);
        case "sselect":
            $lst = isset($params['lst']) ? $params['lst'] : array();
            return showsselect($desc, $name, $id, $lst, $value, $validator);
        case "select2":
            $lst = isset($params['lst']) ? $params['lst'] : array();
            return showselect2($desc, $name, $id, $lst, $value, $validator);
        case "select3":
            $lst = isset($params['lst']) ? $params['lst'] : array();
            return showselect3($desc, $name, $id, $lst, $value, $validator);
        case "cselect":
            $tid = isset($params['tid']) ? intval($params['tid']) : 0;
            $issearch = isset($params['issearch']) ? $params['issearch'] : 0;
            return showcolumnselect($desc, $name, $id, $tid, $value, $validator, $issearch);
        case "page":
            $page = isset($params['page']) ? $params['page'] : array();
            return showpage($page);
        case "ajaxpage":
            $page = isset($params['page']) ? $params['page'] : array();
            return ajaxpage($page);
        case "delbtn":
            return delbtn($desc, $url, $isbtn, $icon, $type);
        case "editbtn":
            return editbtn($desc, $url, $isbtn, $icon, $type);
        case "addbtn":
            return addbtn($desc, $url, $isbtn, $icon, $type);
        case "searchbtn":
            return searchbtn($desc, $icon, $type);
        case "authcbox":
            $auth = isset($params['auth']) ? $params['auth'] : "";
            return showauthactioncbox($name, $value, $desc, $auth);
        case "uploadpic":
            return uploadpic($desc, $name, $id, $value);
        case "ckeditor":
            return ckeditor($desc, $name, $id, $value);
        case "tf":
            return showtruefalse($id);
        case "uploadfile":
            $js = isset($params['js']) ? $params['js'] : "";
            $accept = isset($params['accept']) ? $params['accept'] : "";
            $allowedFileExtensions = isset($params['allowedFileExtensions']) ? $params['allowedFileExtensions'] : "";
            return uploadfile($desc, $name, $id, $value, $js, $accept, $allowedFileExtensions);
        case "inputdatepicker":
            $style = isset($params['style']) ? $params['style'] : "";
            return showinputdatepicker($desc, $name, $id, $value, $style);
        case "inputdatepicker2":
                $style = isset($params['style']) ? $params['style'] : "";
                return showinputdatepicker2($desc, $name, $id, $value, $style);
        case "compressoruppic":
            $js = isset($params['js']) ? $params['js'] : "";
            $css = isset($params['css']) ? $params['css'] : "";
            return compressoruppic($desc, $name, $id, $value, $js, $css);
        default:
            return "";
    }

    return "";
}

function showtruefalse($id)
{
    if ($id == 1) {
        return "是";
    } else {
        return "<font color=red>否</font>";
    }
}

function getuploadurl($act)
{
    return INDEX . "/upload/home/<USER>" . $act;
}

//ckeditor编辑器
function ckeditor($desc, $name, $id, $value = "")
{
    return "<div class='form-group'>
                <label class='col-sm-2 control-label'>{$desc}</label>
                <div class='col-sm-10'>
                    <textarea name='{$name}' id='{$id}' rows='10' cols='80'>
                        {$value}
                    </textarea>
                            <script>
                        CKEDITOR.replace( '{$id}' ,{
                            toolbar: [
                                { name: 'document', items: [ 'Print', 'Source' ] },
                                { name: 'clipboard', items: [ 'Undo', 'Redo' ] },
                                { name: 'styles', items: [ 'Styles', 'Format' ] },
                                { name: 'basicstyles', items: [ 'Bold', 'Italic', 'Strike', '-', 'RemoveFormat' ] },
                                { name: 'paragraph', items: [ 'NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote' ] },
                                { name: 'links', items: [ 'Link', 'Unlink' ] },
                                { name: 'insert', items: [ 'Image', 'Table' ] },
                                { name: 'tools', items: [ 'Maximize' ] },
                                { name: 'editing', items: [ 'Scayt' ] }
                            ],
                            allowedContent : true,
                            height:200,
                            filebrowserUploadMethod :'form',
                            extraPlugins: 'image2,uploadimage',
                            filebrowserUploadUrl:'" . getuploadurl("ckfile") . "',
                            filebrowserImageUploadUrl : '" . getuploadurl("ckimg") . "',
                        });
                    </script>
                </div>
                </div>
";
}

//上传图片
function uploadpic($desc, $name, $id, $value = "")
{
    $ida = "$('#{$id}-up')";
    $initialPreview = "";
    if (!empty($value)) {
        $initialPreview = "
        initialPreviewShowRemove : false,
        initialPreviewFileType: 'image',
        initialPreviewAsData: true,
        initialPreview: ['{$value}'],
        ";
    }

    return "
            <div class='form-group'>
                        <label class='col-sm-2 control-label'>{$desc}</label>
                        <div class='col-sm-5'>
                            <input type='hidden' name='{$name}' id='{$id}' value='{$value}'  />
                            <input id='{$name}-up' name='{$id}-up' type='file' accept='image/*' />
                            <script>
                                    {$ida}.fileinput({
                                    language: 'zh',
                                    uploadUrl: '" . getuploadurl("upimg") . "',
                                    uploadAsync: true,
                                    allowedFileExtensions : ['png', 'jpg','jpeg','tif'],
                                    allowedPreviewTypes : ['image'],
                                    overwriteInitial: true,
                                    showUpload:false,
                                    defaultPreviewContent: '<img src=\"/public/images/nopic.png\" style=\"width: 100px;height: 100px;\" >',
                                    {$initialPreview}
                                }).on('filebatchselected', function(event, files) {
                                    $('#{$id}-up').fileinput('upload');
                                }).on('filecleared', function(event, id) {
                                    $('#{$id}').val('');
                                }).on('fileuploaded', function (event, data) {
                                    //console.log(data.response);
                                    var d=data.response;
                                    if(d.s==1){
                                        $('#{$id}').val(d.www_path);
                                        toastr.success('上传成功');
                                    }else{
                                        toastr.error(d.msg);
                                    }
                                });
                            </script>
                        </div>
                    </div>
    ";
}

//上传文件
function uploadfile($desc, $name, $id, $value = "", $js = "", $accept = "", $allowedFileExtensions = "")
{

    $initialPreview = "";
    if (!empty($value)) {
        $initialPreview = "
        initialPreviewShowRemove : false,
        initialPreviewFileType: 'image',
        initialPreviewAsData: true,
        initialPreview: ['{$value}'],
        ";
    }
    return "
            <div class='form-group'>
                        <label class='col-sm-2 control-label'>{$desc}</label>
                        <div class='col-sm-5'>
                            <input type='hidden' name='{$name}' id='{$id}' value='{$value}'  />
                            <input type='hidden' name='{$name}_id' id='{$id}_id' value=''  />
                            <input id='{$name}-up' name='{$id}-up' type='file' accept='{$accept}' />
                            <script>
                                $('#{$id}-up').fileinput({
                                    language: 'zh',
                                    uploadUrl: '" . getuploadurl("upfile") . "',
                                    uploadAsync: true,
                                    allowedFileExtensions : [{$allowedFileExtensions}],
                                    overwriteInitial: true,
                                    showUpload:false,
                                    defaultPreviewContent: '<img src=\"/public/images/nopic.png\" style=\"width: 100px;height: 100px;\" >',
                                    {$initialPreview}
                                }).on('filebatchselected', function(event, files) {
                                    $('#{$id}-up').fileinput('upload');
                                }).on('filecleared', function(event, id) {
                                    $('#{$id}').val('');
                                }).on('fileuploaded', function (event, data) {
                                    //console.log(data.response);
                                    var d=data.response;
                                    if(d.s==1){
                                        $('#{$id}').val(d.www_path);
                                        $('#{$id}_id').val(d.id);
                                        toastr.success('上传成功');
                                        {$js}
                                    }else{
                                        toastr.error(d.msg);
                                    }
                                });
                            </script>
                        </div>
                    </div>
    ";
}

//权限分配checkbox
function showauthactioncbox($name, $value, $desc, $auth)
{
    $checked = "";
    if (in_array($value, $auth)) {
        $checked = " checked='checked' ";
    }
    /***
    $arr = explode('/', $value);
    $class = "app";
    if (count($arr) >= 2) {
    for ($i = 0; $i <= count($arr) - 2; $i++) {
    $class .= "-" . $arr[$i];
    }
    }
     */
    return "<input class='app' name='{$name}' type='checkbox' value='{$value}' {$checked} />{$desc}";
}

//显示下拉列表
function showselect($desc, $name, $id, $lst, $value = "", $validator)
{
    if ($lst == "tf") {
        $lst = array(1 => "是", 0 => "否");
    }

    $html = "
	<div class='form-group'>
        <label class='col-sm-2 control-label'>$desc</label>
        <div class='col-sm-10'>
			<select class='form-control' name='$name' onchange='headerChange($id)' id='$id' $validator >";
    foreach ($lst as $k => $v) {
        $selected = "";
        if ($k == $value) {
            $selected = " selected='selected' ";
        }
        $html .= "<option value='" . $k . "' $selected >" . $v . "</option>";
    }
    $html .= "</select></div></div>";

    return $html;
}

function showselect3($desc, $name, $id, $lst, $value = "", $validator)
{
    if ($lst == "tf") {
        $lst = array(1 => "是", 0 => "否");
    }

    $html = "
	<div class='form-group'>
        <label class='col-sm-2 control-label'>$desc</label>
        <div class='col-sm-10'>
			<select class='form-control' name='$name' id='$id' $validator >";
    foreach ($lst as $k => $v) {
        $selected = "";
        if ($v == $value) {
            $selected = " selected='selected' ";
        }
        $html .= "<option value='" . $v . "' $selected >" . $v . "</option>";
    }
    $html .= "</select></div></div>";

    return $html;
}

//显示下拉列表
function showselect2($desc, $name, $id, $lst, $value = "", $validator)
{
    if ($lst == "tf") {
        $lst = array(1 => "是", 0 => "否");
    }

    $html = "
	<div class='form-group'>
        <label class='col-sm-2 control-label'>$desc</label>
        <div class='col-sm-10'>
			<select class='form-control' name='$name' id='$id' $validator >";
    foreach ($lst as $k => $v) {
        $selected = "";
        if ($v['k'] == $value) {
            $selected = " selected='selected' ";
        }
        $html .= "<option value='" . $v['k'] . "' $selected >" . $v['v'] . "</option>";
    }
    $html .= "</select></div></div>";

    return $html;
}

function showcolumnselect($desc, $name, $id, $tid, $value = "", $validator, $issearch = 0)
{
    $issearch = intval($issearch);
    $html = "
	<div class='form-group'>
        <label class='col-sm-2 control-label'>$desc</label>
        <div class='col-sm-10'>
            <select class='form-control' name='$name' id='$id' $validator >";

    $html .= "</select></div></div>";
    $html .= "
        <script>
    function change" . $id . "() {
        var sel = $('#" . $id . "');
        var issearch=" . $issearch . ";
        sel.empty();
        if( issearch==1 ){
            var option = $('<option>').text('不限').val(-99)
            sel.append(option);
        }

        $.ajax({
            url: '" . RELROUTE . "/home/<USER>" . $tid . "',
            dataType: 'json',
            success: function (d) {
                //console.log(d);
                $.each(d, function (k, v) {
                    if(v.k!=0){
                        var option = $('<option>').text(v.v).val(v.k);
                        sel.append(option);
                    }
                    //console.log(v);
                });
                $('#" . $id . "').val('" . $value . "');
            }
        });
    }
    change" . $id . "();
    </script>
    ";
    return $html;
}

//add按钮
function addbtn($desc, $url, $isbtn = true, $icon = '', $type = 'sm')
{
    $desc = $desc != "" ? $desc : '添加';
    $icon = $icon != "" ? $icon : 'plus';
    if ($isbtn) {
        return ' <a class="btn btn-' . $type . '  btn-info" href="' . $url . '"><i class="fa fa-' . $icon . '"></i>&nbsp;&nbsp;' . $desc . '</a>';
    } else {
        return "";
    }
}

//edit按钮
function editbtn($desc, $url, $isbtn = true, $icon = '', $type = 'sm')
{
    $desc = $desc != "" ? $desc : '修改';
    $icon = $icon != "" ? $icon : 'edit';
    if ($isbtn) {
        return ' <a class="btn btn-' . $type . '  btn-primary" href="' . $url . '"><i class="fa fa-' . $icon . '"></i>&nbsp;&nbsp;' . $desc . '</a>';
    } else {
        return "";
    }
}

//del按钮
function delbtn($desc, $url, $isbtn = true, $icon = '', $type = 'sm')
{
    $desc = $desc != "" ? $desc : '删除';
    $icon = $icon != "" ? $icon : 'close';
    if ($isbtn) {
        return '<button type="button" onclick="doDelete(\'确定要' . $desc . '吗？\',\'' . $url . '\');" class="btn btn-warning btn-' . $type . '" /><i class="fa fa-' . $icon . '"></i>&nbsp;&nbsp;' . $desc . '</button>';
    } else {
        return "";
    }
}

// 获取地址栏差数，主要用于分页时传递其他差数。$unset用于去掉不需要传值的参数，多个用,隔开
function geturl($unset = '')
{
    $list = array();
    $keys = explode(',', $unset);
    foreach ($_GET as $key => $val) {
        if (!in_array($key, $keys)) {
            $list[] = $key . '=' . urlencode($val);
        }
    }
    return implode('&', $list);
}

//显示分页
function showpage($page)
{
    if (empty($page)) {
        return "";
    }

    $url = geturl('page');
    $url = empty($url) ? '?page=' : '?' . $url . '&page=';
    $url = $page['uri_string'] . $url;

    $html = '<ul class="pagination pagination-sm no-margin pull-right">';
    if ($page['current'] > 1) {
        $html .= ' <li class="paginate_button"><a href="' . $url . '1">首页</a></li>
        <li class="paginate_button"><a href="' . $url . ($page['current'] - 1) . '">上一页</a></li>  ';
    } else {
        $html .= ' <li class="paginate_button disabled"><span>首页</span></li>
        <li class="paginate_button disabled"><span>上一页</span></li>  ';
    }

    if ($page['all'] < 6) {
        $arr = range(1, $page['all']);
    } else {
        if ($page['current'] < 3) {
            $arr = range(1, 5);
        } elseif ($page['current'] <= $page['all'] && $page['current'] > ($page['all'] - 3)) {
            $arr = range(($page['all'] - 4), $page['all']);
        } else {
            $arr = range(($page['current'] - 2), ($page['current'] + 2));
        }
    }
    foreach ($arr as $val) {
        if ($val == $page['current']) {
            $html .= ' <li class="paginate_button disabled"><span>' . $val . '</span></li>  ';
        } else {
            $html .= ' <li class="paginate_button"><a href="' . $url . $val . '">' . $val . '</a></li>  ';
        }
    }

    if ($page['current'] < $page['all']) {
        $html .= ' <li class="paginate_button"><a href="' . $url . ($page['current'] + 1) . '">下一页</a></li>
        <li class="paginate_button"><a href="' . $url . $page['all'] . '">尾页</a></li> ';
    } else {
        $html .= ' <li class="paginate_button disabled"><span>下一页</span></li>
         <li class="paginate_button disabled"><span>尾页</span></li> ';
    }

    $html .= '<li class="disabled"><span>' . $page['count'] . "条记录 " . $page['current'] . "&#047;" . $page['all'] . "页  </span></li>";

    $html .= ' </ul>';

    return $html;
}

//显示描述
function showdesc($desc, $value, $lst = array())
{
    if ($lst == "tf") {
        $lst = array(1 => "是", 0 => "否");
        $value = $lst[$value];
    } else if (!empty($lst)) {
        $value = $lst[$value];
    }

    return "
    <div class='form-group'>
        <label class='col-sm-2 control-label'>$desc</label>
        <div class='col-sm-10'>$value</div>
    </div>
    ";
}

//显示文本/密码输入框
function showinput($desc, $name, $id, $value = "", $type = "text", $validator)
{
    if ($type == "") {
        $type = "text";
    }
    return "
	<div class='form-group'>
        <label  class='col-sm-2 control-label'>$desc</label>
        <div class='col-sm-10'>
            <input class='form-control' type='$type' name='$name' id='$id' value='$value'  $validator>
        </div>
    </div>
    ";
}

//显示搜索下拉列表
function showsselect($desc, $name, $id, $lst, $value = "", $validator)
{
    if ($lst == "tf") {
        $lst = array(1 => "是", 0 => "否");
    }

    $html = "
    <div class='input-group'>
       <span class='input-group-addon bg-aqua color-palette'>$desc</span>
             <select class='form-control' name='$name' id='$id' $validator > ";
    $html .= "<option value='-99' >不限</option>";
    foreach ($lst as $k => $v) {
        $selected = "";
        if ($k == $value) {
            $selected = " selected='selected' ";
        }
        $html .= "<option value='" . $k . "' $selected >" . $v . "</option>";
    }

    $html .= "</select></div>";

    return $html;
}

//搜索按钮
function searchbtn($desc, $icon, $type = 'sm')
{
    $desc = $desc != "" ? $desc : '搜索';
    $icon = $icon != "" ? $icon : 'search';
    return '
    <!--
    <button type="button" class="btn btn-' . $type . ' btn-warning" style="width: 120px;" onclick="dosearchrest()"><i class="fa fa-refresh"></i>&nbsp;&nbsp;重置</button>
    -->
    <button type="button" class="btn btn-' . $type . ' btn-success" style="width: 120px;" onclick="dosearch()"><i class="fa fa-' . $icon . '"></i>&nbsp;&nbsp;' . $desc . '</button>
    ';
}

function showinputdatepicker($desc, $name, $id, $value = "", $style = "datepicker")
{
    return "
	<div class='form-group'>
        <label  class='col-sm-2 control-label'>$desc</label>
        <div class='col-sm-10'>
            <input class='form-control $style' readonly='readonly' type='text' name='$name' id='$id' value='$value'>
        </div>
    </div>
    ";
}

function showinputdatepicker2($desc, $name, $id, $value = "", $style = "datepicker")
{
    return "
	<div class='form-group'>
        <label  class='col-sm-4 control-label'>$desc</label>
        <div class='col-sm-8'>
            <input class='form-control $style' readonly='readonly' type='text' name='$name' id='$id' value='$value'>
        </div>
    </div>
    ";
}

function compressoruppic($desc, $name, $id, $value = "", $js = "", $css = "width: 8rem;height: 8rem;")
{
    return "
    <div class='form-group'>
        <label class='col-sm-2 control-label'>$desc</label>
        <div class='col-sm-10'>
            <img src='/public/images/nopic.png' id='img_{$id}' style='{$css}' />
            <input type='file' accept='image/*' id='file_{$id}' name='file' onchange='upload_{$id}();'
            class='fileInput' value='' />
        </div>
    </div>

    <script>
    function upload_{$id}() {
        var c = document.querySelector('#file_{$id}'),
            d = document.querySelector('#img_{$id}'),
            file = c.files[0];
        if (!file) {
            return;
        }
        new Compressor(file, {
            convertSize: 1800000,
            success(result) {
                console.log(result);

                const formData = new FormData();
                formData.append('file', result, result.name);

                // 通过XMLHttpRequest服务发送压缩的图像文件-Send the compressed image file to server with XMLHttpRequest.
                axios.post('" . getuploadurl("upimg") . "', formData).then(
                        function (response) {
                            console.log(response);
                            var reader = new FileReader();
                            reader.readAsDataURL(result);
                            reader.onload = function (e) {
                                d.setAttribute('src', e.target.result);
                            };
                            success('上传成功');
                            {$js}
                        })
                    .catch(function (error) {
                        console.log(error);
                        success('上传失败');
                        d.setAttribute('src', '/public/images/nopic.png');
                    });

                //blog = result;

            },
            error(err) {
                console.log(err.message);
            },
        });
    }
    </script>

    ";
}

//显示分页
function ajaxpage($page)
{
    if (empty($page)) {
        return "";
    }

    $url = geturl('page');
    $url = empty($url) ? '?page=' : '?' . $url . '&page=';
    $url = $page['uri_string'] . $url;

    $html = '<ul class="pagination pagination-sm no-margin pull-right">';
    if ($page['current'] > 1) {
        $html .= ' <li class="paginate_button"><a href="javascript:gopage(1);">首页</a></li>
        <li class="paginate_button"><a href="javascript:gopage(' . ($page['current'] - 1) . ');">上一页</a></li>  ';
    } else {
        $html .= ' <li class="paginate_button disabled"><span>首页</span></li>
        <li class="paginate_button disabled"><span>上一页</span></li>  ';
    }

    if ($page['all'] < 6) {
        $arr = range(1, $page['all']);
    } else {
        if ($page['current'] < 3) {
            $arr = range(1, 5);
        } elseif ($page['current'] <= $page['all'] && $page['current'] > ($page['all'] - 3)) {
            $arr = range(($page['all'] - 4), $page['all']);
        } else {
            $arr = range(($page['current'] - 2), ($page['current'] + 2));
        }
    }
    foreach ($arr as $val) {
        if ($val == $page['current']) {
            $html .= ' <li class="paginate_button disabled"><span>' . $val . '</span></li>  ';
        } else {
            $html .= ' <li class="paginate_button"><a href="javascript:gopage(' . $val . ');">' . $val . '</a></li>  ';
        }
    }

    if ($page['current'] < $page['all']) {
        $html .= ' <li class="paginate_button"><a href="javascript:gopage(' . ($page['current'] + 1) . ');">下一页</a></li>
        <li class="paginate_button"><a href="javascript:gopage(' . $page['all'] . ');">尾页</a></li> ';
    } else {
        $html .= ' <li class="paginate_button disabled"><span>下一页</span></li>
         <li class="paginate_button disabled"><span>尾页</span></li> ';
    }

    $html .= '<li class="disabled"><span>' . $page['count'] . "条记录 " . $page['current'] . "&#047;" . $page['all'] . "页  </span></li>";

    $html .= ' </ul>';

    return $html;
}
