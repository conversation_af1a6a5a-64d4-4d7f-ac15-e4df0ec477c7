<?php

/**
 * +-----------------------------------------
 * | 授权文件管理配置（后台和会员中心用到）
 * +-----------------------------------------
 */

defined('BASEPATH') or exit('No direct script access allowed');

/*

$_authaction 数组字段说明

menu      是否在菜单显示，0为否，1为是；
app       应用标志
name      名称
children  子数组

module    应用下面的模块
action    模块下面的操作

备注：本框架主要以url节点来做权限判断。如 index.php?m=module&a=action

 */

$config['authaction'] = array(

    /**
     * +--------------------
     * | 后台管理
     * +--------------------
     */
    array(
        'menu' => 0,
        'app' => 'admin',
        'name' => '后台',
        'children' => array(
            // 后台首页管理
            array(
                'menu' => 1,
                'module' => 'home',
                'name' => '后台首页',
                'ico' => 'fa-home',
                'children' => array(
                    array('menu' => 0, 'action' => 'index', 'name' => '后台主页'),
                    array('menu' => 1, 'action' => 'main', 'name' => '控制台'),
                    array('menu' => 1, 'action' => 'self', 'name' => '个人信息修改'),
                    array('menu' => 0, 'action' => 'getjson', 'name' => '获取后台JSON'),
                ),
            ),
            // 系统管理
            array(
                'menu' => 1,
                'module' => 'system',
                'name' => '系统设置',
                'ico' => 'fa-gear',
                'children' => array(
                    array('menu' => 1, 'action' => 'config', 'name' => '网站配置'),
                    array('menu' => 1, 'action' => 'log_action', 'name' => '操作日志'),
                    //array('menu' => 1, 'action' => 'compfile', 'name' => '文件比较'),
                ),
            ),

            // 管理员管理
            array(
                'menu' => 1,
                'module' => 'user',
                'ico' => 'fa-lock',
                'name' => '管理员管理',
                'children' => array(
                    array('menu' => 1, 'action' => 'list', 'name' => '管理员列表'),
                    array('menu' => 0, 'action' => 'add', 'name' => '管理员添加'),
                    array('menu' => 0, 'action' => 'update', 'name' => '更新管理员'),
                    array('menu' => 0, 'action' => 'delete', 'name' => '管理员删除'),

                    array('menu' => 1, 'action' => 'role', 'name' => '角色管理'),
                    array('menu' => 0, 'action' => 'role_add', 'name' => '角色添加'),
                    array('menu' => 0, 'action' => 'role_update', 'name' => '角色更新'),
                    array('menu' => 0, 'action' => 'role_delete', 'name' => '角色删除'),
                    array('menu' => 0, 'action' => 'role_auth_setting', 'name' => '权限分配'),
                    array('menu' => 0, 'action' => 'role_auth_update', 'name' => '权限更新'),
                    array('menu' => 0, 'action' => 'product', 'name' => '分配产品'),
                    array('menu' => 0, 'action' => 'product_update', 'name' => '分配产品更新'),
                ),
            ),

            array(
                'menu' => 1,
                'module' => 'column',
                'name' => '参数管理',
                'ico' => 'fa-columns',
                'children' => array(
                    array('menu' => 1, 'action' => 'alist', 'name' => '类型列表'),
                    array('menu' => 1, 'action' => 'blist', 'name' => '规格列表'),
                    array('menu' => 1, 'action' => 'clist', 'name' => '店主列表'),
                    array('menu' => 0, 'action' => 'list', 'name' => '参数列表'),
                    array('menu' => 0, 'action' => 'add', 'name' => '添加参数'),
                    array('menu' => 0, 'action' => 'update', 'name' => '更新参数'),
                    array('menu' => 0, 'action' => 'delete', 'name' => '删除参数'),
                    array('menu' => 1, 'action' => 'config', 'name' => '面单参数'),
                ),
            ),
            /**
            array(
                'menu' => 1,
                'module' => 'clamp',
                'name' => '夹具管理',
                'ico' => 'fa-bell-o',
                'children' => array(
                    array('menu' => 1, 'action' => 'list', 'name' => '夹具列表'),
                    array('menu' => 0, 'action' => 'update', 'name' => '更新夹具'),
                ),
            ),
             */
            //订单管理
            array(
                'menu' => 1,
                'module' => 'sorder',
                'name' => '订单管理',
                'ico' => 'fa-book',
                'children' => array(
                    array('menu' => 1, 'action' => 'list', 'name' => '订单列表'),

                    array('menu' => 0, 'action' => 'add', 'name' => '添加订单'),
                    array('menu' => 0, 'action' => 'update', 'name' => '更新订单'),
                    array('menu' => 0, 'action' => 'delete', 'name' => '删除订单'),
                    array('menu' => 0, 'action' => 'export', 'name' => '导出记录'),
                    array('menu' => 0, 'action' => 'doprod', 'name' => '订单提交生产'),

                    array('menu' => 0, 'action' => 'update3', 'name' => '更新邮票'),

                    array('menu' => 0, 'action' => 'add2', 'name' => '添加产品'),
                    array('menu' => 0, 'action' => 'update2', 'name' => '更新产品'),
                    array('menu' => 0, 'action' => 'delete2', 'name' => '删除产品'),

                    array('menu' => 1, 'action' => 'rlist', 'name' => '生产产品'),
                    array('menu' => 0, 'action' => 'dprint', 'name' => '改变产品生产状态'),
                    array('menu' => 0, 'action' => 'mprint', 'name' => '打印面单'),
                    array('menu' => 0, 'action' => 'mprintjiu', 'name' => '打印生产文件'),
                    array('menu' => 0, 'action' => 'mprintjiuAll', 'name' => '批量打印生产文件'),
                    array('menu' => 0, 'action' => 'mprintStamp', 'name' => '批量打印邮票'),
                    array('menu' => 0, 'action' => 'dprintAll', 'name' => '批量改变产品生产状态'),
                    array('menu' => 0, 'action' => 'dprintStamp', 'name' => '批量改变邮票状态'),
                   
                ),
            ),

        ),
    ),

    /**
     * +--------------------
     * | 上传
     * +--------------------
     */
    array(
        'menu' => 0,
        'app' => 'upload',
        'name' => '上传',
        'children' => array(
            // 上传
            array(
                'menu' => 0,
                'module' => 'home',
                'name' => '上传文件',
                'children' => array(
                    array('menu' => 0, 'action' => 'index', 'name' => '图片/文件上传'),
                ),
            ),
            // 这里添加您自己的插件...
        ),
    ),

);
