<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014-2018, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['ftp_no_connection'] = '无法找到一个有效的连接 ID。在执行任何文件例程时请确保已经连接成功。';
$lang['ftp_unable_to_connect'] = '无法使用提供的主机名连接到 FTP 服务器。';
$lang['ftp_unable_to_login'] = '无法登录到 FTP 服务器，请检查用户名及密码是否正确。';
$lang['ftp_unable_to_mkdir'] = '无法创建指定的目录。';
$lang['ftp_unable_to_changedir'] = '无法改变目录。';
$lang['ftp_unable_to_chmod'] = '无法设置文件权限，检查文件路径或用户权限。';
$lang['ftp_unable_to_upload'] = '无法上传指定的文件，检查上传路径或用户权限。';
$lang['ftp_unable_to_download'] = '无法下载指定的文件，检查下载路径或用户权限。';
$lang['ftp_no_source_file'] = '无法找到指定的源文件，检查文件路径或用户权限。';
$lang['ftp_unable_to_rename'] = '无法重命名文件。';
$lang['ftp_unable_to_delete'] = '无法删除文件。';
$lang['ftp_unable_to_move'] = '无法移动文件，检查目的目录是否存在。';
