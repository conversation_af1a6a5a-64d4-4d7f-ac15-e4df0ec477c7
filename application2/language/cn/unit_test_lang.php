<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014-2018, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['ut_test_name'] = '测试名';
$lang['ut_test_datatype'] = '测试的数据类型';
$lang['ut_res_datatype'] = '期望的数据类型';
$lang['ut_result'] = '结果';
$lang['ut_undefined'] = '未定义的测试名';
$lang['ut_file'] = '文件名';
$lang['ut_line'] = '行号';
$lang['ut_passed'] = '通过';
$lang['ut_failed'] = '失败';
$lang['ut_boolean'] = '布尔型';
$lang['ut_integer'] = '整型';
$lang['ut_float'] = '浮点数';
$lang['ut_double'] = '双精度数'; // can be the same as float
$lang['ut_string'] = '字符串';
$lang['ut_array'] = '数组';
$lang['ut_object'] = '对象';
$lang['ut_resource'] = '资源';
$lang['ut_null'] = 'Null';
$lang['ut_notes'] = '笔记';
