<?php
/**
 * System messages translation for CodeIgniter(tm)
 *
 * <AUTHOR> community
 * @copyright	Copyright (c) 2014-2018, British Columbia Institute of Technology (http://bcit.ca/)
 * @license	http://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required'] = '{field} 不能为空。';
$lang['form_validation_isset'] = '{field} 不能为空。';
$lang['form_validation_valid_email'] = '{field} 必须是一个有效的 E-mail地址。';
$lang['form_validation_valid_emails'] = '{field} 必须都是有效的 E-mail地址。';
$lang['form_validation_valid_url'] = '{field} 必须是一个有效的 URL。';
$lang['form_validation_valid_ip'] = '{field} 必须是一个有效的 IP地址。';
$lang['form_validation_min_length'] = '{field} 不能少于 {param}位。';
$lang['form_validation_max_length'] = '{field} 不能超过 {param}位。';
$lang['form_validation_exact_length'] = '{field} 长度必须为 {param}位。';
$lang['form_validation_alpha'] = '{field} 只能使用字母。';
$lang['form_validation_alpha_numeric'] = '{field} 只能使用字母和数字。';
$lang['form_validation_alpha_numeric_spaces'] = '{field} 只能使用字母、数字和空格。';
$lang['form_validation_alpha_dash'] = '{field} 只能使用字母、数字、下划线和破折号';
$lang['form_validation_numeric'] = '{field} 只能是数值。';
$lang['form_validation_is_numeric'] = '{field} 只能是数值。';
$lang['form_validation_integer'] = '{field} 只能是数字。';
$lang['form_validation_regex_match'] = '{field} 格式错误。';
$lang['form_validation_matches'] = '{field} 必须是{param}。';
$lang['form_validation_differs'] = '{field} 不能是{param}。';
$lang['form_validation_is_unique'] = '{field} 不能重复。';
$lang['form_validation_is_natural'] = '{field} 必须是数值。';
$lang['form_validation_is_natural_no_zero'] = '{field} 必须是不为0的数值。';
$lang['form_validation_decimal'] = '{field} 必须是数值。';
$lang['form_validation_less_than'] = '{field} 必须小于 {param}。';
$lang['form_validation_less_than_equal_to'] = '{field} 必须小于等于 {param}。';
$lang['form_validation_greater_than'] = '{field} 字段的值必须大于 {param}。';
$lang['form_validation_greater_than_equal_to'] = '{field} 必须大于等于 {param}。';
$lang['form_validation_error_message_not_set'] = ' 没有{field}的错误消息。';
$lang['form_validation_in_list'] = '{field} 必须是 {param}之一。'; 
