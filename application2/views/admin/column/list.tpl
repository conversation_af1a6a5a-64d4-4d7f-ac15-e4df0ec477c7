{include file='admin/public/cheader.tpl'}
<script src="{$env.public}/jquery/qrcode.min.js"></script>

<div class="box">

    <form class="form-horizontal" id="search_form" action="{URI}">
        <div class="box-header">
            <h3 class="box-title">{$modeName}列表</h3>
        </div>
        <div class="box-body searchform">
            <!-- /.box-header -->
            <div class="box-body">
                <div class="row">
                    <div class="col-sm-6">
                        {formgroup t='select' desc="分类" id="stid" name="stid" lst=$tid_arr
                        value=$s.stid|default:'-99' }
                    </div>
                    <div class="col-sm-6">
                        {formgroup t='input' desc="类型nid" id="sgid" name="sgid" value=$s.sgid|default:0}
                    </div>
                    <div class="col-sm-6">
                        {formgroup t='input' desc="标题" id="stitle" name="stitle" value=$s.stitle|default:''}
                    </div>
                </div>

                <div class="btn-group">
                    {formgroup t='addbtn' url="javascript:juaddurl()"
                    isbtn=$qx.add desc=$modeName|cat:"添加"}
                    <script>
                        function juaddurl() {
                            window.location.href = '{RELROUTE}/{MODULE}/add?tid=' + $('#stid').val();
                        }
                    </script>
                    {formgroup t='searchbtn' }
                </div>
            </div>
            <!-- /.box-body -->
        </div>
    </form>

</div>
<div class="box">
    <!-- /.box-header -->
    <div class="box-body table-responsive no-padding">
        <table class="table table-hover">
            <tbody>
                <tr>
                    <th>nid</th>
                    <th>标题</th>
                    <th>分类</th>
                    <th>显示</th>
                    {if $s.stid|default:'-99' eq 1}
                    <th>单价</th>
                    <th>所属类型</th>
                    {/if}
                    {if $s.stid|default:'-99' eq 2}
                    <th>所属</th>
                    {/if}
                    <th>操作</th>
                </tr>
                {foreach $lst as $k=>$v}
                <tr>
                    <td>{$v.nid}</td>
                    <td>{$v.title|truncate:28:'...'}</td>

                    <td>{$tid_arr[$v.tid]|default:''}</td>
                    <td>{formgroup t='tf' id=$v.status}</td>
                    {if $s.stid|default:'-99' eq 1}
                    <td>{$v.description|default:''}</td>
                    <td>{$column_arr[$v.oid]|default:''}</td>
                    {/if}
                    {if $s.stid|default:'-99' eq 2}
                    <td>{$v.creater}</td>
                    {/if}
                    <td>
                        <div class="btn-group">
                            {formgroup t='editbtn' url=RELROUTE|cat:"/"|cat:MODULE|cat:"/update?nid="|cat:$v.nid
                            isbtn=$qx.update}
                            {formgroup t='delbtn' url=RELROUTE|cat:"/"|cat:MODULE|cat:"/delete?nid="|cat:$v.nid
                            isbtn=$qx.delete}
                        </div>
                    </td>
                </tr>
                {/foreach}

            </tbody>
        </table>
    </div>
    <!-- /.box-body -->
    <div class="box-footer clearfix">
        {formgroup t='page' page=$page}
    </div>
</div>
<!-- /.box -->
{include file='admin/public/cfooter.tpl'}