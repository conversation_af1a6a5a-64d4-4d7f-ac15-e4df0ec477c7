{include file='admin/public/cheader.tpl'}
<style>
    select[readonly] {
        background: #eee;
        cursor: no-drop;
    }

    select[readonly] option {
        display: none;
    }
</style>
<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">{$modeName}{if ACTION eq "add"}添加{/if}{if ACTION eq "update"}修改{/if}</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="{URI}" method="post">
                <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
                <div class="box-body">


                    {formgroup t='input' desc="名称" id="title" name="title"
                    value=$data.title|default:''
                    validator=" required data-bv-notempty-message='名称不能为空' "}


                    {formgroup t='select' desc="分类" id="tid" name="tid" lst=$tid_arr
                    value=$data.tid|default:0}
                    
                    {if $data.tid|default:0 eq 1}
                    {formgroup t='input' desc="单价" id="description" name="description"
                    value=$data.description|default:0}
                    {formgroup t='select' desc="所属类型" id="oid" name="oid" lst=$c1_arr value=$data.oid|default:0}
                    {/if}

                    {formgroup t='select' desc="是否显示" id="status" name="status" lst="tf"
                    value=$data.status|default:1}

                    <input type="hidden" name="nid" id="nid" value="{$data.nid|default:0}">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="{RELROUTE}/{MODULE}/list?stid={$data.tid|default:0}">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {
        $('#input_form').bootstrapValidator();
        $('#tid').attr('readonly', true);
    });
</script>

{include file='admin/public/cfooter.tpl'}