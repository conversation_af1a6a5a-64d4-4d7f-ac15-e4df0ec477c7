{include file='admin/public/cheader.tpl'}
{include file='admin/public/fileinputjs.tpl'}
<!--日期框-->
<link href="{$env.public}/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<script src="{$env.public}/datepicker/bootstrap-datetimepicker.min.js"></script>
<script src="{$env.public}/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"></script>

<link href="{$env.public}/bootstrap-select/css/bootstrap-select.min.css" rel="stylesheet">
<script src="{$env.public}/bootstrap-select/js/bootstrap-select.min.js"></script>
<script src="{$env.public}/bootstrap-select/js/i18n/defaults-zh_CN.min.js"></script>

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">更新邮票</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="{URI}" method="post">
                <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
                <div class="box-body">

                    {formgroup t='uploadfile' desc="邮票" id="stamp" name="stamp" js="succesuploadflie()"
                    value=$data.stamp|default:''}

                    <div class="form-group">
                        <label class="col-sm-2 control-label">&nbsp;</label>
                        <div class="col-sm-10" id="errmsg" style="color: red;display: none;">
                            注意，您上传的邮票格式并非是jpg格式，请检查无误后再保存邮票
                        </div>
                    </div>

                    <input type="hidden" name="nid" id="nid" value="{$data.nid|default:0}">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="{RELROUTE}/{MODULE}/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {
        $('#input_form').bootstrapValidator();

        $('.datepicker_oid').datetimepicker({
            format: "yyyy-mm-dd hh:ii", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'day',
            autoclose: true //选择日期后自动关闭
        });
    });

    function succesuploadflie() {
        if ($('#stamp').val().indexOf('.jpg') < 5) {
            $("#errmsg").show();
        } else {
            $("#errmsg").hide();
        }
    }
</script>

{include file='admin/public/cfooter.tpl'}