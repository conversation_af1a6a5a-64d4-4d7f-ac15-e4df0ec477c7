<div class="box-header">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button>
</div>
<div class="a6printw" id="printdiv">
    <div class="a6print pagep" style="height:{{$aconfig.conf_fsheete|default:900}}px;b">
        <img src=" {$data.stamp|default:''}" style="width:560px;height:{{$aconfig.conf_fsheete-40|default:880}}px;">
    </div>
    {foreach $data.lst|default:array() as $k=>$v}
    <div class="a6print pagep" style="height:{{$aconfig.conf_fsheete|default:900}}px;">
        <img src="{$v.pic|default:''}.png" class="pics" />
        <br /> <span class="cpsl">数量：{$v.num|default:''}</span> <br />
        订单ID：{$data.nid|default:''} <br />
        产品ID：{$v.nid|default:''} <br />
        客户名称：{$data.title|default:''} <br />
        店主：{$c3_arr[$data.s1]|default:''} <br />
        产品类型：{$c1_arr[$v.cplx]|default:''}<br />
        产品规格：{$c2_arr[$v.cpkg]|default:''}<br />
        下单时间：{if $data['oid']|default:0>0}{date("Y-m-d H:i", $data['oid'])|default:''}{/if}<br />
        提交时间：{if $data['a1']|default:0>0}{date("Y-m-d H:i", $data['a1'])|default:''}{/if}<br />
        生产日期：{if $data['ptime']|default:0>0}{date("Y-m-d H:i", $data['ptime'])|default:''}{/if}<br />
        产品内容：{$v.s1|default:''}<br />
    </div>
    {/foreach}
</div>
<div class="box-footer">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button>
</div>
<script>
    $(document).ready(function (e) {
        //tif2img("#printdiv");
    });
</script>