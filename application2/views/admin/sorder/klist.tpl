{include file='admin/public/cheader.tpl'}
<script src="{$env.public}/jquery/printThis.js"></script>
<!--日期框-->
<link href="{$env.public}/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<script src="{$env.public}/datepicker/bootstrap-datetimepicker.min.js"></script>
<script src="{$env.public}/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"></script>

<div class="box">

    <form class="form-horizontal" id="search_form" action="{URI}">
        <div class="box-header">
            <h3 class="box-title">发车订单打印</h3>
        </div>
        <div class="box-body searchform">
            <!-- /.box-header -->
            <div class="box-body">
                <div class="row">

                    <div class="col-sm-5">
                        {formgroup t='input' desc="输入客户" id="skh" name="skh" value="{$s.skh|default:''}"}
                    </div>
                    <div class="col-sm-5">
                        {formgroup t='input' desc="类型nid" id="sgid" name="sgid" value=$s.sgid|default:0}
                    </div>
                    <div class="col-sm-5">
                        {formgroup t='inputdatepicker' desc='开始时间' id="soid" name="soid"
                        style="datepicker_soid"
                        value=$s.soid|default:date("Y-m-d",time())}
                    </div>
                    <div class="col-sm-5">
                        {formgroup t='inputdatepicker' desc='结束时间' id="eoid" name="eoid"
                        style="datepicker_soid"
                        value=$s.eoid|default:date("Y-m-d",time())}
                    </div>

                    <div class="col-sm-5">
                        {formgroup t='select' desc="司机" id="scj" name="scj" issearch=1 lst=$sj_arr
                        value=$s.scj|default:''}
                    </div>

                </div>

                <div class="btn-group">
                    {formgroup t='addbtn' url=RELROUTE|cat:"/"|cat:MODULE|cat:"/add" isbtn=$qx.add
                    desc=$modeName|cat:"添加"}
                    {formgroup t='searchbtn' }

                    <button type="button" class="btn btn-sm btn-warning" style="width: 120px;" onclick="dosx()"><i
                            class="fa fa-check-square-o"></i>&nbsp;&nbsp;筛选</button>

                    <button type="button" class="btn btn-sm btn-danger" style="width: 120px;" onclick="doprint()"><i
                            class="fa fa-print"></i>&nbsp;&nbsp;打印</button>

                    <button type="button" class="btn btn-sm btn-info" style="width: 120px;" onclick="dosavek()"><i
                            class="fa fa-pencil-square"></i>保存发车单</button>
                </div>
            </div>
            <!-- /.box-body -->
        </div>
    </form>

</div>
<div class="box">
    <!-- /.box-header -->
    <div class="box-body table-responsive no-padding" id="printtable">
        <section class="invoice">
            <!-- title row -->
            <div class="row">
                <div class="col-xs-12">
                    <h2 class="page-header">
                        <i class="fa fa-truck"></i><span id='sjname'>{$s.scj|default:''}</span>发车订单打印
                        <small class="pull-right">订单时间：<b id="sjstime">{$s.soid|default:date("Y-m-d",time())}</b> 至
                            <b id="sjetime">{$s.eoid|default:date("Y-m-d",time())}</b></small>
                    </h2>
                </div>
                <!-- /.col -->
            </div>
            <!-- Table row -->
            <div class="row">
                <div class="col-xs-12 table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr id="ztitle">
                                <th>序号</th>
                                <th>客户</th>
                                <th>统计</th>
                                {foreach $column_arr as $kk=>$vv}
                                <th>{$vv}</th>
                                {/foreach}
                            </tr>
                        </thead>
                        <tbody>
                            <tr id="zjz">
                                <th>-</th>
                                <th>总计</th>
                                <th id="zjz_2" style="color: red;">-</th>
                                {$i=3}
                                {foreach $column_arr as $kk=>$vv}
                                <th id="zjz_{$i}" style="color: red;">-</th>
                                {$i=$i+1}
                                {/foreach}
                            </tr>
                        </tbody>
                        <tbody id="pintlst">

                        </tbody>
                    </table>
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </section>
    </div>
    <div class="box-body table-responsive no-padding">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>客户</th>
                    <th>统计</th>
                    {foreach $column_arr as $kk=>$vv}
                    <th>{$vv}</th>
                    {/foreach}
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th><input id="selectAll" type="checkbox" value="1">全选</th>
                    <th>总计</th>
                    <th id="maxs" style="color: red;">0</th>
                    {foreach $column_arr as $kk=>$vv}
                    <th id="s_{$kk}" style="color: red;">0</th>
                    {/foreach}
                </tr>
                {$i=1}
                {foreach $lst as $k=>$v}
                <tr id="trs_{$i}">
                    <td class="ids"><input class="app" name="sellst" type="checkbox" value="{$i}" />{$i}</td>
                    <td>{$kids_arr[$v.cid]|default:''}</td>
                    <td id="n_{$v.cid}" style="color: red;">0</td>
                    {foreach $column_arr as $kk=>$vv}
                    <td class="c_{$kk} n_{$v.cid}">{$nums[$v['cid']][$kk]|default:0}</td>
                    {/foreach}
                </tr>
                {$i=$i+1}
                {/foreach}

            </tbody>
        </table>
    </div>
    <!-- /.box-body -->
</div>
<!-- /.box -->
<script>
    // 加载完毕执行jq
    $(document).ready(function (e) {
        $('.datepicker_soid').datetimepicker({
            format: "yyyy-mm-dd hh:ii", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            //minView: 'hours',
            autoclose: true //选择日期后自动关闭
        });
        //点击全选
        $("#selectAll").click(function () {
            //1.获取全选的状态
            var flag = this.checked; //获取全选的状态
            $(":checkbox[name=sellst]").prop("checked",flag);
        });
    });

    function dosx() {
        $("#pintlst").html("");

        $("input[name='sellst']").each(function () {
            if ($(this).is(":checked")) {
                //alert($(this).val());
                var k = $(this).val();
                //console.log($('#trs_' + k + ' td:not(:first-child)'));
                //$("#pintlst").append("<tr><td>" + i + "</td>");
                $("#pintlst").append($('#trs_' + k).prop("outerHTML"));
                //$("#pintlst").append("</tr>");

            }
        });

        //$("#pintlst input").remove();
        var i = 1;
        $("#pintlst .ids").each(function () {
            $(this).html(i);
            i++;
        });

        var ll = 0;
        $("#zjz th").each(function () {
            ll++;
            if (ll >= 3) {
                $(this).html(0);
            }
        });
        //console.log(ll);
        $('#pintlst tr').each(function () {

            for (var i = 2; i < ll; i++) {
                var k = parseFloat($("#zjz_" + i).html());
                k = k + parseFloat($(this).find('td:eq(' + i + ')').html());
                $("#zjz_" + i).html(k)
                //$("#zjz")[0].find('th:eq(' + i + ')').hmtl(i);
            }


            //console.log(txt)
        });

    }

    function doprint() {
        $("#printtable").printThis({
            debug: false,
            importCSS: true,
            importStyle: true,
            printContainer: true,
            //    loadCSS: "/Content/Themes/Default/style.css", 
            pageTitle: "",
            removeInline: false,
            printDelay: 333,
            header: null,
            formValues: false
        });
    }

    function dosavek() {
        var cjson = {};
        cjson['sjname'] = $('#sjname').html();
        cjson['sjstime'] = $('#sjstime').html();
        cjson['sjetime'] = $('#sjetime').html();
        cjson['sjtotle'] = $('#zjz_2').html();

        cjson['table'] = {};

        var i = 0;
        var j = 0;
        cjson['table'][i] = {};
        $("#ztitle th").each(function () {
            cjson['table'][i][j] = $(this).html();
            j++;
        });

        i++;
        j = 0;
        cjson['table'][i] = {};
        $("#zjz th").each(function () {
            //console.log($(this).html());
            cjson['table'][i][j] = $(this).text();
            j++;
        });



        $('#pintlst tr').each(function (ii) {
            i++;
            j = 0;
            cjson['table'][i] = {};
            $(this).children('td').each(function (j) {
                cjson['table'][i][j] = $(this).html();
                j++;
            });
        });

        console.log(cjson);

        $.ajax({
            url: '/index.php/htgl/sorder/savek',
            method: "POST",
            //dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(cjson),
            async: true,
            error: function () {
                parent.error('error');
                //window.location.reload();
            },
            success: function (data) {
                //console.log(data);
                if (data == '1') {
                    parent.success("操作成功");
                } else {
                    parent.error(data);
                }
                //window.location.reload();
            }
        });
    }

    var maxs = 0;
</script>
{foreach $column_arr as $kk=>$vv}
<script>
    var i = 0;
    $(".c_{$kk}").each(function () {
        i = i + parseFloat($(this).html());
    });
    $("#s_{$kk}").html(i);
</script>
{/foreach}
{foreach $lst as $k=>$v}
<script>
    var i = 0;
    $(".n_{$v.cid}").each(function () {
        i = i + parseFloat($(this).html());
    });
    maxs = maxs + i;
    $("#n_{$v.cid}").html(i);
</script>
{/foreach}
<script>
    $("#maxs").html(maxs);
</script>
{include file='admin/public/cfooter.tpl'}