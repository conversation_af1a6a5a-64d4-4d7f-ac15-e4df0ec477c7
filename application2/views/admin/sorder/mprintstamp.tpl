<div class="box-header">
    <button onclick="doprintB()" class="btn btn-success pull-left">打印</button>
    <!-- <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button> -->
    <button onclick="printSuccess()" class="btn btn-danger pull-right">打印成功请点击批量修改邮票状态</button>
</div>
<style>
    #printdivB {
        width: 10.5cm;
        
    }
    #printCssB {
        width:10.5cm;
        height: 14.8cm;
        /* border: 0.5cm solid red; */
        box-sizing: border-box;
        
        /* text-align: center; */
    }

    #printCssImgB {
        width:100%;
        height: 100%;
    }
    
</style>
<div class="a6printw" id="printdivB">
    {foreach $data|default:array() as $k=>$v}
        {if $v.stamp!=""} 
        <div id="printCssB">
            <!-- {$v.stamp} -->

            <!-- {$v.title|default:''} + {$c2_arr[$v.cpkg]|default:''} + {$v.num}-{$v.sort}{if $v.num != 1} + <span style="color:red;font-weight: bold;">combine</span>{/if} <br /> -->
            <img src="{$v.stamp|default:''}" id="printCssImgB" class="pics" />
        </div>
        {/if}
    {/foreach}
</div>
<div class="box-footer">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button>
</div>
<script>
    $(document).ready(function (e) {
        //tif2img("#printdiv");
    });

    function printSuccess(){
        var userResponse1 = confirm("你确定要批量修改邮票状态吗？请先确认打印无误");
        if (userResponse1) {
            console.log("用户点击了确定。");
            // 执行操作
            $.ajax({
                url: '/index.php/htgl/sorder/dprintStamp',
                type: 'GET',
                data:{
                    'lstNidArr': {$lstNidArr}
                },
                contentType: "application/json",
                async: true,
                dataType: 'json',
                success: function(data){
                    if(data == 1){
                        alert('修改批量邮票状态成功');
                        location.reload();
                    }else {
                        alert('修改批量邮票状态失败');
                    }
                },
            });
        } else {
            console.log("用户点击了取消。");
            // 执行其他操作或者什么都不做
        }
        
        // alert(123);
    }
</script>