{include file='admin/public/cheader.tpl'}
<script src="{$env.public}/jquery/printThis.js"></script>
<!--日期框-->
<link href="{$env.public}/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<script src="{$env.public}/datepicker/bootstrap-datetimepicker.min.js"></script>
<script src="{$env.public}/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"></script>
<style>
    th {
        font-size: 16px;
    }

    .tprice {
        font-weight: 600;
    }
</style>
<div class="box">

    <form class="form-horizontal" id="search_form" action="{URI}">
        <div class="box-header">
            <h3 class="box-title">{$modeName}列表</h3>
        </div>
        <div class="box-body searchform">
            <!-- /.box-header -->
            <div class="box-body">
                <div class="row">

                    <div class="col-sm-6">
                        {formgroup t='input' desc="输入客户" id="stitle" name="stitle" value="{$s.stitle|default:''}"}
                    </div>
                    <div class="col-sm-6">
                        {formgroup t='sselect' desc="订单状态" id="sstatus" name="sstatus" lst=$env.sstatus
                        issearch=1 value=$s.sstatus|default:''}
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-4">
                        {formgroup t='sselect' desc="产品类型" id="scplx" name="scplx" lst=$c1_arr
                        issearch=1 value=$s.scplx|default:''}
                    </div>

                    <div class="col-sm-4">
                        {formgroup t='sselect' desc="订单店主" id="sdddz" name="sdddz" lst=$c3_arr
                        issearch=1 value=$s.sdddz|default:''}
                    </div>

                    <div class="col-sm-4">
                        {formgroup t='sselect' desc="邮票状态" id="sstamp" name="sstamp" lst=$env.sstamp
                        issearch=1 value=$s.sstamp|default:''}
                    </div>
                    
                    {if $user_arr}
                    <div class="col-sm-4">
                        {formgroup t='sselect' desc="订单所属账号" id="creater" name="creater" lst=$user_arr
                        issearch=1 value=$s.creater|default:''}
                    </div>
                    {/if}
                    
                    <div class="col-sm-6">
                        {formgroup t='inputdatepicker2' desc='订单提交时间（开始）' id="stjsjs" name="stjsjs"
                        style="datepicker"
                        value=$s.stjsjs|default:''}
                    </div>
                    <div class="col-sm-6">
                        {formgroup t='inputdatepicker2' desc='订单提交时间（结束）' id="stjsje" name="stjsje"
                        style="datepicker"
                        value=$s.stjsje|default:''}
                    </div>

                    <div class="col-sm-6">
                        {formgroup t='inputdatepicker2' desc='订单生产时间（开始）' id="sscsjs" name="sscsjs"
                        style="datepicker"
                        value=$s.sscsjs|default:''}
                    </div>
                    <div class="col-sm-6">
                        {formgroup t='inputdatepicker2' desc='订单生产时间（结束）' id="sscsje" name="sscsje"
                        style="datepicker"
                        value=$s.sscsje|default:''}
                    </div>
                </div>
            </div>
            <div class="btn-group">
                {formgroup t='addbtn' url=RELROUTE|cat:"/"|cat:MODULE|cat:"/add" isbtn=$qx.add type="lg"
                desc=$modeName|cat:"添加"}
                {formgroup t='searchbtn' type="lg"}
            </div>
        </div>
        <!-- /.box-body -->
</div>
</form>

</div>
<div class="box">
    <!-- /.box-header -->
    <div class="box-body no-padding">
        <table class="table">
            <tbody>
                <tr>
                    <th>订单ID</th>
                    <th>客户名称<br />店主归属</th>
                    <th>下单时间<br />提交时间</th>
                    <th>订单状态</th>
                    <th>所属账号/数量</th>
                    <th>价格(美金)</th>
                    <th>操作</th>
                </tr>
                {foreach $lst as $k=>$v}
                <tr>
                    <td>{$v.nid|default:''}</td>
                    <td>
                        {$v.title|default:''}
                        <br />店主：{$c3_arr[$v.s1]|default:''}
                    </td>
                    <td>
                        下单时间：{date("Y-m-d H:i", $v['oid'])|default:''}
                        {if $v['a1']|default:0 > 0}
                        <br />提交时间：{date("Y-m-d H:i", $v['a1'])|default:''}
                        {/if}
                    </td>
                    <td>
                        {$env['sstatus'][$v.status]|default:''}
                        {if $v.status|default:'1' eq 3}
                        <br />生产日期：{date("Y-m-d H:i", $v['ptime'])|default:''}
                        {/if}
                    </td>
                    <td>{$v.creater|default:''}
                        <br />
                        {if $v.stamp}
                        <a href="{$v.stamp|default:''}" target="_blank">查看邮票</a>
                        {/if}
                    </td>
                    <td nid="{$v.nid|default:''}" class="tprice">0</td>
                    <td>

                        <div class="btn-group">
                            {if $v.status|default:'1'
                            <= 1} {formgroup t='addbtn' url=RELROUTE|cat:"/"|cat:MODULE|cat:"/add2?cid="|cat:$v.nid
                                desc='添加产品' isbtn=$qx.add2} {formgroup t='editbtn'
                                url=RELROUTE|cat:"/"|cat:MODULE|cat:"/update?nid="|cat:$v.nid isbtn=$qx.update}
                                {formgroup t='delbtn' url=RELROUTE|cat:"/"|cat:MODULE|cat:"/delete?nid="|cat:$v.nid
                                isbtn=$qx.delete} {/if} </div> <br />
                            <div class="btn-group">
                                {if $v.status|default:'1' <= 1} <button type="button"
                                    onclick="doDelete('确定要提交生产吗？','{RELROUTE}/{MODULE}/doprod?nid={$v.nid|default:0}');"
                                    class="btn btn-success btn-sm">
                                    <i class="fa fa-check"></i>&nbsp;&nbsp;提交生产
                                    </button>
                                    {/if}
                                    {formgroup t='editbtn'
                                    url=RELROUTE|cat:"/"|cat:MODULE|cat:"/update3?nid="|cat:$v.nid desc='更新邮票'
                                    isbtn=$qx.update3}
                                    {if $qx.mprint|default:false}
                                    {if $v.status|default:'1' >= 2}
                                    <button type="button"
                                        onclick="showMyModal('打印面单','{RELROUTE}/{MODULE}/mprint?nid={$v.nid|default:0}');"
                                        class="btn btn-info btn-sm {if $v.a2|default:0>0}disabled{/if}"
                                        data-toggle="modal" data-target="#myModal" value="">
                                        【{$v.a2|default:0}】打印面单
                                    </button>
                                    {/if}
                                    {/if}
                            </div>

                    </td>
                </tr>

                {foreach $v.plst|default:array() as $kk=>$vv}
                <tr>
                    <td>产品ID：{$vv.nid|default:''}<br />{$vv.pm|default:''}</td>
                    <td>
                        {if $vv.pic}
                        <img src="{$vv.pic|default:''}.png" style="height: 50px;" />
                        <br />
                        <a href="{$vv.pic|default:''}"
                            download="{getdlname($v, $vv, $c1_arr, $c2_arr, $c3_arr)|default:''}"
                            target="_blank">下载生产文件</a>
                        {/if}
                    </td>
                    <td class="wb">
                        类型：{$c1_arr[$vv.cplx]|default:''}
                        <br />
                        规格：{$c2_arr[$vv.cpkg]|default:''}
                        <br />
                        内容：{$vv.s1|default:''}
                    </td>
                    <td>
                        {$env['pstatus'][$vv.status]|default:''}
                        {if $vv['a1']|default:0 > 0}
                        <br />提交时间：{date("Y-m-d H:i", $vv['a1'])|default:''}
                        {/if}
                        {if $vv.status|default:'1' eq 3}
                        <br />生产时间：{date("Y-m-d H:i", $vv['ptime'])|default:''}
                        {/if}
                    </td>
                    <td>数量：{$vv.num|default:''} </td>
                    <td class="cprice_{$v.nid|default:''}">{($c2_1_arr[$vv.cpkg]*$vv.num)|default:0}</td>
                    <td>
                        <div class="btn-group">
                            {if $vv.status|default:'1' <= 1} {formgroup t='editbtn'
                                url=RELROUTE|cat:"/"|cat:MODULE|cat:"/update2?nid="|cat:$vv.nid isbtn=$qx.update2}
                                {formgroup t='delbtn' url=RELROUTE|cat:"/"|cat:MODULE|cat:"/delete2?nid="|cat:$vv.nid
                                isbtn=$qx.delete2} {/if} </div> </td> </tr> {/foreach} </tr> <tr>
                    <td colspan="20" style="background-color: #f4f4f4;height: 5px;font-size: 1px;">&nbsp;</td>
                </tr>
                {/foreach}

            </tbody>
        </table>
    </div>
    <!-- /.box-body -->
    <div class="box-footer clearfix">
        {formgroup t='page' page=$page}
    </div>
</div>
<!-- /.box -->
<script>
    function doprint() {
        $("#printdiv").printThis({
            debug: false,
            importCSS: true,
            importStyle: true,
            printContainer: true,
            //    loadCSS: "/Content/Themes/Default/style.css", 
            pageTitle: "",
            removeInline: false,
            printDelay: 333,
            header: null,
            formValues: false
        });
    }

    function showPic(pic) {
        //alert(pic);
        showMyModalHtml("显示图片", "<img src='" + pic + "' class='m_img'/>")
    }

    $(document).ready(function (e) {

        $("td.tprice").each(function (index, element) {
            //console.log($(this).attr('nid'));
            var totle = 0.0;
            var nid = $(this).attr('nid');
            $("td.cprice_" + nid).each(function (index, element) {
                totle = totle + parseFloat($(this).html());
            });
            $(this).html(totle);
        });

        $('.datepicker').datetimepicker({
            format: "yyyy-mm-dd", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'month',
            autoclose: true //选择日期后自动关闭
        });

        //tif2img();

    });
</script>
{include file='admin/public/cfooter.tpl'}