{include file='admin/public/cheader.tpl'}
{include file='admin/public/fileinputjs.tpl'}
<!--日期框-->
<link href="{$env.public}/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<script src="{$env.public}/datepicker/bootstrap-datetimepicker.min.js"></script>
<script src="{$env.public}/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"></script>

<link href="{$env.public}/bootstrap-select/css/bootstrap-select.min.css" rel="stylesheet">
<script src="{$env.public}/bootstrap-select/js/bootstrap-select.min.js"></script>
<script src="{$env.public}/bootstrap-select/js/i18n/defaults-zh_CN.min.js"></script>

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">产品{if ACTION eq "add2"}添加{/if}{if ACTION eq "update2"}修改{/if}</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="{URI}" method="post">
                <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
                <div class="box-body">


                    {include file='admin/sorder/intut_p.tpl'}

                    <input type="hidden" name="nid" id="nid" value="{$data.nid|default:0}">
                    <input type="hidden" name="cid" id="cid" value="{$data.cid|default:0}">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="{RELROUTE}/{MODULE}/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {

        $('#input_form').submit(function (e) {
            if ($.trim($('#pic0').val()) == '') {
                show_error('产品图片不能为空');
                $('#pic0').focus();
                return false;
            }
        });
    });
</script>

{include file='admin/public/cfooter.tpl'}