{include file='admin/public/cheader.tpl'}
{include file='admin/public/fileinputjs.tpl'}
<!--日期框-->
<link href="{$env.public}/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<script src="{$env.public}/datepicker/bootstrap-datetimepicker.min.js"></script>
<script src="{$env.public}/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"></script>

<link href="{$env.public}/bootstrap-select/css/bootstrap-select.min.css" rel="stylesheet">
<script src="{$env.public}/bootstrap-select/js/bootstrap-select.min.js"></script>
<script src="{$env.public}/bootstrap-select/js/i18n/defaults-zh_CN.min.js"></script>

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">{$modeName}{if ACTION eq "add"}添加{/if}{if ACTION eq "update"}修改{/if}</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="{URI}" method="post">
                <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
                <div class="box-body">


                    {formgroup t='input' desc="客户名称<code>(*)</code>" id="title" name="title"
                    value=$data.title|default:''
                    validator=" required data-bv-notempty-message='客户名称不能为空' "}

                    {formgroup t='inputdatepicker' desc='下单时间' id="oid" name="oid"
                    style="datepicker_oid"
                    value=$data.oid|default:date("Y-m-d H:i",time())}

                    <!--
                    {formgroup t='input' desc="订单内容" id="description" name="description"
                    value=$data.description|default:''}
                    {formgroup t='input' desc="订单备注" id="cont" name="cont"
                    value=$data.cont|default:''}
                    -->
                    {formgroup t='select' desc="店主" id="s1" name="s1" lst=$c3_arr value=$data.s1|default:0}

                    {if ACTION eq "add"}
                    {include file='admin/sorder/intut_p.tpl'}
                    {/if}

                    <input type="hidden" name="nid" id="nid" value="{$data.nid|default:0}">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="{RELROUTE}/{MODULE}/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {
        //$('#input_form').bootstrapValidator();
        /***
        $('.datepicker_oid').datetimepicker({
            format: "yyyy-mm-dd hh:ii", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'day',
            autoclose: true //选择日期后自动关闭
        });
        **/
        $('#input_form').submit(function (e) {

            // if($('#cplx').val() == 106 && $('#cpkg').val() == 118 || $('#cpkg').val() == 120 || $('#cpkg').val() == 121) {
            //     if ($("#num").val() % 2 !== 0) {
            //         show_error('数量必须为偶数');
            //         $('#num').focus();
            //         return false;
            //     }
            // } 


            if ($.trim($('#title').val()) == '') {
                show_error('客户名称不能为空');
                $('#title').focus();
                return false;
            }
            /***{if ACTION eq "add"}**/
            // if ($.trim($('#pic').val()) == '') {
            //     show_error('产品图片不能为空');
            //     $('#pic').focus();
            //     return false;
            // }
            /***{/if}**/

        });
    });
</script>

{include file='admin/public/cfooter.tpl'}