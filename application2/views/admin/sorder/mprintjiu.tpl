<div class="box-header">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <!-- <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button> -->
    <button onclick="printSuccess()" class="btn btn-danger pull-right">打印成功请点击批量修改产品状态</button>
    
</div>
<style>
    #printdiv {
        width: 12.4cm;
        
    }
    #printCss {
        width:12.4cm;
        height: 19cm;
        text-align:center;
        /* border: 0.5cm solid red; */
        position: relative;
        box-sizing: border-box;
    }

    #printCssImg {
        width: 12.4cm;
        height: 19cm;
        /* height: auto; */
    }

    #printSpan {
        color:red !important;
        font-weight: bold !important;
    }
    
    .title {
        width: 11.6cm;
        height: 1cm;
        text-align: center;
        /* line-height: 1cm; */
        position: absolute;
        top: 4.5mm;
        left: 4mm;
        font-size: 3mm;
    }
    
</style>
<div class="a6printw" id="printdiv">

    {if isset($type)&&$type=='stamp'} 
        {foreach $data|default:array() as $k=>$v}
        {if $v.stamp!=""} 
        <div class="a6print pagep" style="height:{{$aconfig.conf_fsheete|default:900}}px;float:left;">
            <!-- {$v.stamp} -->

            <!-- {$v.title|default:''} + {$c2_arr[$v.cpkg]|default:''} + {$v.num}-{$v.sort}{if $v.num != 1} + <span style="color:red;font-weight: bold;">combine</span>{/if} <br /> -->
            <img src="{$v.stamp|default:''}" class="pics" />
        </div>
        {/if}
        {/foreach}
    {else}
        <!-- <img src=" {$data.stamp|default:''}" style="width:560px;height:{{$aconfig.conf_fsheete-40|default:880}}px;"> -->
        {foreach $data|default:array() as $k=>$v}
        <div id="printCss">
            <div class="title">
                {if $v.cpkg == 118 || $v.cpkg == 120 || $v.cpkg == 121}
                    {$v.title|default:''} + {$c2_arr[$v.cpkg]|default:''} + {$v.numSum}-{$v.sort - 1} / {$v.numSum}-{$v.sort}{if $v.numSum != 1} + <span id="printSpan">combine</span>{/if}
                {else}
                    {$v.title|default:''} + {$c2_arr[$v.cpkg]|default:''} + {$v.numSum}-{$v.sort}{if $v.numSum != 1} + <span id="printSpan">combine</span>{/if}
                {/if}
                
            </div>
            <!-- <span id="printCssSpan">{$v.title|default:''} + {$c2_arr[$v.cpkg]|default:''} + {$v.num}-{$v.sort}{if $v.num != 1} + <span style="color:red;font-weight: bold;">combine</span>{/if} <br /></span> -->
            <img src="{$v.pic|default:''}.png" id="printCssImg" class="pics" />
            
        </div>
        {/foreach}
    {/if}
</div>
<div class="box-footer">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button>
</div>
<script>
    $(document).ready(function (e) {
        //tif2img("#printdiv");
    });

    function printSuccess(){
        var userResponse = confirm("你确定要批量修改产品状态吗？请先确认打印无误");
        if (userResponse) {
            console.log("用户点击了确定。");
            // 执行操作
            $.ajax({
                url: '/index.php/htgl/sorder/dprintAll',
                type: 'GET',
                data:{
                    'idAll': {$idAll}
                },
                contentType: "application/json",
                async: true,
                dataType: 'json',
                success: function(data){
                    if(data == 1){
                        alert('批量生产状态修改成功');
                        location.reload();
                    }else {
                        alert('批量生产状态修改失败');
                    }
                },
            });
        } else {
            console.log("用户点击了取消。");
            // 执行其他操作或者什么都不做
        }
        
        // alert(123);
    }
</script>