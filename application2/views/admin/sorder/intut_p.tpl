<hr />

    <div class="form-group">
        <label class="col-sm-2 control-label"></label>
        <label class="col-sm-10">订单产品</label>
        
    </div>
    {if $data==[]}
    <div id="div">
        <input type="text" value="1" id="sort" hidden="true">
        <div id="div1">
            <!-- {formgroup t='uploadpic' desc="产品生产文件<code>(*)</code>" id="pic1" name="pic1" value=$data.pic|default:''}
            {formgroup t='select' desc="产品类型" id="cplx1" name="cplx1" lst=$c1_arr value=$data.cplx|default:''}
            {formgroup t='select' desc="产品规格" id="cpkg1" name="cpkg1" lst=array() value=$data.cpkg|default:''}
            
            <div class="form-group">
                <label class="col-sm-2 control-label">产品数量</label>
                <div class="col-sm-10">
                    <input class="form-control" type="number" name="num1" id="num1" value="{$data.num|default:'1'}" required=""
                        data-bv-notempty-message="产品数量不能为空">
                </div>
            </div> -->
            <!-- {formgroup t='input' desc="产品内容" id="ss11" name="ss11" value=$data.s1|default:''} -->
             <div id="picHtml">
                <div>
                    <div class='form-group'>
                        <label class='col-sm-2 control-label'>产品生产文件</label>
                        <div class='col-sm-5'>
                            <input type='hidden' name='pic0' id='pic0' value=''  />
                            <input id='pic0-up' name='pic0-up' type='file' accept='image/*' />
                        </div>
                    </div>
                    {formgroup t='select' desc="产品类型" id="cplx0" name="cplx0" lst=$c1_arr value=$data.cplx|default:''}
                    {formgroup t='select' desc="产品规格" id="cpkg0" name="cpkg0" lst=array() value=$data.cpkg|default:''}
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">产品数量</label>
                    <div class="col-sm-10">
                        <input class="form-control" type="number" onblur="headerBlur(0)" name="num0" id="num0" value="{$data.num|default:'1'}" required=""
                            data-bv-notempty-message="产品数量不能为空">
                    </div>
                </div>
                {formgroup t='input' desc="产品内容" id="ss10" name="ss10" value=$data.s1|default:''}
             </div>
        </div>
    </div>
    <button type="button"
            onclick="addasd1()"
            class="btn btn-success btn-sm " data-toggle="modal" value="">
            添加产品
        </button>
    {else}
        <div>
            {formgroup t='uploadpic' desc="产品生产文件<code>(*)</code>" id="pic0" name="pic0" value=$data.pic|default:''}
            {formgroup t='select' desc="产品类型" id="cplx0" name="cplx0" lst=$c1_arr value=$data.cplx|default:''}
            {formgroup t='select' desc="产品规格" id="cpkg0" name="cpkg0" lst=array() value=$data.cpkg|default:''}
            <div class="form-group">
                <label class="col-sm-2 control-label">产品数量</label>
                <div class="col-sm-10">
                    <input class="form-control" type="number" name="num0" id="num0" onblur="headerBlur(0)" value="{$data.num|default:'1'}" required=""
                        data-bv-notempty-message="产品数量不能为空">
                </div>
            </div>
            {formgroup t='input' desc="产品内容" id="ss10" name="ss10" value=$data.s1|default:''}
        </div>
    {/if}


<script>
    var c2_sjson = '{$c2_json|default:array()}';
    var c2json = $.parseJSON(c2_sjson);
    var cpgk = '{$data.cpkg|default:0}';
    var c1_arr = '{$c1_arr}';
    var c1_json = '{$c1_json}';
    
    $(document).ready(function (e) {
        setCPGG($('#cplx0').val());
        $('#cplx0').on('change', function () {
            setCPGG($('#cplx0').val());
        });
        
        $('#cpkg0').on('change', function () {
            var cplx = parseInt($('#cplx0').val());
            var cpkg = parseInt($('#cpkg0').val());
            
            if(cplx == 106 && cpkg == 118 || cpkg == 120 || cpkg == 121) {
                $('#num0').val(2);
            }
        });        
    });
    // 初始化默认上传图片
    test()


    function setCPGG(id) {
        $("#cpkg0").empty();
        $.each(c2json[id], function (key, value) {
            console.log(key + ": " + value);
            var sleected = '';
            if (key == cpgk) {
                sleected = ' selected="selected" '
            }
            $("#cpkg0").append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
        });
    }
    
    function addasd() {
        $('#sort').val(parseInt($('#sort').val()));
        var sort = $('#sort').val() + 1; 
        // 先修改元素的id和name
        $('#pic1').attr('id','pic23');
        
        var elementToCopy = document.getElementById('div1');
        console.log(elementToCopy);
        // $('#pic1').attr('name','pic23');
        // elementToCopy.children[0].children[1].children[0].id = 'pic'+ sort;
        // elementToCopy.children[0].children[1].children[0].name = 'pic' + sort;
        // console.log($('#pic23').attr('id'));
        elementToCopy.children[1].children[1].children[0].id = 'cplx' + sort;
        elementToCopy.children[1].children[1].children[0].name = 'cplx' + sort;

        elementToCopy.children[2].children[1].children[0].id = 'cpkg' + sort;
        elementToCopy.children[2].children[1].children[0].name = 'cpkg' + sort;

        elementToCopy.children[3].children[1].children[0].id = 'num' + sort;
        elementToCopy.children[3].children[1].children[0].name = 'num' + sort;

        // elementToCopy.children[4].children[1].children[0].id = 'ss1' + sort;
        // elementToCopy.children[4].children[1].children[0].name = 'ss1' + sort;


        // 再复制元素
        var copiedElement = elementToCopy.cloneNode(true);
        copiedElement.id = 'div' + sort;
        var body = document.getElementById('div');
        body.appendChild(copiedElement);

        // 再把元素原来的id和name改回来
        elementToCopy.children[0].children[1].children[0].id = 'pic1';
        elementToCopy.children[0].children[1].children[0].name = 'pic1';

        elementToCopy.children[1].children[1].children[0].id = 'cplx1';
        elementToCopy.children[1].children[1].children[0].name = 'cplx1';

        elementToCopy.children[2].children[1].children[0].id = 'cpkg1';
        elementToCopy.children[2].children[1].children[0].name = 'cpkg1';

        elementToCopy.children[3].children[1].children[0].id = 'num1';
        elementToCopy.children[3].children[1].children[0].name = 'num1';

        elementToCopy.children[4].children[1].children[0].id = 'ss11';
        elementToCopy.children[4].children[1].children[0].name = 'ss11';

        var body = document.getElementById('div');
        body.appendChild(copiedElement);

        $("#cpkg"+sort).empty();
        $.each(c2json[$('#cplx'+sort).val()], function (key, value) {
            console.log(key + ": " + value);
            var sleected = '';
            if (key == cpgk) {
                sleected = ' selected="selected" '
            }
            $("#cpkg"+sort).append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
        });
        $('#cplx'+sort).on('change', function () {
            $("#cpkg"+sort).empty();
            $.each(c2json[$('#cplx'+sort).val()], function (key, value) {
                console.log(key + ": " + value);
                var sleected = '';
                if (key == cpgk) {
                    sleected = ' selected="selected" '
                }
                $("#cpkg"+sort).append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
            });
        });

        $('#cpkg'+sort).on('change', function () {
            console.log(123);
            var cplx = parseInt($('#cplx'+sort).val());
            var cpkg = parseInt($('#cpkg'+sort).val());
            
            if(cplx == 106 && cpkg == 118 || cpkg == 120 || cpkg == 121) {
                $('#num'+sort).val(2);
            }
        }); 
    }

    // 新增插入元素
    var i = 0
    let addFormArr = []
    function addasd1(){
        i++
        var html = `
            <div>
                    <div class='form-group'>
                        <label class='col-sm-2 control-label'>产品生产文件</label>
                        <div class='col-sm-5'>
                            <input type='hidden' name='pic`+i+`' id='pic`+i+`' value=''  />
                            <input id='pic`+i+`-up' name='pic`+i+`-up' type='file' accept='image/*' />
                        </div>
                    </div>
                    {formgroup t='select' desc="产品类型" id="cplx`+i+`" name="cplx`+i+`" index="`+i+`" lst=$c1_arr value=$data.cplx|default:''}
                    {formgroup t='select' desc="产品规格" id="cpkg`+i+`" name="cpkg`+i+`" lst=array() value=$data.cpkg|default:''}
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">产品数量</label>
                    <div class="col-sm-10">
                        <input class="form-control" type="number" name="num`+i+`" id="num`+i+`" onblur="headerBlur(`+i+`)" value="{$data.num|default:'1'}" required=""
                            data-bv-notempty-message="产品数量不能为空">
                    </div>
                </div>
                {formgroup t='input' desc="产品内容" id="ss1`+i+`" name="ss1`+i+`" value=$data.s1|default:''}
             </div>
        `
        // 插入
        addFormArr.push({})
        $('#picHtml').append(html)
        test1()
        setCPGG1($('#cplx'+i).val(),i)
        changeCpkg(i);
    }
    function changeCpkg(i){
        $('#cpkg'+i).on('change', function () {
            var cplx = parseInt($('#cplx'+i).val());
            var cpkg = parseInt($('#cpkg'+i).val());
            
            if(cplx == 106 && cpkg == 118 || cpkg == 120 || cpkg == 121) {
                $('#num'+i).val(2);
            }
        }); 
    }
    // 点击初始化上传图片
    function test1(){
        $('#pic'+i+'-up').fileinput({
            language: 'zh',
            uploadUrl:'/index.php//upload/home/<USER>',
            uploadAsync: true,
            allowedFileExtensions : ['png', 'jpg','jpeg','tif'],
            allowedPreviewTypes : ['image'],
            overwriteInitial: true,
            showUpload:false,
            defaultPreviewContent: '<img src=\"/public/images/nopic.png\" style=\"width: 100px;height: 100px;\" >',
            }).on('filebatchselected', function(event, files) {
                $('#pic'+i+'-up').fileinput('upload');
            }).on('filecleared', function(event, id) {
               $('#pic'+i).val('');
            }).on('fileuploaded', function (event, data) {
                var d=data.response;
            if(d.s==1){
            $('#pic'+i).val(d.www_path);
                toastr.success('上传成功');
            }else{
                toastr.error(d.msg);
            }
            });
    }
    // 初始化默认上传图片
    function test(id){
        $('#pic0-up').fileinput({
            language: 'zh',
            uploadUrl:'/index.php//upload/home/<USER>',
            uploadAsync: true,
            allowedFileExtensions : ['png', 'jpg','jpeg','tif'],
            allowedPreviewTypes : ['image'],
            overwriteInitial: true,
            showUpload:false,
            defaultPreviewContent: '<img src=\"/public/images/nopic.png\" style=\"width: 100px;height: 100px;\" >',
            }).on('filebatchselected', function(event, files) {
                $('#pic0-up').fileinput('upload');
            }).on('filecleared', function(event, id) {
               $('#pic0').val('');
            }).on('fileuploaded', function (event, data) {
                //console.log(data.response);
                var d=data.response;
            if(d.s==1){
            $('#pic0').val(d.www_path);
                toastr.success('上传成功');
            }else{
                toastr.error(d.msg);
            }
            });
    }
    function setCPGG1(id,match) {
        $("#cpkg"+match).empty();
        $.each(c2json[id], function (key, value) {
            var sleected = '';
            if (key == cpgk) {
                sleected = ' selected="selected" '
            }
            $("#cpkg"+match).append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
        });
    }
    function headerChange(id){
        const regex = /\d+/g;
        let match = regex.exec($(id).attr('id'))[0]
        // 判断是否产品类型，如果是产品类型则请求使用一下方法
        if($(id).attr('id').indexOf('cplx') === 0){
            setCPGG1($(id).val(),match)
        }
    }

    function headerBlur(value){
        
        var cplx = parseInt($('#cplx'+value).val());
        var cpkg = parseInt($('#cpkg'+value).val());
        
        if(cplx == 106 && cpkg == 118 || cpkg == 120 || cpkg == 121) {
            if ($("#num"+value).val() % 2 !== 0) {
                show_error('数量必须为偶数');
                return false;
            }
        }
    }

</script>