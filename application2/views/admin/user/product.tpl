{include file='admin/public/cheader.tpl'}
<form method="post" action="{RELROUTE}/user/product_update">
    <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
    <input type="hidden" name="uid" value="{$uid}">

    <h3 class="box-title">分配产品</h3>

    <div class="box-body table-responsive no-padding">
            
            
            
            <div class="col-xs-12" >
               
                <ul class="role_control_setting" style=''>
               
                    {foreach $lst as $k=>$v}
                    <li>
                        {formgroup t='authcbox' name='authaction[]'
                        value=$v.nid desc=$v.title
                        auth=$role_auth}
                    </li>
                    {/foreach}
                </ul>    
            </div>    
            
           
            


        <div class="box-footer">
            &nbsp;<br />&nbsp;<br />
            <input class="btn" type="button" onclick="location='{RELROUTE}/user/list'" value="返回">
            <input class="btn btn-info pull-right" type="submit" value="更 新">
        </div>
    </div>
</form>
<script type="text/javascript">
    $(document).ready(function (e) {
        $('.app').click(function (e) {
            //console.log($(this).val());
            var o = $(this).is(':checked');
            var a = $(this).val().split('/');
            //console.log(a.length);
            if (a.length == 1) {
                //app
                $("#app_" + a[0] + " input").prop("checked", o);
            }
            if (a.length == 2) {
                //app
                $("#mod_" + a[0] + "_" + a[1] + " input").prop("checked", o);
            }
        });
    });
</script>
{include file='admin/public/cfooter.tpl' }