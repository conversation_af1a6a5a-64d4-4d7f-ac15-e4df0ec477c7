{include file='admin/public/cheader.tpl'}
<script src="{$env.public}/jquery/qrcode.min.js"></script>

<div class="box">

    <form class="form-horizontal" id="search_form" action="{URI}">
        <div class="box-header">
            <h3 class="box-title">{$modeName}列表</h3>
        </div>
        <div class="box-body searchform">
            <!-- /.box-header -->
            <div class="box-body">
                <div class="row">
                    <div class="col-sm-4">
                        {formgroup t='sselect' desc="单位" id="spid" name="spid" lst=$part_arr
                        value=$s.spid|default:'-99' }
                    </div>
                    <div class="col-sm-4">
                        {formgroup t='sselect' desc="角色" id="srid" name="srid" lst=$role_arr
                        value=$s.srid|default:'-99' }
                    </div>
                    <div class="col-sm-4">
                        {formgroup t='input' desc="姓名" id="skeyw" name="skeyw"
                        value=$s.skeyw|default:''}
                    </div>
                </div>

                <div class="btn-group">
                    {formgroup t='addbtn' url=RELROUTE|cat:"/user/add" isbtn=$qx.add desc=$modeName|cat:"添加"}
                    {formgroup t='searchbtn' }
                </div>
            </div>
            <!-- /.box-body -->
        </div>
    </form>

</div>
<div class="box">
    <div class="box-header">

    </div>
    <!-- /.box-header -->
    <div class="box-body table-responsive no-padding">
        <table class="table table-hover">
            <tbody>
                <tr>
                    <th>uid</th>
                    <th>角色</th>
                    <th>单位</th>
                    <th>用户名</th>
                    <th>姓名</th>
                    <th>邮箱</th>
                    <th>启用</th>
                    <th>最后登录时间/IP</th>
                    <th>操作</th>
                    {if $qx.founder}
                    <th>Authenticator</th>
                    {/if}
                </tr>
                {foreach $lst as $k=>$v}
                <tr>
                    <td>{$v.uid}</td>
                    <td>{$role_arr[$v.role_id]|default:''}</td>
                    <td>{$part_arr[$v.part_id]|default:''}</td>
                    <td>{$v.username}</td>
                    <td>{$v.nickname}</td>
                    <td>{$v.email}</td>
                    <td>{formgroup t='tf' id=$v.status}</td>
                    <td>{$v.last_login_time|date_format:"Y-m-d H:i:s"}<br />{$v.last_login_ip}</td>
                    <td>
                        <div class="btn-group">
                            {if $v.role_id eq 5}
                            {formgroup t='editbtn' url=RELROUTE|cat:"/user/teacher_update?uid="|cat:$v.uid
                            isbtn=$qx.teacher_update}
                            {else}
                            {formgroup t='editbtn' url=RELROUTE|cat:"/user/update?uid="|cat:$v.uid isbtn=$qx.update}
                            {/if}
                            {formgroup t='delbtn' url=RELROUTE|cat:"/user/delete?uid="|cat:$v.uid isbtn=$qx.delete}
                            {if $v.role_id eq 4}
                               <a href="{RELROUTE}/user/product?uid={$v.uid}" class="btn btn-success"><i class="fa fa-cogs"></i>&nbsp;&nbsp;分配产品</a>
                            {/if}
                        </div>
                    </td>
                    {if $qx.founder}
                    <td>
                        <input type="button" onclick="show_qrcode('{$v.username}','{$v.Authenticator}');"
                            class="btn btn-default btn-sm" value="查看">
                    </td>
                    {/if}
                </tr>
                {/foreach}

            </tbody>
        </table>
    </div>
    <!-- /.box-body -->
    <div class="box-footer clearfix">
        {formgroup t='page' page=$page}
    </div>
</div>
<!-- /.box -->
<script type="text/javascript">
    function show_qrcode(_uname, _code) {
        $('#my_qrcode').modal('show');
        $('#qrcode').html("");
        $('#qrcode').qrcode('otpauth://totp/' + _uname + '?secret=' + _code + '&issuer={$env.site_code}');
    }
</script>
<!-- /.modal -->
<div class="modal" id="my_qrcode">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header my-rzzc">
                <h5>Google身份验证器二维码</h5>
            </div>
            <div class="modal-body text-danger">
                <div id="qrcode"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
{include file='admin/public/cfooter.tpl'}