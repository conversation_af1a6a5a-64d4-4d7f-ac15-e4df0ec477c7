{include file='admin/public/cheader.tpl'}

<div class="box box-info">

    {if $qx.part_add}
    <form action="{RELROUTE}/user/part_add" method="post">
        <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
        <div class="box-header with-border">
            <h3 class="box-title">添加单位</h3>
        </div>
        <div class="box-body">
            <div class="input-group">
                <span class="input-group-addon"><b>单位名称</b></span>
                <input name="part_name" type="text" class="form-control" placeholder="单位名称" required />
                <span class="input-group-btn">
                    <button type="submit" class="btn btn-info btn-flat">添 加</button>
                </span>
            </div>
        </div>
    </form>
    {/if}

    <div class="box-header with-border">
        <h3 class="box-title">单位列表</h3>
    </div>

    {foreach $lst as $k=>$v}
    <form action="{RELROUTE}/user/part_update" method="post">
        <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
        <input type="hidden" name="part_id" value="{$v.part_id}">
        <div class="row box-body">
            <span class="col-sm-1 text-right"><b>ID:{$v.part_id}-单位名称：</b></span>
            <div class="col-sm-3">
                <input name="part_name" value="{$v.part_name}" type="text" class="form-control " placeholder="单位名称"
                    required />
            </div>
            <span class="col-sm-1 text-right"><b>启用:</b></span>
            <div class="col-sm-3">
                <select name="status" class="form-control">
                    {if $v.status eq 1}
                    <option value="1">是</option>
                    <option value="0">否</option>
                    {else}
                    <option value="0">否</option>
                    <option value="1">是</option>
                    {/if}
                </select>
            </div>
            <div class="col-sm-4 btn-group">
                {if $qx.part_update}
                <button type="submit" class="btn btn-primary"><i class="fa fa-refresh"></i>&nbsp;&nbsp;更新</button>
                {/if}
                {if $qx.part_delete}
                <button type="button" onclick="doDelete('确定要删除吗？','{RELROUTE|cat:"/user/part_delete?part_id="|cat:$v.part_id}');"
                    class="btn btn-warning"><i class="fa fa-close"></i>&nbsp;&nbsp;删除</button>
                {/if}
            </div>
        </div>

    </form>
    {/foreach}
</div>
{include file='admin/public/cfooter.tpl' }