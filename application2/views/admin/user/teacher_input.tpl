{include file='admin/public/cheader.tpl'}

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">教师{if ACTION eq "teacher_add"}添加{/if}{if ACTION eq
                    "teacher_update"}修改{/if}</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="{URI}" method="post">
                <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
                <div class="box-body">
                    {if ACTION eq "teacher_add"}
                    {formgroup t='input' desc="手机号(账号)" id="username" name="username" value=$data.username|default:''}
                    {/if}
                    {if ACTION eq "teacher_update"}
                    <input type="hidden" name="username" id="username" value="{$data.username|default:''}">
                    {formgroup t='desc' desc="手机号(账号)" value=$data.username}
                    {/if}
                    {formgroup t='input' desc="用户邮箱" id="email" name="email" value=$data.email|default:''}
                    {formgroup t='input' desc="姓名" id="nickname" name="nickname" value=$data.nickname|default:''}

                    {formgroup t='select' desc="所属单位" id="part_id" name="part_id" lst=$part_arr
                    value=$data.part_id|default:''}
                    {formgroup t='select' desc="是否启用" id="status" name="status" lst="tf"
                    value=$data.status|default:1}
                    {formgroup t='input' desc="备注" id="remark" name="remark" value=$data.remark|default:''}

                    {if ACTION eq "teacher_update"}

                    {formgroup t='desc' desc="注册时间" value=$data.regdate|date_format:"Y-m-d H:i:s"}
                    {formgroup t='desc' desc="注册IP" value=$data.regip}
                    {formgroup t='desc' desc="最后登录时间" value=$data.last_login_time|date_format:"Y-m-d H:i:s"}
                    {formgroup t='desc' desc="最后登录ip" value=$data.last_login_ip}
                    {formgroup t='desc' desc="登陆次数" value=$data.login_count}
                    {formgroup t='desc' desc="" value="<b>注意：以下密码不修改请留空</b>"}
                    {/if}

                    {formgroup t='input' desc="密码" id="password" name="password" type="password" value=""}
                    {formgroup t='input' desc="密码确认" id="apassword" name="apassword" type="password" value=""}

                    <hr />

                    <div class="row">
                        <div class="col-sm-4">
                            {formgroup t='input' desc=$slst['inputlst']['idcare'] id="idcare" name="idcare"
                            value=$data.idcare|default:''}
                        </div>
                        <div class="col-sm-4">
                            {formgroup t='input' desc=$slst['inputlst']['lxdh'] id="lxdh" name="lxdh"
                            value=$data.lxdh|default:''}
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-sm-4">
                            {formgroup t='select3' desc=$slst['inputlst']['sex'] id="json_sex" name="json[sex]"
                            lst=$slst['sexlst']
                            value=$data.json.sex|default:''}
                        </div>
                        <div class="col-sm-4">
                            {formgroup t='select3' desc=$slst['inputlst']['mz'] id="json_mz" name="json[mz]"
                            lst=$slst['mzlst']
                            value=$data.json.mz|default:''}
                        </div>
                        <div class="col-sm-4">
                            {formgroup t='input' desc=$slst['inputlst']['zzmm'] id="json_zzmm" name="json[zzmm]"
                            value=$data.json.zzmm|default:''}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-4">
                            {formgroup t='input' desc=$slst['inputlst']['jg'] id="json_jg" name="json[jg]"
                            value=$data.json.jg|default:''}
                        </div>
                        <div class="col-sm-4">
                            {formgroup t='input' desc=$slst['inputlst']['csny'] id="json_csny" name="json[csny]"
                            value=$data.json.csny|default:''}
                        </div>
                        <div class="col-sm-4">
                            {formgroup t='input' desc=$slst['inputlst']['jtzz'] id="json_jtzz" name="json[jtzz]"
                            value=$data.json.jtzz|default:''}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            {formgroup t='input' desc=$slst['inputlst']['zy'] id="json_zy" name="json[zy]"
                            value=$data.json.jg|default:''}
                        </div>
                        <div class="col-sm-6">
                            {formgroup t='select3' desc=$slst['inputlst']['zyzs'] id="json_zyzs" name="json[zyzs]"
                            lst=$slst['zyzs']
                            value=$data.json.zyzs|default:''}
                        </div>
                        <div class="col-sm-6">
                            {formgroup t='select3' desc=$slst['inputlst']['hzfs'] id="json_hzfs" name="json[hzfs]"
                            lst=$slst['hzfs']
                            value=$data.json.hzfs|default:''}
                        </div>
                        <div class="col-sm-6">
                            {formgroup t='input' desc=$slst['inputlst']['hzqx'] id="json_hzqx" name="json[hzqx]"
                            value=$data.json.hzqx|default:''}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-hover">
                                <tbody>
                                    <tr>
                                        <th colspan=10 style="text-align:center;">受教育情况</th>
                                    </tr>
                                    <tr>
                                        <th>起止年月</th>
                                        <th>毕业院校</th>
                                        <th>专业</th>
                                        <th>学历（学位）</th>
                                        <th>学制（年）</th>
                                        <th>办学形式</th>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[jyqzny1]"
                                                id="json_jyqzny1" value="{$data.json.jyqzny1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jybyxx1]"
                                                id="json_jybyxx1" value="{$data.json.jybyxx1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyzxzy1]"
                                                id="json_jyzxzy1" value="{$data.json.jyzxzy1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyxl1]" id="json_jyxl1"
                                                value="{$data.json.jyxl1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyxz1]" id="json_jyxz1"
                                                value="{$data.json.jyxz1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jybxxs1]"
                                                id="json_jybxxs1" value="{$data.json.jybxxs1|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[jyqzny2]"
                                                id="json_jyqzny2" value="{$data.json.jyqzny2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jybyxx2]"
                                                id="json_jybyxx2" value="{$data.json.jybyxx2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyzxzy2]"
                                                id="json_jyzxzy2" value="{$data.json.jyzxzy2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyxl2]" id="json_jyxl2"
                                                value="{$data.json.jyxl2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyxz2]" id="json_jyxz2"
                                                value="{$data.json.jyxz2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jybxxs2]"
                                                id="json_jybxxs2" value="{$data.json.jybxxs2|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[jyqzny3]"
                                                id="json_jyqzny3" value="{$data.json.jyqzny3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jybyxx3]"
                                                id="json_jybyxx3" value="{$data.json.jybyxx3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyzxzy3]"
                                                id="json_jyzxzy3" value="{$data.json.jyzxzy3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyxl3]" id="json_jyxl3"
                                                value="{$data.json.jyxl3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jyxz3]" id="json_jyxz3"
                                                value="{$data.json.jyxz3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[jybxxs3]"
                                                id="json_jybxxs3" value="{$data.json.jybxxs3|default:''}"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-hover">
                                <tbody>
                                    <tr>
                                        <th colspan=10 style="text-align:center;">职业生涯业绩/成就</th>
                                    </tr>
                                    <tr>
                                        <th>时间</th>
                                        <th>业绩/成就</th>
                                        <th>颁发单位</th>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[zysj1]" id="json_zysj1"
                                                value="{$data.json.zysj1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zycj1]" id="json_zycj1"
                                                value="{$data.json.zycj1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zybfdw1]"
                                                id="json_zybfdw1" value="{$data.json.zybfdw1|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[zysj2]" id="json_zysj2"
                                                value="{$data.json.zysj2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zycj2]" id="json_zycj2"
                                                value="{$data.json.zycj2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zybfdw2]"
                                                id="json_zybfdw2" value="{$data.json.zybfdw2|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[zysj3]" id="json_zysj3"
                                                value="{$data.json.zysj3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zycj3]" id="json_zycj3"
                                                value="{$data.json.zycj3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zybfdw3]"
                                                id="json_zybfdw3" value="{$data.json.zybfdw3|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[zysj4]" id="json_zysj4"
                                                value="{$data.json.zysj4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zycj4]" id="json_zycj4"
                                                value="{$data.json.zycj4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zybfdw4]"
                                                id="json_zybfdw4" value="{$data.json.zybfdw4|default:''}"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-hover">
                                <tbody>
                                    <tr>
                                        <th colspan=10 style="text-align:center;">职称晋升记录</th>
                                    </tr>
                                    <tr>
                                        <th>时间</th>
                                        <th>专业</th>
                                        <th>专业技术资格</th>
                                        <th>评定单位</th>
                                        <th>聘任单位</th>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[zcsj1]" id="json_zcsj1"
                                                value="{$data.json.zcsj1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zczy1]" id="json_zczy1"
                                                value="{$data.json.zczy1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zczg1]" id="json_zczg1"
                                                value="{$data.json.zczg1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zcpd1]" id="json_zcpd1"
                                                value="{$data.json.zcpd1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zcpr1]" id="json_zcpr1"
                                                value="{$data.json.zcpr1|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[zcsj2]" id="json_zcsj2"
                                                value="{$data.json.zcsj2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zczy2]" id="json_zczy2"
                                                value="{$data.json.zczy2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zczg2]" id="json_zczg2"
                                                value="{$data.json.zczg2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zcpd2]" id="json_zcpd2"
                                                value="{$data.json.zcpd2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zcpr2]" id="json_zcpr2"
                                                value="{$data.json.zcpr2|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[zcsj3]" id="json_zcsj3"
                                                value="{$data.json.zcsj3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zczy3]" id="json_zczy3"
                                                value="{$data.json.zczy3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zczg3]" id="json_zczg3"
                                                value="{$data.json.zczg3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zcpd3]" id="json_zcpd3"
                                                value="{$data.json.zcpd3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zcpr3]" id="json_zcpr3"
                                                value="{$data.json.zcpr3|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[zcsj4]" id="json_zcsj4"
                                                value="{$data.json.zcsj4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zczy4]" id="json_zczy4"
                                                value="{$data.json.zczy4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zczg4]" id="json_zczg4"
                                                value="{$data.json.zczg4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zcpd4]" id="json_zcpd4"
                                                value="{$data.json.zcpd4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[zcpr4]" id="json_zcpr4"
                                                value="{$data.json.zcpr4|default:''}"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-hover">
                                <tbody>
                                    <tr>
                                        <th colspan=10 style="text-align:center;">主要工作经历</th>
                                    </tr>
                                    <tr>
                                        <th>自何年何月</th>
                                        <th>至何年何月</th>
                                        <th>在何地、和单位任何职</th>
                                        <th>证明人</th>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[gzks1]" id="json_gzks1"
                                                value="{$data.json.gzks1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzjs1]" id="json_gzjs1"
                                                value="{$data.json.gzjs1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzzw1]" id="json_gzzw1"
                                                value="{$data.json.gzzw1|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzzm1]" id="json_gzzm1"
                                                value="{$data.json.gzzm1|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[gzks2]" id="json_gzks2"
                                                value="{$data.json.gzks2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzjs2]" id="json_gzjs2"
                                                value="{$data.json.gzjs2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzzw2]" id="json_gzzw2"
                                                value="{$data.json.gzzw2|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzzm2]" id="json_gzzm2"
                                                value="{$data.json.gzzm2|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[gzks3]" id="json_gzks3"
                                                value="{$data.json.gzks3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzjs3]" id="json_gzjs3"
                                                value="{$data.json.gzjs3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzzw3]" id="json_gzzw3"
                                                value="{$data.json.gzzw3|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzzm3]" id="json_gzzm3"
                                                value="{$data.json.gzzm3|default:''}"></td>
                                    </tr>
                                    <tr>
                                        <td><input class="form-control" type="text" name="json[gzks4]" id="json_gzks4"
                                                value="{$data.json.gzks4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzjs4]" id="json_gzjs4"
                                                value="{$data.json.gzjs4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzzw4]" id="json_gzzw4"
                                                value="{$data.json.gzzw4|default:''}"></td>
                                        <td><input class="form-control" type="text" name="json[gzzm4]" id="json_gzzm4"
                                                value="{$data.json.gzzm4|default:''}"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <hr />
                    {formgroup t='compressoruppic' desc="上传材料文件" id="ucl" name="ucl" css="display:none;"
                    value="" js="setpics(response)"}
                    <h4>材料列表</h4>
                    <div id="picsshow" class="row">
                    </div>

                    <input type="hidden" name="role_id" id="role_id" value="{$data.role_id|default:5}">
                    <input type="hidden" name="uid" id="uid" value="{$data.uid|default:0}">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="{RELROUTE}/{MODULE}/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>

<div style="display:none;" id="picsdiv">
    <div class="col-sm-2" style="text-align: center;">
        <input type="hidden" name="picsid[#id#]" id="picsid_#id#" value="#id#">
        <input type="hidden" name="pics[#id#]" id="pics_#id#" value="#www#">
        <img src='#www#' id='pics_img_#$id#' style='width:90%;height: 16rem;margin-bottom: 10px;'
            onclick="showPic('#www#')" />
        {formgroup t='select3' desc="<i class='fa fa-close rmpic' onclick='rmpic(this)'></i>" id="picsdesc_#id#"
        name="picsdesc[#id#]"
        lst=$slst['cllst']
        value=''}
    </div>
</div>

{include file='admin/wxuser/pjs.tpl'}

<script type="text/javascript">
    function rmpic(a) {
        $(a).parent().parent().parent().remove();
    }

    function setpics(d) {
        var html = $("#picsdiv").html();
        //console.log(html);
        html = replacesall(html, "#id#", d.data.id);
        html = replacesall(html, "#www#", d.data.www_path);
        $("#picsshow").append(html);

    }

    function resetpic(d) {
        $("#picsshow").html("");
        $.each(d, function (i, val) {
            var html = $("#picsdiv").html();
            //console.log(html);
            html = replacesall(html, "#id#", val.id);
            html = replacesall(html, "#www#", val.pic);
            $("#picsshow").append(html);
            $("#picsdesc_" + val.id).val(val.desc);
        });
    }

    // 加载完毕执行jq
    $(document).ready(function (e) {
        if ("{ACTION}" == "teacher_update") {
            opics = '{$data.pics|default:"[]"}';
            //console.log(opics);
            var opics = $.parseJSON(opics);
            resetpic(opics);
        }
        //+---------------
        //| 提交时检查数据是否正确
        //+---------------
        $('#input_form').submit(function (e) {

            if ($.trim($('#username').val()) == '') {
                show_error('用户名不能为空！');
                $('#username').focus();
                return false;
            }

            if ($.trim($('#email').val()) == '') {
                show_error('用户邮箱不能为空！');
                $('#email').focus();
                return false;
            }

            if ($.trim($('#password').val()) != '' || $.trim($('#apassword').val()) != '') {
                if ($.trim($('#password').val()) != $.trim($('#apassword').val())) {
                    show_error('两次密码输入不一致！');
                    $('#password').focus();
                    return false;
                }
            }

        });
    });
</script>

{include file='admin/public/cfooter.tpl'}