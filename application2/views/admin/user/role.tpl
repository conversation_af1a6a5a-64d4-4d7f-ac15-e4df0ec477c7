{include file='admin/public/cheader.tpl'}

<div class="box box-info">

    {if $qx.role_add}
    <form action="{RELROUTE}/user/role_add" method="post">
        <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
        <div class="box-header with-border">
            <h3 class="box-title">添加角色</h3>
        </div>
        <div class="box-body">
            <div class="input-group">
                <span class="input-group-addon"><b>角色名称</b></span>
                <input name="role_name" type="text" class="form-control" placeholder="角色名称" required />
                <span class="input-group-btn">
                    <button type="submit" class="btn btn-info btn-flat">添 加</button>
                </span>
            </div>
        </div>
    </form>
    {/if}

    <div class="box-header with-border">
        <h3 class="box-title">角色列表</h3>
    </div>

    {foreach $lst as $k=>$v}
    <form action="{RELROUTE}/user/role_update" method="post">
        <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
        <input type="hidden" name="role_id" value="{$v.role_id}">
        <div class="row box-body">
            <span class="col-sm-1 text-right"><b>ID:{$v.role_id}-角色名称：</b></span>
            <div class="col-sm-3">
                <input name="role_name" value="{$v.role_name}" type="text" class="form-control " placeholder="角色名称"
                    required  {if $v.role_id <= 2}readonly="readonly"{/if}/>
            </div>
            <span class="col-sm-1 text-right"><b>启用:</b></span>
            <div class="col-sm-3">
                <select name="status" class="form-control">
                    {if $v.status eq 1}
                    <option value="1">是</option>
                    <option value="0">否</option>
                    {else}
                    <option value="0">否</option>
                    <option value="1">是</option>
                    {/if}
                </select>
            </div>
            <div class="col-sm-4 btn-group">
                {if $qx.role_update}
                <button type="submit" class="btn btn-primary"><i class="fa fa-refresh"></i>&nbsp;&nbsp;更新</button>
                {/if}
                {if $qx.role_delete}
                {if $v.role_id > 2}
                <button type="button" onclick="doDelete('确定要删除吗？','{RELROUTE|cat:"/user/role_delete?role_id="|cat:$v.role_id}');"
                    class="btn btn-warning"><i class="fa fa-close"></i>&nbsp;&nbsp;删除</button>
                {/if}
                {/if}
                {if $qx.role_auth_setting}
                <a href="{RELROUTE}/user/role_auth_setting?role_id={$v.role_id}" class="btn btn-success"><i class="fa fa-cogs"></i>&nbsp;&nbsp;权限分配</a>
                {/if}
            </div>
        </div>

    </form>
    {/foreach}
</div>

{include file='admin/public/cfooter.tpl' }