{include file='admin/public/cheader.tpl'}

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">{$modeName}{if ACTION eq "add"}添加{/if}{if ACTION eq "update"}修改{/if}</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="{URI}" method="post">
                <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
                <div class="box-body">
                    {if ACTION eq "add"}
                    {formgroup t='input' desc="用户名" id="username" name="username" value=$data.username|default:''}
                    {/if}
                    {if ACTION eq "update"}
                    <input type="hidden" name="username" id="username" value="{$data.username|default:''}">
                    {formgroup t='desc' desc="用户名" value=$data.username}
                    {/if}
                    {formgroup t='input' desc="用户邮箱" id="email" name="email" value=$data.email|default:''}
                    {formgroup t='input' desc="姓名" id="nickname" name="nickname" value=$data.nickname|default:''}
                    {formgroup t='select' desc="所属角色" id="role_id" name="role_id" lst=$role_arr
                    value=$data.role_id|default:''}
                    {formgroup t='select' desc="所属单位" id="part_id" name="part_id" lst=$part_arr
                    value=$data.part_id|default:''}
                    {formgroup t='select' desc="是否启用" id="status" name="status" lst="tf"
                    value=$data.status|default:1}

                    {formgroup t='input' desc="备注" id="remark" name="remark" value=$data.remark|default:''}

                    {if ACTION eq "update"}

                    {formgroup t='desc' desc="注册时间" value=$data.regdate|date_format:"Y-m-d H:i:s"}
                    {formgroup t='desc' desc="注册IP" value=$data.regip}
                    {formgroup t='desc' desc="最后登录时间" value=$data.last_login_time|date_format:"Y-m-d H:i:s"}
                    {formgroup t='desc' desc="最后登录ip" value=$data.last_login_ip}
                    {formgroup t='desc' desc="登陆次数" value=$data.login_count}
                    {formgroup t='desc' desc="" value="<b>注意：以下密码不修改请留空</b>"}
                    {/if}

                    {formgroup t='input' desc="密码" id="password" name="password" type="password" value=""}
                    {formgroup t='input' desc="密码确认" id="apassword" name="apassword" type="password" value=""}

                    <input type="hidden" name="uid" id="uid" value="{$data.uid|default:0}">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="{RELROUTE}/{MODULE}/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {
        //+---------------
        //| 提交时检查数据是否正确
        //+---------------
        $('#input_form').submit(function (e) {

            if ($.trim($('#username').val()) == '') {
                show_error('用户名不能为空！');
                $('#username').focus();
                return false;
            }

            if ($.trim($('#email').val()) == '') {
                show_error('用户邮箱不能为空！');
                $('#email').focus();
                return false;
            }

            if ($.trim($('#password').val()) != '' || $.trim($('#apassword').val()) != '') {
                if ($.trim($('#password').val()) != $.trim($('#apassword').val())) {
                    show_error('两次密码输入不一致！');
                    $('#password').focus();
                    return false;
                }
            }

        });
    });
</script>

{include file='admin/public/cfooter.tpl'}