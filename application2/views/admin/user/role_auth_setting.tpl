{include file='admin/public/cheader.tpl'}
<form method="post" action="{RELROUTE}/user/role_auth_update">
    <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
    <input type="hidden" name="role_id" value="{$odata.role_id}">

    <h3 class="box-title">[{$odata.role_name}] 角色权限分配</h3>

    <div class="box-body table-responsive no-padding">

        {foreach $_authaction as $k=>$v}
        <div class="col-xs-12" id="app_{$v.app}">
            <h4>
                {formgroup t='authcbox' name='authaction[]' value=$v.app desc=$v.name auth=$role_auth}
            </h4>
            {foreach $v.children as $ck=>$cv}
            <div id="mod_{$v.app}_{$cv.module}" class="col-xs-12" style="padding: 20px;border-bottom: 1px solid #ccc;">
                <h5>
                    {formgroup t='authcbox' name='authaction[]' value=$v.app|cat:'/'|cat:$cv.module
                    desc=$cv.name auth=$role_auth}
                </h5>
                <ul class="role_control_setting">
                    <!-- 三级 -->
                    {foreach $cv.children as $cck=>$ccv}
                    <li>
                        {formgroup t='authcbox' name='authaction[]'
                        value=$v.app|cat:'/'|cat:$cv.module|cat:'/'|cat:$ccv.action desc=$ccv.name
                        auth=$role_auth}
                    </li>
                    {/foreach}
                </ul>
            </div>
            {/foreach}
        </div>
        {/foreach}

        <div class="box-footer">
            &nbsp;<br />&nbsp;<br />
            <input class="btn" type="button" onclick="location='{RELROUTE}/user/role'" value="返回">
            <input class="btn btn-info pull-right" type="submit" value="更 新">
        </div>
    </div>
</form>
<script type="text/javascript">
    $(document).ready(function (e) {
        $('.app').click(function (e) {
            //console.log($(this).val());
            var o = $(this).is(':checked');
            var a = $(this).val().split('/');
            //console.log(a.length);
            if (a.length == 1) {
                //app
                $("#app_" + a[0] + " input").prop("checked", o);
            }
            if (a.length == 2) {
                //app
                $("#mod_" + a[0] + "_" + a[1] + " input").prop("checked", o);
            }
        });
    });
</script>
{include file='admin/public/cfooter.tpl' }