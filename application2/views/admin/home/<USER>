{include file='admin/public/cheader.tpl'}

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">个人信息修改</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="form_self" action="{URI}" method="post">
                <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
                <div class="box-body">
                    {formgroup t='desc' desc="用户名" value=$user_info.username}
                    {formgroup t='desc' desc="所属角色" value=$user_info.role_name}
                    {formgroup t='input' desc="邮箱" id="email" name="email" value=$user_info.email}
                    {formgroup t='desc' desc="注册时间" value=$user_info.regdate|date_format:"Y-m-d H:i:s"}
                    {formgroup t='desc' desc="注册IP" value=$user_info.regip}
                    {formgroup t='desc' desc="最后登录时间" value=$user_info.last_login_time|date_format:"Y-m-d H:i:s"}
                    {formgroup t='desc' desc="最后登录ip" value=$user_info.last_login_ip}
                    {formgroup t='desc' desc="登陆次数" value=$user_info.login_count}
                    {formgroup t='input' desc="姓名" id="nickname" name="nickname" value=$user_info.nickname}
                    {formgroup t='desc' desc="" value="<b>注意：以下密码不修改请留空</b>"}

                    {formgroup t='input' desc="旧密码" id="opassword" name="opassword" type="password" value=""}
                    {formgroup t='input' desc="新密码" id="password" name="password" type="password" value=""}
                    {formgroup t='input' desc="密码确认" id="apassword" name="apassword" type="password" value=""}

                    <input type="hidden" name="action" value="doself">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <button type="submit" class="btn btn-info pull-right">更 新</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>

{include file='admin/public/cfooter.tpl'}