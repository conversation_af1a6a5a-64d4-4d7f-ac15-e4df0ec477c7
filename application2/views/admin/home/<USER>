{include file='admin/public/header.tpl'}
<style>
    .content-iframe-wrapper {
        -webkit-overflow-scrolling: touch;
        width: 100%;
        overflow-y: auto;
        height: calc(100vh - 110px);
    }

    .content-iframe-wrapper .content-iframe {
        width: 100%;
        height: 100%;
    }
</style>

<body class="skin-blue fixed sidebar-mini {if $PushMenu eq 'close'}sidebar-collapse{/if}" style="overflow-y: hidden;">
    <!-- Site wrapper -->
    <div class="wrapper">
        {include file='admin/public/nav.tpl'}
        <div id="pframe" class="content-wrapper" style="background-color: #fff;overflow-y: hidden;">
            <!-- Main content -->
            <iframe src="" id="main_ifram" name="main_ifram" frameborder="no" class="tab_iframe" data-pageid="10008"
                style="width: 1px;; height: 1px;">
            </iframe>
            <script>
                var UA = navigator.userAgent;

                $(document).ready(function () {
                    $('.sidebar-menu').tree();
                    //$('#pframe').css("height", $(window).height() - 100);
                    //
                    //$('#pframe').css("max-height", $(window).height() - 100);
                    if (UA.match(/iPad/) || UA.match(/iPhone/) || UA.match(/iPod/)) {
                        $('#pframe').addClass('content-iframe-wrapper')
                        $('#main_ifram').addClass('content-iframe')
                    } else {
                        $('#pframe').css("min-height", $(window).height() - 2);
                        $('#pframe').css("height", $(window).height() - 2);
                    }

                    $('#main_ifram').width($('#pframe').width());
                    $('#main_ifram').height($('#pframe').height() + 0);

                    $(".main_ifram").click(function () {
                        _href = $(this).attr("href");
                        _href = getIframUrl(_href);
                        if (_href != "") {
                            $("#main_ifram").attr("src", _href);
                        }
                    });
                    var _href = getIframUrl(window.location.hash);
                    if (_href == "") {
                        _href = "{RELROUTE}/home/<USER>";
                    }
                    $("#main_ifram").attr("src", _href);

                    //ifram自适应
                    $(window).resize(function () {
                        //alert($('#pframe').width());
                        //alert($('#main_ifram').width());
                        $('#main_ifram').width($('#pframe').width())
                    });
                })
            </script>
            <!-- /.content -->
        </div>
        {include file='admin/public/copyright.tpl'}
        {include file='admin/public/footer.tpl'}
        <!-- ./wrapper -->
    </div>
</body>

</html>