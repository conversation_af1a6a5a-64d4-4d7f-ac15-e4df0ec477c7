{include file='admin/public/cheader.tpl'}


<div class="box box-info">
    <!-- /.box-header -->
    <!-- form start -->
    <form method="post" action="{RELROUTE}/system/log_action">
        <input type="hidden" name="{$csrf.name}" value="{$csrf.hash}" />
        <input type="hidden" name="action" id="action" value="">

        <div class="box">
            <div class="box-header">
                <h3 class="box-title">操作日志</h3>
                &nbsp;&nbsp;&nbsp;&nbsp;
                {if $qx.founder}
                <input type="submit" class="btn btn-danger btn-sm" value="删 除">
                {/if}
            </div>
            <!-- /.box-header -->
            <div class="box-body table-responsive no-padding">
                <table class="table table-hover">
                    <tbody>
                        <tr>
                            <th><input id="select_all_btn" type="checkbox"></th>
                            <th>日志ID</th>
                            <th>操作者</th>
                            <th>内容</th>
                            <th>操作日期</th>
                            <th>IP地址</th>
                            <th>操作详情</th>
                            <th>URL</th>
                        </tr>
                        {foreach $lst as $k=>$v}
                        <tr>
                            <td>
                                <input art="check_arr" type="checkbox" name="ids[]" value="{$v.log_id}" />
                            </td>
                            <td>{$v.log_id}</td>
                            <td>{$v.username}</td>
                            <td>{$v.title}</td>
                            <td>{$v.log_time|date_format:"Y-m-d H:i:s"}</td>
                            <td>{$v.ip}</td>
                            <td style="max-width:200px;word-wrap:break-word;word-break:break-all;">{$v.log_info|strip_tags}</td>
                            <td>{$v.url}</td>
                        </tr>
                        {/foreach}
                    </tbody>
                </table>
            </div>
            <!-- /.box-body -->
            <div class="box-footer clearfix">
                {formgroup t='page' page=$page}
            </div>
        </div>
        <!-- /.box -->

    </form>
</div>

<script type="text/javascript">
    $(document).ready(function (e) {
        // 全选或全取消
        $('#select_all_btn').click(function (e) {
            var o = $(this).is(':checked');
            if (o) {
                $("input[art='check_arr']").each(function () {
                    $(this).prop("checked", true);
                });
            } else {
                $("input[art='check_arr']").each(function () {
                    $(this).prop("checked", false);
                });
            }
        });
        // 删除
        $('.btn-danger').click(function (e) {

            var t = false;
            $("input[art='check_arr']").each(function () {
                if ($(this).is(':checked')) {
                    t = true;
                }
            });
            if (!t) {
                show_error("请至少选择一项");
                return false;
            }

            var r = confirm('一旦删除将无法恢复，确定要删除吗？');
            if (r == true) {
                $("#action").val("dodelete")
                return true;
            } else {
                return false;
            }
        });
    });
</script>
{include file='admin/public/cfooter.tpl'}