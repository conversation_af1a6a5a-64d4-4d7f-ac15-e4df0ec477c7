<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PY_Controller extends CI_Controller
{
    public $usrpid = 0;

    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set("PRC");
        $csrf = array(
            'name' => $this->security->get_csrf_token_name(),
            'hash' => $this->security->get_csrf_hash(),
        );
        $this->assign('csrf', $csrf);
        $this->assign('env', $this->config->item('env'));
        $this->ACL();
        //$this->error($this->lang->line('msg_no_aut'));
    }

    //成功提示
    public function success($message, $url = '', $time = 3)
    {
        $this->showMsg($message, "success", $url, $time);
    }

    //错误提示
    public function error($message, $url = '', $time = 3)
    {
        $this->showMsg($message, "danger", $url, $time);
    }

    private function showMsg($message, $bg, $url = '', $time = 3)
    {
        $time = 1;
        $link = $js_link = '';
        if (empty($url)) {
            $link = 'javascript:window.history.back();';
            $js_link = 'window.history.back();';
        } else {
            $link = $url;
            $js_link = 'window.location.href = "' . $url . '";';
            // 如果 $url 为空，跳转到上一页，否则跳转到指定的页面
        }
        $jump = $this->lang->line('msg_jump');
        $jump = sprintf($jump, $time, $link);
        //var_dump ();

        $data = array(
            'link' => $link,
            'js_link' => $js_link,
            'jump' => $jump,
            'time' => $time,
            'message' => $message,
            'bg' => $bg,
        );

        $this->assign('data', $data);
        $this->display('admin/msg.tpl');
        die();
    }

    /**
     * 判断是否是创始人
     */
    public function isFounder($isDie = true)
    {
        $founder_arr = explode(',', $this->config->item('founder'));
        if (($_SESSION['user']['role_id'] != 1) || !in_array($_SESSION['user']['uid'], $founder_arr)) {
            if ($isDie) {
                $this->error('只允许创始人删除管理日志！');
                die();
            }
            return false;
        }
        return true;
    }

    //注册权限
    public function assignQX($lst)
    {
        $qx = array();
        foreach ($lst as $k => $v) {
            $qx[$v] = $this->getQX($v);
        }
        $qx['founder'] = $this->isFounder(false);
        $this->assign('qx', $qx);
    }

    /**
     * 获取子权限
     */
    public function getQX($ACTION)
    {
        if (!isset($_SESSION['user']['role_id']) || empty($_SESSION['user']['role_id'])) {
            return false;
        }

        if ($this->isFounder(false)) {
            return true;
        }

        if (!isset($_SESSION['user']['aut']) || empty($_SESSION['user']['aut'])) {
            return false;
        }

        $action = APP . '/' . MODULE . '/' . $ACTION;
        $allow_action = explode(',', $_SESSION['user']['aut']);

        if (in_array($action, $allow_action)) {
            return true;
        }

        return false;
    }

    //权限控制
    private function ACL()
    {
        $APP = $this->uri->segment(1);
        $APP = !empty($APP) ? $APP : '';

        $MODULE = $this->uri->segment(2);
        $MODULE = !empty($MODULE) ? $MODULE : '';

        $ACTION = $this->uri->segment(3);
        $ACTION = !empty($ACTION) ? $ACTION : 'index';

        //echo APP;
        $cur_action = APP . '/' . $MODULE . '/' . $ACTION;
        define('RELROUTE', INDEX . $APP);
        define('URI', INDEX . $APP . '/' . $MODULE . '/' . $ACTION);
        define('MODULE', $MODULE);
        define('ACTION', $ACTION);
        if (!ACL) {
            return;
        }

        $this->load->model('User_model');
        $result = $this->User_model->check_access($cur_action);
        switch ($result) {
            case 0:
                exit('<script language="javascript">top.location.href="' . RELROUTE . '/login"</script>');
                //header('Location: ' . RELROUTE . '/login');
                die();
                break;
            case -1:
                $this->error($this->lang->line('msg_no_supaut'), RELROUTE . '/main');
                die();
                break;
            case -2:
                $this->error($this->lang->line('msg_no_aut'));
                die();
                break;
        }

        //if (@$_SESSION['user']['role_id'] == 3) {
            $this->usrpid = @$_SESSION['user']['part_id'];
        //}
        $this->assign('s_uname', @$_SESSION['user']['username']);
        //var_dump($this->config->item('guest'));
        //echo($cur_action);
    }

    //分页函数
    public function getPageLst($table, $where = array(), $orderby = "", $pagesize = 15, $select = "*", $isprotect = true)
    {
        $page = array(
            'count' => 0, // 总记录数，用于分页
            'all' => 0, // 总页数
            'current' => 1, // 当前页
            'size' => $pagesize, // 每页显示条数
            'uri_string' => INDEX . $this->uri->uri_string(),
        );

        $c = $this->input->get('page');
        $page['current'] = (isset($c) && !empty($c) && (intval($c) > 0)) ? intval($c) : 1;

        $page['count'] = $this->db
            ->from($table)
            ->where($where, null, $isprotect)
            ->count_all_results();

        $res = $this->db
            ->select($select)
            ->from($table)
            ->where($where, null, $isprotect)
            ->order_by($orderby)
            ->limit($page['size'], ($page['current'] - 1) * $page['size'])
            ->get();

        $lst = $res->result_array();
        $page['all'] = ceil($page['count'] / $page['size']);

        $this->assign('page', $page);
        return $lst;
    }

    //
    /**
     * 获取数组转成key->val形式
     * $select = "`key` as k,`value` as v"
     */
    public function getKeyVal($table, $select = "*", $where = array(), $orderby = "", $findIn = array())
    {
        if($findIn){
            if($findIn['value']){
               $res = $this->db
                ->select($select)
                ->from($table)
                ->where($where)
                ->where_in($findIn['key'],$findIn['value'])
                ->order_by($orderby)
                ->get(); 
            }else{
                $res = $this->db
                    ->select($select)
                    ->from($table)
                    ->where('1 != 1')
                    ->order_by($orderby)
                    ->get();
            }
             
        }else{
            $res = $this->db
                ->select($select)
                ->from($table)
                ->where($where)
                ->order_by($orderby)
                ->get();
        }
        

        $lst = $res->result_array();
        $rt = array();

        foreach ($lst as $uk => $uv) {
            $rt[$uv['k']] = $uv['v'];
        }

        return $rt;
    }

    //多级获取
    public function getKeyVals($table, $select = "*", $where = array(), $orderby = "", $cid = 0, $qz = "|_")
    {
        $where2 = $where;
        $where2['cid'] = $cid;
        $res = $this->db
            ->select($select)
            ->from($table)
            ->where($where2)
            ->order_by($orderby)
            ->get();

        $lst = $res->result_array();
        $rt = array();
        if (empty($lst)) {
            return $rt;
        }

        foreach ($lst as $uk => $uv) {
            $p = array();
            $p['k'] = $uv['k'];
            $p['v'] = $qz . " " . $uv['v'];
            $rt[] = $p;
            $clst = $this->getKeyVals($table, $select, $where, $orderby, $uv['k'], "　" . $qz);
            if (!empty($clst)) {
                $rt = array_merge($rt, $clst);
            }

        }

        return $rt;
    }

    //获取一条数据
    public function ckeckbyodata($table, $where = array(), $isajax = false)
    {

        $res = $this->db
            ->select("*")
            ->from($table)
            ->where($where)
            ->get();

        if (!empty($res->result_array())) {
            return $res->result_array()[0];
        }

        if ($isajax) {
            $this->ajaxError('数据不存在！');
        } else {
            $this->error('数据不存在！');
        }
    }

    public function assign($key, $val)
    {
        $this->csmarty->assign($key, $val);
    }

    public function display($html)
    {
        $this->csmarty->display($html);
    }

    public function ajaxError($message = "")
    {
        echo ($message);
        die();
    }

    public function ajaxSuccess()
    {
        echo ('1');
        die();
    }

    public function iniAjax()
    {
        $json = array();
        $json["s"] = 0;
        $json["msg"] = "操作失败";
        return $json;
    }

    public function rtAjax($json)
    {
        header('Content-type:text/json');
        echo json_encode($json);
        die();
    }

    //获取目录+子目录ID
    public function getcids($cid, $tid = 0)
    {
        $cids = array();
        $cids[] = $cid;
        $lst = $this->getKeyVals("column", "`nid` as k,`title` as v", array("type" => "column", "tid" => $tid, "status" => 1), " oid ", $cid);
        //var_dump($lst);
        if (!empty($lst)) {
            foreach ($lst as $k => $v) {
                $cids[] = $v['k'];
            }
        }
    }

}

//require_once 'WX_Controller.php';
