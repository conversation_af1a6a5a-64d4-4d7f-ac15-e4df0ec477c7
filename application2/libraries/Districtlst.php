<?php if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Districtlst
{
    public function getlst()
    {
        $filename = FCPATH . "/conf/Districtlst.txt";
        //var_dump($filename);
        $handle = fopen($filename, "r"); //读取二进制文件时，需要将第二个参数设置成'rb'

        //通过filesize获得文件大小，将整个文件一下子读到一个字符串中
        $content = fread($handle, filesize($filename));
        //var_dump($contents);
        fclose($handle);

        $content = str_replace("\n", "", $content);
        $content = str_replace("\r\n", "", $content);
        $content = preg_replace("/\s/", "", $content);
        $content = str_replace(" ", "", $content);

        $districtlst = explode(",", $content);

        //var_dump($districtlst);
        //die();

        return $districtlst;
    }
}
