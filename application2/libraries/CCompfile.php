<?php if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

require_once APPPATH . 'third_party/HttpClient.class.php';

class CCompfile
{
    public $md5r = array();
    public $appPath;

    public function start($url, $authkey)
    {
        global $md5r, $appPath;
        $md5_url = $url . "?t=" . time();
        $httpclient = HttpClient::getInstance();
        $re = $httpclient->getHttpsResult($md5_url, '', '', true);
        $json = @$this->passport_decrypt($re, $authkey);
        $js = json_decode($json, true);

        $md5r = array();
        $appPath = FCPATH;

        $this->traverse_dir($appPath);
        //$this->directoryiterator($appPath);

        $is_diff = "";
        //目录

        foreach ($md5r as $k => $v) {
            if ($v != @$js[$k]) {
                $a = @date("Y-m-d H:i:s", filemtime($appPath . "$k"));
                $is_diff .= $k . "【" . $a . "】<br/>";
            }
        }
        //var_dump($js);
        $is_del = "";
        if (is_array($js) && !empty($js)) {
            foreach ($js as $k => $v) {
                if (empty($md5r[$k])) {
                    $is_del .= $k . "<br/>";
                }
            }
        }

        $t['is_diff'] = $is_diff;
        $t['is_del'] = $is_del;
        return $t;

        //echo json_encode($md5r);
    }

    public function getMd5text($authkey)
    {
        global $md5r, $appPath;
        $md5r = array();
        $appPath = FCPATH;
        $this->traverse_dir($appPath);
        //$this->directoryiterator($appPath);
        $json = json_encode($md5r);
        $json = $this->passport_encrypt($json, $authkey);
        Header("Content-type:application/octet-stream");
        Header("Accept-Ranges:bytes ");
        header("Content-Disposition:attachment;filename=md5.txt ");
        header("Expires:0");
        header("Cache-Control:must-revalidate,post-check=0,pre-check=0 ");
        header("Pragma:   public ");
        echo ($json);
    }

    private function directoryiterator($path)
    {
        global $md5r, $appPath;
        $iterator = new DirectoryIterator($path);
        foreach ($iterator as $fileinfo) {
            if (!$fileinfo->isDot()) {
                if ($fileinfo->isDir()) {
                    //echo $fileinfo.'=><blockquote>';
                    $this->directoryiterator($path . DIRECTORY_SEPARATOR . $fileinfo);
                    //echo '</blockquote>';
                } else {
                    $url = $fileinfo->getPathname();
                    $vurl = iconv('gb2312', 'utf-8', str_replace($appPath, "", $url));
                    $vmd5 = @md5_file($url);
                    $md5r[$vurl] = $vmd5;
                    //echo $fileinfo->getPathname().'<br/>';
                }
            }
        }
    }

    //效率高点
    private function traverse_dir($dir)
    {
        global $md5r, $appPath;
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir), RecursiveIteratorIterator::CHILD_FIRST);
        foreach ($iterator as $path) {
            //echo $path->__toString(),'<br>';
            if ($path->isDir()) {;
                // rmdir($path->__toString());
                //$dir_list[]=$path->__toString();
            } else {
                // $file_list[]=$path->__toString();
                $url = $path->__toString();
                $vurl = iconv('gb2312', 'utf-8', str_replace($appPath, "", $url));
                $vmd5 = @md5_file($url);
                $md5r[$vurl] = $vmd5;
                // unlink($path->__toString());
            }
        }
        // rmdir($dir);
        //return array($dir_list,$file_list);
    }

    //加密函数
    private function passport_encrypt($txt, $key)
    {
        srand((double) microtime() * 1000000);
        $encrypt_key = md5(rand(0, 32000));
        $ctr = 0;
        $tmp = '';
        for ($i = 0; $i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $encrypt_key[$ctr] . ($txt[$i] ^ $encrypt_key[$ctr++]);
        }
        return base64_encode($this->passport_key($tmp, $key));
    }

    //解密函数
    private function passport_decrypt($txt, $key)
    {
        $txt = $this->passport_key(base64_decode($txt), $key);
        $tmp = '';
        for ($i = 0; $i < strlen($txt); $i++) {
            $md5 = $txt[$i];
            $tmp .= $txt[++$i] ^ $md5;
        }
        return $tmp;
    }

    private function passport_key($txt, $encrypt_key)
    {
        $encrypt_key = md5($encrypt_key);
        $ctr = 0;
        $tmp = '';
        for ($i = 0; $i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $txt[$i] ^ $encrypt_key[$ctr++];
        }
        return $tmp;
    }
}
