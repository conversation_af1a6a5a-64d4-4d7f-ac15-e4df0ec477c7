<?php
/* Smarty version 3.1.33, created on 2024-07-29 16:26:42
  from '/var/www/html/favo/application/views/admin/user/input.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a7524204ef45_56761147',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '4784f08206701d062750e0b4921ebdaac13fd638' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/user/input.tpl',
      1 => 1702388592,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a7524204ef45_56761147 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),1=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/modifier.date_format.php','function'=>'smarty_modifier_date_format',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title"><?php echo $_smarty_tpl->tpl_vars['modeName']->value;
if (ACTION == "add") {?>添加<?php }
if (ACTION == "update") {?>修改<?php }?></h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="<?php echo URI;?>
" method="post">
                <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
                <div class="box-body">
                    <?php if (ACTION == "add") {?>
                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"用户名",'id'=>"username",'name'=>"username",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['username'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    <?php }?>
                    <?php if (ACTION == "update") {?>
                    <input type="hidden" name="username" id="username" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['username'])===null||$tmp==='' ? '' : $tmp);?>
">
                    <?php echo smarty_function_formgroup(array('t'=>'desc','desc'=>"用户名",'value'=>$_smarty_tpl->tpl_vars['data']->value['username']),$_smarty_tpl);?>

                    <?php }?>
                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"用户邮箱",'id'=>"email",'name'=>"email",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['email'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"姓名",'id'=>"nickname",'name'=>"nickname",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['nickname'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"所属角色",'id'=>"role_id",'name'=>"role_id",'lst'=>$_smarty_tpl->tpl_vars['role_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['role_id'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"所属单位",'id'=>"part_id",'name'=>"part_id",'lst'=>$_smarty_tpl->tpl_vars['part_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['part_id'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"是否启用",'id'=>"status",'name'=>"status",'lst'=>"tf",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['status'])===null||$tmp==='' ? 1 : $tmp)),$_smarty_tpl);?>


                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"备注",'id'=>"remark",'name'=>"remark",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['remark'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>


                    <?php if (ACTION == "update") {?>

                    <?php echo smarty_function_formgroup(array('t'=>'desc','desc'=>"注册时间",'value'=>smarty_modifier_date_format($_smarty_tpl->tpl_vars['data']->value['regdate'],"Y-m-d H:i:s")),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'desc','desc'=>"注册IP",'value'=>$_smarty_tpl->tpl_vars['data']->value['regip']),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'desc','desc'=>"最后登录时间",'value'=>smarty_modifier_date_format($_smarty_tpl->tpl_vars['data']->value['last_login_time'],"Y-m-d H:i:s")),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'desc','desc'=>"最后登录ip",'value'=>$_smarty_tpl->tpl_vars['data']->value['last_login_ip']),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'desc','desc'=>"登陆次数",'value'=>$_smarty_tpl->tpl_vars['data']->value['login_count']),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'desc','desc'=>'','value'=>"<b>注意：以下密码不修改请留空</b>"),$_smarty_tpl);?>

                    <?php }?>

                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"密码",'id'=>"password",'name'=>"password",'type'=>"password",'value'=>''),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"密码确认",'id'=>"apassword",'name'=>"apassword",'type'=>"password",'value'=>''),$_smarty_tpl);?>


                    <input type="hidden" name="uid" id="uid" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['uid'])===null||$tmp==='' ? 0 : $tmp);?>
">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<?php echo '<script'; ?>
 type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {
        //+---------------
        //| 提交时检查数据是否正确
        //+---------------
        $('#input_form').submit(function (e) {

            if ($.trim($('#username').val()) == '') {
                show_error('用户名不能为空！');
                $('#username').focus();
                return false;
            }

            if ($.trim($('#email').val()) == '') {
                show_error('用户邮箱不能为空！');
                $('#email').focus();
                return false;
            }

            if ($.trim($('#password').val()) != '' || $.trim($('#apassword').val()) != '') {
                if ($.trim($('#password').val()) != $.trim($('#apassword').val())) {
                    show_error('两次密码输入不一致！');
                    $('#password').focus();
                    return false;
                }
            }

        });
    });
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
