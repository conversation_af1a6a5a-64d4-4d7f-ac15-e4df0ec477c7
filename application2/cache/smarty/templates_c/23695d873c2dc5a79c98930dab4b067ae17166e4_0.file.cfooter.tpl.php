<?php
/* Smarty version 3.1.33, created on 2024-07-25 07:49:25
  from '/var/www/html/favo/application/views/admin/public/cfooter.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a19305e26d02_24068111',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '23695d873c2dc5a79c98930dab4b067ae17166e4' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/public/cfooter.tpl',
      1 => 1702388592,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/footer.tpl' => 1,
  ),
),false)) {
function content_66a19305e26d02_24068111 (Smarty_Internal_Template $_smarty_tpl) {
?></section>
<?php $_smarty_tpl->_subTemplateRender('file:admin/public/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</body>

</html>
<?php }
}
