<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:53:26
  from '/var/www/html/favo/application/views/admin/sorder/input3.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1cc36978d01_72485835',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '39c5106575668d4ff8b13c3408e931ae830b152d' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/input3.tpl',
      1 => 1721879277,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/fileinputjs.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a1cc36978d01_72485835 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
$_smarty_tpl->_subTemplateRender('file:admin/public/fileinputjs.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<!--日期框-->
<link href="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"><?php echo '</script'; ?>
>

<link href="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/css/bootstrap-select.min.css" rel="stylesheet">
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/js/bootstrap-select.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/js/i18n/defaults-zh_CN.min.js"><?php echo '</script'; ?>
>

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">更新邮票</h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="<?php echo URI;?>
" method="post">
                <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
                <div class="box-body">

                    <?php echo smarty_function_formgroup(array('t'=>'uploadfile','desc'=>"邮票",'id'=>"stamp",'name'=>"stamp",'js'=>"succesuploadflie()",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['stamp'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>


                    <div class="form-group">
                        <label class="col-sm-2 control-label">&nbsp;</label>
                        <div class="col-sm-10" id="errmsg" style="color: red;display: none;">
                            注意，您上传的邮票格式并非是jpg格式，请检查无误后再保存邮票
                        </div>
                    </div>

                    <input type="hidden" name="nid" id="nid" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<?php echo '<script'; ?>
 type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {
        $('#input_form').bootstrapValidator();

        $('.datepicker_oid').datetimepicker({
            format: "yyyy-mm-dd hh:ii", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'day',
            autoclose: true //选择日期后自动关闭
        });
    });

    function succesuploadflie() {
        if ($('#stamp').val().indexOf('.jpg') < 5) {
            $("#errmsg").show();
        } else {
            $("#errmsg").hide();
        }
    }
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
