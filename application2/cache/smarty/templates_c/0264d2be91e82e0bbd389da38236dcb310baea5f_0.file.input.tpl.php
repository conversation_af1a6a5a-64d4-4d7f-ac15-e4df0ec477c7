<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:52:55
  from '/var/www/html/favo/application/views/admin/sorder/input.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1cc179c8c23_77785985',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '0264d2be91e82e0bbd389da38236dcb310baea5f' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/input.tpl',
      1 => 1721879277,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/fileinputjs.tpl' => 1,
    'file:admin/sorder/intut_p.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a1cc179c8c23_77785985 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
$_smarty_tpl->_subTemplateRender('file:admin/public/fileinputjs.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<!--日期框-->
<link href="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"><?php echo '</script'; ?>
>

<link href="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/css/bootstrap-select.min.css" rel="stylesheet">
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/js/bootstrap-select.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/js/i18n/defaults-zh_CN.min.js"><?php echo '</script'; ?>
>

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title"><?php echo $_smarty_tpl->tpl_vars['modeName']->value;
if (ACTION == "add") {?>添加<?php }
if (ACTION == "update") {?>修改<?php }?></h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="<?php echo URI;?>
" method="post">
                <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
                <div class="box-body">


                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"客户名称<code>(*)</code>",'id'=>"title",'name'=>"title",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['title'])===null||$tmp==='' ? '' : $tmp),'validator'=>" required data-bv-notempty-message='客户名称不能为空' "),$_smarty_tpl);?>


                    <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker','desc'=>'下单时间','id'=>"oid",'name'=>"oid",'style'=>"datepicker_oid",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['oid'])===null||$tmp==='' ? date("Y-m-d H:i",time()) : $tmp)),$_smarty_tpl);?>


                    <!--
                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"订单内容",'id'=>"description",'name'=>"description",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['description'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"订单备注",'id'=>"cont",'name'=>"cont",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cont'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    -->
                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"店主",'id'=>"s1",'name'=>"s1",'lst'=>$_smarty_tpl->tpl_vars['c3_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['s1'])===null||$tmp==='' ? 0 : $tmp)),$_smarty_tpl);?>


                    <?php if (ACTION == "add") {?>
                    <?php $_smarty_tpl->_subTemplateRender('file:admin/sorder/intut_p.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
                    <?php }?>

                    <input type="hidden" name="nid" id="nid" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<?php echo '<script'; ?>
 type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {
        //$('#input_form').bootstrapValidator();
        /***
        $('.datepicker_oid').datetimepicker({
            format: "yyyy-mm-dd hh:ii", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'day',
            autoclose: true //选择日期后自动关闭
        });
        **/
        $('#input_form').submit(function (e) {

            // if($('#cplx').val() == 106 && $('#cpkg').val() == 118 || $('#cpkg').val() == 120 || $('#cpkg').val() == 121) {
            //     if ($("#num").val() % 2 !== 0) {
            //         show_error('数量必须为偶数');
            //         $('#num').focus();
            //         return false;
            //     }
            // } 


            if ($.trim($('#title').val()) == '') {
                show_error('客户名称不能为空');
                $('#title').focus();
                return false;
            }
            /***<?php if (ACTION == "add") {?>**/
            // if ($.trim($('#pic').val()) == '') {
            //     show_error('产品图片不能为空');
            //     $('#pic').focus();
            //     return false;
            // }
            /***<?php }?>**/

        });
    });
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
