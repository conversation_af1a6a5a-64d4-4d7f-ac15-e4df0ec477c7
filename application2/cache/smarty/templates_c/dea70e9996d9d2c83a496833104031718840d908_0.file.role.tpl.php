<?php
/* Smarty version 3.1.33, created on 2024-07-25 09:41:40
  from '/var/www/html/favo/application/views/admin/user/role.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1ad547a8554_77178714',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'dea70e9996d9d2c83a496833104031718840d908' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/user/role.tpl',
      1 => 1717754821,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a1ad547a8554_77178714 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<div class="box box-info">

    <?php if ($_smarty_tpl->tpl_vars['qx']->value['role_add']) {?>
    <form action="<?php echo RELROUTE;?>
/user/role_add" method="post">
        <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
        <div class="box-header with-border">
            <h3 class="box-title">添加角色</h3>
        </div>
        <div class="box-body">
            <div class="input-group">
                <span class="input-group-addon"><b>角色名称</b></span>
                <input name="role_name" type="text" class="form-control" placeholder="角色名称" required />
                <span class="input-group-btn">
                    <button type="submit" class="btn btn-info btn-flat">添 加</button>
                </span>
            </div>
        </div>
    </form>
    <?php }?>

    <div class="box-header with-border">
        <h3 class="box-title">角色列表</h3>
    </div>

    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['lst']->value, 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
    <form action="<?php echo RELROUTE;?>
/user/role_update" method="post">
        <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
        <input type="hidden" name="role_id" value="<?php echo $_smarty_tpl->tpl_vars['v']->value['role_id'];?>
">
        <div class="row box-body">
            <span class="col-sm-1 text-right"><b>ID:<?php echo $_smarty_tpl->tpl_vars['v']->value['role_id'];?>
-角色名称：</b></span>
            <div class="col-sm-3">
                <input name="role_name" value="<?php echo $_smarty_tpl->tpl_vars['v']->value['role_name'];?>
" type="text" class="form-control " placeholder="角色名称"
                    required  <?php if ($_smarty_tpl->tpl_vars['v']->value['role_id'] <= 2) {?>readonly="readonly"<?php }?>/>
            </div>
            <span class="col-sm-1 text-right"><b>启用:</b></span>
            <div class="col-sm-3">
                <select name="status" class="form-control">
                    <?php if ($_smarty_tpl->tpl_vars['v']->value['status'] == 1) {?>
                    <option value="1">是</option>
                    <option value="0">否</option>
                    <?php } else { ?>
                    <option value="0">否</option>
                    <option value="1">是</option>
                    <?php }?>
                </select>
            </div>
            <div class="col-sm-4 btn-group">
                <?php if ($_smarty_tpl->tpl_vars['qx']->value['role_update']) {?>
                <button type="submit" class="btn btn-primary"><i class="fa fa-refresh"></i>&nbsp;&nbsp;更新</button>
                <?php }?>
                <?php if ($_smarty_tpl->tpl_vars['qx']->value['role_delete']) {?>
                <?php if ($_smarty_tpl->tpl_vars['v']->value['role_id'] > 2) {?>
                <button type="button" onclick="doDelete('确定要删除吗？','<?php echo ((RELROUTE).("/user/role_delete?role_id=")).($_smarty_tpl->tpl_vars['v']->value['role_id']);?>
');"
                    class="btn btn-warning"><i class="fa fa-close"></i>&nbsp;&nbsp;删除</button>
                <?php }?>
                <?php }?>
                <?php if ($_smarty_tpl->tpl_vars['qx']->value['role_auth_setting']) {?>
                <a href="<?php echo RELROUTE;?>
/user/role_auth_setting?role_id=<?php echo $_smarty_tpl->tpl_vars['v']->value['role_id'];?>
" class="btn btn-success"><i class="fa fa-cogs"></i>&nbsp;&nbsp;权限分配</a>
                <?php }?>
            </div>
        </div>

    </form>
    <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
</div>

<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
