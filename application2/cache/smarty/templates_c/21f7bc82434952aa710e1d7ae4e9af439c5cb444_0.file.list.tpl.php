<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:48:16
  from '/var/www/html/favo/application/views/admin/user/list.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1cb005044e2_04375278',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '21f7bc82434952aa710e1d7ae4e9af439c5cb444' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/user/list.tpl',
      1 => 1721879284,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a1cb005044e2_04375278 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),1=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/modifier.date_format.php','function'=>'smarty_modifier_date_format',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/jquery/qrcode.min.js"><?php echo '</script'; ?>
>

<div class="box">

    <form class="form-horizontal" id="search_form" action="<?php echo URI;?>
">
        <div class="box-header">
            <h3 class="box-title"><?php echo $_smarty_tpl->tpl_vars['modeName']->value;?>
列表</h3>
        </div>
        <div class="box-body searchform">
            <!-- /.box-header -->
            <div class="box-body">
                <div class="row">
                    <div class="col-sm-4">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"单位",'id'=>"spid",'name'=>"spid",'lst'=>$_smarty_tpl->tpl_vars['part_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['spid'])===null||$tmp==='' ? '-99' : $tmp)),$_smarty_tpl);?>

                    </div>
                    <div class="col-sm-4">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"角色",'id'=>"srid",'name'=>"srid",'lst'=>$_smarty_tpl->tpl_vars['role_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['srid'])===null||$tmp==='' ? '-99' : $tmp)),$_smarty_tpl);?>

                    </div>
                    <div class="col-sm-4">
                        <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"姓名",'id'=>"skeyw",'name'=>"skeyw",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['skeyw'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                </div>

                <div class="btn-group">
                    <?php echo smarty_function_formgroup(array('t'=>'addbtn','url'=>(RELROUTE).("/user/add"),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['add'],'desc'=>($_smarty_tpl->tpl_vars['modeName']->value).("添加")),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'searchbtn'),$_smarty_tpl);?>

                </div>
            </div>
            <!-- /.box-body -->
        </div>
    </form>

</div>
<div class="box">
    <div class="box-header">

    </div>
    <!-- /.box-header -->
    <div class="box-body table-responsive no-padding">
        <table class="table table-hover">
            <tbody>
                <tr>
                    <th>uid</th>
                    <th>角色</th>
                    <th>单位</th>
                    <th>用户名</th>
                    <th>姓名</th>
                    <th>邮箱</th>
                    <th>启用</th>
                    <th>最后登录时间/IP</th>
                    <th>操作</th>
                    <?php if ($_smarty_tpl->tpl_vars['qx']->value['founder']) {?>
                    <th>Authenticator</th>
                    <?php }?>
                </tr>
                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['lst']->value, 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
                <tr>
                    <td><?php echo $_smarty_tpl->tpl_vars['v']->value['uid'];?>
</td>
                    <td><?php echo (($tmp = @$_smarty_tpl->tpl_vars['role_arr']->value[$_smarty_tpl->tpl_vars['v']->value['role_id']])===null||$tmp==='' ? '' : $tmp);?>
</td>
                    <td><?php echo (($tmp = @$_smarty_tpl->tpl_vars['part_arr']->value[$_smarty_tpl->tpl_vars['v']->value['part_id']])===null||$tmp==='' ? '' : $tmp);?>
</td>
                    <td><?php echo $_smarty_tpl->tpl_vars['v']->value['username'];?>
</td>
                    <td><?php echo $_smarty_tpl->tpl_vars['v']->value['nickname'];?>
</td>
                    <td><?php echo $_smarty_tpl->tpl_vars['v']->value['email'];?>
</td>
                    <td><?php echo smarty_function_formgroup(array('t'=>'tf','id'=>$_smarty_tpl->tpl_vars['v']->value['status']),$_smarty_tpl);?>
</td>
                    <td><?php echo smarty_modifier_date_format($_smarty_tpl->tpl_vars['v']->value['last_login_time'],"Y-m-d H:i:s");?>
<br /><?php echo $_smarty_tpl->tpl_vars['v']->value['last_login_ip'];?>
</td>
                    <td>
                        <div class="btn-group">
                            <?php if ($_smarty_tpl->tpl_vars['v']->value['role_id'] == 5) {?>
                            <?php echo smarty_function_formgroup(array('t'=>'editbtn','url'=>((RELROUTE).("/user/teacher_update?uid=")).($_smarty_tpl->tpl_vars['v']->value['uid']),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['teacher_update']),$_smarty_tpl);?>

                            <?php } else { ?>
                            <?php echo smarty_function_formgroup(array('t'=>'editbtn','url'=>((RELROUTE).("/user/update?uid=")).($_smarty_tpl->tpl_vars['v']->value['uid']),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['update']),$_smarty_tpl);?>

                            <?php }?>
                            <?php echo smarty_function_formgroup(array('t'=>'delbtn','url'=>((RELROUTE).("/user/delete?uid=")).($_smarty_tpl->tpl_vars['v']->value['uid']),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['delete']),$_smarty_tpl);?>

                            <?php if ($_smarty_tpl->tpl_vars['v']->value['role_id'] == 4) {?>
                               <a href="<?php echo RELROUTE;?>
/user/product?uid=<?php echo $_smarty_tpl->tpl_vars['v']->value['uid'];?>
" class="btn btn-success"><i class="fa fa-cogs"></i>&nbsp;&nbsp;分配产品</a>
                            <?php }?>
                        </div>
                    </td>
                    <?php if ($_smarty_tpl->tpl_vars['qx']->value['founder']) {?>
                    <td>
                        <input type="button" onclick="show_qrcode('<?php echo $_smarty_tpl->tpl_vars['v']->value['username'];?>
','<?php echo $_smarty_tpl->tpl_vars['v']->value['Authenticator'];?>
');"
                            class="btn btn-default btn-sm" value="查看">
                    </td>
                    <?php }?>
                </tr>
                <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

            </tbody>
        </table>
    </div>
    <!-- /.box-body -->
    <div class="box-footer clearfix">
        <?php echo smarty_function_formgroup(array('t'=>'page','page'=>$_smarty_tpl->tpl_vars['page']->value),$_smarty_tpl);?>

    </div>
</div>
<!-- /.box -->
<?php echo '<script'; ?>
 type="text/javascript">
    function show_qrcode(_uname, _code) {
        $('#my_qrcode').modal('show');
        $('#qrcode').html("");
        $('#qrcode').qrcode('otpauth://totp/' + _uname + '?secret=' + _code + '&issuer=<?php echo $_smarty_tpl->tpl_vars['env']->value['site_code'];?>
');
    }
<?php echo '</script'; ?>
>
<!-- /.modal -->
<div class="modal" id="my_qrcode">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header my-rzzc">
                <h5>Google身份验证器二维码</h5>
            </div>
            <div class="modal-body text-danger">
                <div id="qrcode"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
