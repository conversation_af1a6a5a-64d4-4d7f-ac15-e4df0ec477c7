<?php
/* Smarty version 3.1.33, created on 2024-07-25 07:47:07
  from '/var/www/html/favo/application/views/admin/login.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1927b9675d2_25145007',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'bdfa0986ccfb4cb9ee5d4204bc6650d5682a2c2e' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/login.tpl',
      1 => 1718273302,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/header.tpl' => 1,
    'file:admin/public/footer.tpl' => 1,
  ),
),false)) {
function content_66a1927b9675d2_25145007 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/public/header.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<body class="hold-transition login-page">
  <div class="login-box">
    <div class="login-logo">
      <svg t="1716635130477" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
        p-id="10311" width="150" height="150">
        <path
          d="M791.979 733.459c23.05 0 41.898-18.848 41.898-41.896V209.824c0-23.034-18.847-41.882-41.898-41.882H142.68c-23.034 0-41.882 18.848-41.882 41.882v481.738c0 23.049 18.85 41.896 41.882 41.896h649.299z"
          fill="#FFD3A8" p-id="10312"></path>
        <path
          d="M880.051 285.365H230.748c-23.034 0-41.883 18.849-41.883 41.884v481.736c0 23.046 18.849 41.897 41.883 41.897h649.301c23.046 0 41.896-18.851 41.896-41.897V327.249c0-23.034-18.848-41.884-41.894-41.884z m-409.87 83.939c29.327 0 52.36 23.048 52.36 52.377 0 29.312-23.033 54.438-52.36 54.438-29.326 0-52.362-23.033-52.362-52.347 2.093-31.418 25.144-54.468 52.362-54.468z m-18.849 397.967c-16.756 0-31.42-14.66-31.42-31.416 0-16.758 14.664-31.42 31.42-31.42 16.76 0 31.423 14.662 31.423 31.42-0.002 16.755-14.665 31.416-31.423 31.416z m104.725 0c-16.756 0-31.42-14.66-31.42-31.416 0-16.758 14.664-31.42 31.42-31.42 16.755 0 31.42 14.662 31.42 31.42 0 16.755-14.665 31.416-31.42 31.416z m104.723 0c-16.757 0-31.419-14.66-31.419-31.416 0-16.758 14.662-31.42 31.419-31.42 16.756 0 31.42 14.662 31.42 31.42 0 16.755-14.664 31.416-31.42 31.416z m154.993-146.618H315.19c-6.278 0-12.571-4.19-16.756-8.39-6.278-8.37-4.187-23.034 4.186-29.313l71.21-52.357c14.664-10.48 31.421-10.48 46.083-2.095l77.503 48.163 167.566-104.723c16.74-10.467 37.698-8.373 50.254 4.182l115.205 108.927c4.184 4.182 6.293 10.478 6.293 14.66-0.002 12.557-8.391 20.946-20.961 20.946z"
          fill="#FF7E00" p-id="10313"></path>
      </svg>
      <p style="color: #000;font-weight: 600;">欢迎回家</p>
    </div>
    <!-- /.login-logo -->
    <div class="login-box-body">
      <p class="login-box-msg">请输入账号密码登录</p>

      <form action="<?php echo RELROUTE;?>
/login/clogin" id="login_form" method="post" name="login">
        <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />

        <div class="form-group has-feedback">
          <input type="text" class="form-control" placeholder="账号名" name="username" id="username" type="text" autofocus>
          <span class="glyphicon glyphicon-user form-control-feedback"></span>
        </div>
        <div class="form-group has-feedback">
          <input type="password" class="form-control" placeholder="密 码" name="password" id="password" value="">
          <span class="glyphicon glyphicon-lock form-control-feedback"></span>
        </div>

        <?php if (ACTION == 'suplogin') {?>
        <div class="form-group has-feedback">
          <input type="text" class="form-control" placeholder="随机码" name="idcode" id="idcode" type="text">
          <span class="glyphicon glyphicon-asterisk form-control-feedback"></span>
        </div>


        <div class="form-group has-feedback">
          <input type="text" class="form-control" placeholder="验证码" name="captcha" id="captcha" type="text">
          <span class="glyphicon glyphicon-asterisk form-control-feedback"></span>
        </div>
        <?php }?>
        <div class="form-group has-feedback" id="get_img">

        </div>

        <div class="row">
          <div class="col-xs-8">

          </div>
          <!-- /.col -->
          <div class="col-xs-4">
            <button type="submit" class="btn btn-primary btn-block btn-flat">登 录</button>
          </div>
          <!-- /.col -->
        </div>
      </form>
    </div>
    <!-- /.login-box-body -->
  </div>
  <!-- /.login-box -->

  <?php echo '<script'; ?>
>
    $(document).ready(function (e) {
      //$("#get_img").load("<?php echo RELROUTE;?>
/login/captcha");
      //$("#get_img").click(function () {
      //  $("#get_img").load("<?php echo RELROUTE;?>
/login/captcha");
      //})
      //+---------------
      //| 提交时检查数据是否正确
      //+---------------
      $('#login_form').submit(function (e) {
        if ($.trim($('#username').val()) == '') {
          show_error("请输入账号名");
          return false;
        }
        if ($.trim($('#password').val()) == '') {
          show_error("请输入密码");
          return false;
        }
        /**
        if ($.trim($('#captcha').val()) == '') {
          show_error("请输入验证码");
          return false;
        }
        */
      });
    });
  <?php echo '</script'; ?>
>

  <?php $_smarty_tpl->_subTemplateRender('file:admin/public/footer.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</body>

</html><?php }
}
