<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:51:36
  from '/var/www/html/favo/application/views/admin/sorder/intut_p.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1cbc8643df2_19106465',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '927204483015c2926a749bdebf7cc49e5651466b' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/intut_p.tpl',
      1 => 1721879276,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_66a1cbc8643df2_19106465 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
?>
<hr />

    <div class="form-group">
        <label class="col-sm-2 control-label"></label>
        <label class="col-sm-10">订单产品</label>
        
    </div>
    <?php if ($_smarty_tpl->tpl_vars['data']->value == array()) {?>
    <div id="div">
        <input type="text" value="1" id="sort" hidden="true">
        <div id="div1">
            <!-- <?php echo smarty_function_formgroup(array('t'=>'uploadpic','desc'=>"产品生产文件<code>(*)</code>",'id'=>"pic1",'name'=>"pic1",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['pic'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

            <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"产品类型",'id'=>"cplx1",'name'=>"cplx1",'lst'=>$_smarty_tpl->tpl_vars['c1_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cplx'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

            <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"产品规格",'id'=>"cpkg1",'name'=>"cpkg1",'lst'=>array(),'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cpkg'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

            
            <div class="form-group">
                <label class="col-sm-2 control-label">产品数量</label>
                <div class="col-sm-10">
                    <input class="form-control" type="number" name="num1" id="num1" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['num'])===null||$tmp==='' ? '1' : $tmp);?>
" required=""
                        data-bv-notempty-message="产品数量不能为空">
                </div>
            </div> -->
            <!-- <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"产品内容",'id'=>"ss11",'name'=>"ss11",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['s1'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>
 -->
             <div id="picHtml">
                <div>
                    <div class='form-group'>
                        <label class='col-sm-2 control-label'>产品生产文件</label>
                        <div class='col-sm-5'>
                            <input type='hidden' name='pic0' id='pic0' value=''  />
                            <input id='pic0-up' name='pic0-up' type='file' accept='image/*' />
                        </div>
                    </div>
                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"产品类型",'id'=>"cplx0",'name'=>"cplx0",'lst'=>$_smarty_tpl->tpl_vars['c1_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cplx'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"产品规格",'id'=>"cpkg0",'name'=>"cpkg0",'lst'=>array(),'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cpkg'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">产品数量</label>
                    <div class="col-sm-10">
                        <input class="form-control" type="number" onblur="headerBlur(0)" name="num0" id="num0" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['num'])===null||$tmp==='' ? '1' : $tmp);?>
" required=""
                            data-bv-notempty-message="产品数量不能为空">
                    </div>
                </div>
                <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"产品内容",'id'=>"ss10",'name'=>"ss10",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['s1'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

             </div>
        </div>
    </div>
    <button type="button"
            onclick="addasd1()"
            class="btn btn-success btn-sm " data-toggle="modal" value="">
            添加产品
        </button>
    <?php } else { ?>
        <div>
            <?php echo smarty_function_formgroup(array('t'=>'uploadpic','desc'=>"产品生产文件<code>(*)</code>",'id'=>"pic0",'name'=>"pic0",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['pic'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

            <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"产品类型",'id'=>"cplx0",'name'=>"cplx0",'lst'=>$_smarty_tpl->tpl_vars['c1_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cplx'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

            <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"产品规格",'id'=>"cpkg0",'name'=>"cpkg0",'lst'=>array(),'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cpkg'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

            <div class="form-group">
                <label class="col-sm-2 control-label">产品数量</label>
                <div class="col-sm-10">
                    <input class="form-control" type="number" name="num0" id="num0" onblur="headerBlur(0)" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['num'])===null||$tmp==='' ? '1' : $tmp);?>
" required=""
                        data-bv-notempty-message="产品数量不能为空">
                </div>
            </div>
            <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"产品内容",'id'=>"ss10",'name'=>"ss10",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['s1'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

        </div>
    <?php }?>


<?php echo '<script'; ?>
>
    var c2_sjson = '<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_json']->value)===null||$tmp==='' ? array() : $tmp);?>
';
    var c2json = $.parseJSON(c2_sjson);
    var cpgk = '<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['cpkg'])===null||$tmp==='' ? 0 : $tmp);?>
';
    var c1_arr = '<?php echo $_smarty_tpl->tpl_vars['c1_arr']->value;?>
';
    var c1_json = '<?php echo $_smarty_tpl->tpl_vars['c1_json']->value;?>
';
    
    $(document).ready(function (e) {
        setCPGG($('#cplx0').val());
        $('#cplx0').on('change', function () {
            setCPGG($('#cplx0').val());
        });
        
        $('#cpkg0').on('change', function () {
            var cplx = parseInt($('#cplx0').val());
            var cpkg = parseInt($('#cpkg0').val());
            
            if(cplx == 106 && cpkg == 118 || cpkg == 120 || cpkg == 121) {
                $('#num0').val(2);
            }
        });        
    });
    // 初始化默认上传图片
    test()


    function setCPGG(id) {
        $("#cpkg0").empty();
        $.each(c2json[id], function (key, value) {
            console.log(key + ": " + value);
            var sleected = '';
            if (key == cpgk) {
                sleected = ' selected="selected" '
            }
            $("#cpkg0").append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
        });
    }
    
    function addasd() {
        $('#sort').val(parseInt($('#sort').val()));
        var sort = $('#sort').val() + 1; 
        // 先修改元素的id和name
        $('#pic1').attr('id','pic23');
        
        var elementToCopy = document.getElementById('div1');
        console.log(elementToCopy);
        // $('#pic1').attr('name','pic23');
        // elementToCopy.children[0].children[1].children[0].id = 'pic'+ sort;
        // elementToCopy.children[0].children[1].children[0].name = 'pic' + sort;
        // console.log($('#pic23').attr('id'));
        elementToCopy.children[1].children[1].children[0].id = 'cplx' + sort;
        elementToCopy.children[1].children[1].children[0].name = 'cplx' + sort;

        elementToCopy.children[2].children[1].children[0].id = 'cpkg' + sort;
        elementToCopy.children[2].children[1].children[0].name = 'cpkg' + sort;

        elementToCopy.children[3].children[1].children[0].id = 'num' + sort;
        elementToCopy.children[3].children[1].children[0].name = 'num' + sort;

        // elementToCopy.children[4].children[1].children[0].id = 'ss1' + sort;
        // elementToCopy.children[4].children[1].children[0].name = 'ss1' + sort;


        // 再复制元素
        var copiedElement = elementToCopy.cloneNode(true);
        copiedElement.id = 'div' + sort;
        var body = document.getElementById('div');
        body.appendChild(copiedElement);

        // 再把元素原来的id和name改回来
        elementToCopy.children[0].children[1].children[0].id = 'pic1';
        elementToCopy.children[0].children[1].children[0].name = 'pic1';

        elementToCopy.children[1].children[1].children[0].id = 'cplx1';
        elementToCopy.children[1].children[1].children[0].name = 'cplx1';

        elementToCopy.children[2].children[1].children[0].id = 'cpkg1';
        elementToCopy.children[2].children[1].children[0].name = 'cpkg1';

        elementToCopy.children[3].children[1].children[0].id = 'num1';
        elementToCopy.children[3].children[1].children[0].name = 'num1';

        elementToCopy.children[4].children[1].children[0].id = 'ss11';
        elementToCopy.children[4].children[1].children[0].name = 'ss11';

        var body = document.getElementById('div');
        body.appendChild(copiedElement);

        $("#cpkg"+sort).empty();
        $.each(c2json[$('#cplx'+sort).val()], function (key, value) {
            console.log(key + ": " + value);
            var sleected = '';
            if (key == cpgk) {
                sleected = ' selected="selected" '
            }
            $("#cpkg"+sort).append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
        });
        $('#cplx'+sort).on('change', function () {
            $("#cpkg"+sort).empty();
            $.each(c2json[$('#cplx'+sort).val()], function (key, value) {
                console.log(key + ": " + value);
                var sleected = '';
                if (key == cpgk) {
                    sleected = ' selected="selected" '
                }
                $("#cpkg"+sort).append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
            });
        });

        $('#cpkg'+sort).on('change', function () {
            console.log(123);
            var cplx = parseInt($('#cplx'+sort).val());
            var cpkg = parseInt($('#cpkg'+sort).val());
            
            if(cplx == 106 && cpkg == 118 || cpkg == 120 || cpkg == 121) {
                $('#num'+sort).val(2);
            }
        }); 
    }

    // 新增插入元素
    var i = 0
    let addFormArr = []
    function addasd1(){
        i++
        var html = `
            <div>
                    <div class='form-group'>
                        <label class='col-sm-2 control-label'>产品生产文件</label>
                        <div class='col-sm-5'>
                            <input type='hidden' name='pic`+i+`' id='pic`+i+`' value=''  />
                            <input id='pic`+i+`-up' name='pic`+i+`-up' type='file' accept='image/*' />
                        </div>
                    </div>
                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"产品类型",'id'=>"cplx`+i+`",'name'=>"cplx`+i+`",'index'=>"`+i+`",'lst'=>$_smarty_tpl->tpl_vars['c1_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cplx'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"产品规格",'id'=>"cpkg`+i+`",'name'=>"cpkg`+i+`",'lst'=>array(),'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['cpkg'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">产品数量</label>
                    <div class="col-sm-10">
                        <input class="form-control" type="number" name="num`+i+`" id="num`+i+`" onblur="headerBlur(`+i+`)" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['num'])===null||$tmp==='' ? '1' : $tmp);?>
" required=""
                            data-bv-notempty-message="产品数量不能为空">
                    </div>
                </div>
                <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"产品内容",'id'=>"ss1`+i+`",'name'=>"ss1`+i+`",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['s1'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

             </div>
        `
        // 插入
        addFormArr.push({})
        $('#picHtml').append(html)
        test1()
        setCPGG1($('#cplx'+i).val(),i)
        changeCpkg(i);
    }
    function changeCpkg(i){
        $('#cpkg'+i).on('change', function () {
            var cplx = parseInt($('#cplx'+i).val());
            var cpkg = parseInt($('#cpkg'+i).val());
            
            if(cplx == 106 && cpkg == 118 || cpkg == 120 || cpkg == 121) {
                $('#num'+i).val(2);
            }
        }); 
    }
    // 点击初始化上传图片
    function test1(){
        $('#pic'+i+'-up').fileinput({
            language: 'zh',
            uploadUrl:'/index.php//upload/home/<USER>',
            uploadAsync: true,
            allowedFileExtensions : ['png', 'jpg','jpeg','tif'],
            allowedPreviewTypes : ['image'],
            overwriteInitial: true,
            showUpload:false,
            defaultPreviewContent: '<img src=\"/public/images/nopic.png\" style=\"width: 100px;height: 100px;\" >',
            }).on('filebatchselected', function(event, files) {
                $('#pic'+i+'-up').fileinput('upload');
            }).on('filecleared', function(event, id) {
               $('#pic'+i).val('');
            }).on('fileuploaded', function (event, data) {
                var d=data.response;
            if(d.s==1){
            $('#pic'+i).val(d.www_path);
                toastr.success('上传成功');
            }else{
                toastr.error(d.msg);
            }
            });
    }
    // 初始化默认上传图片
    function test(id){
        $('#pic0-up').fileinput({
            language: 'zh',
            uploadUrl:'/index.php//upload/home/<USER>',
            uploadAsync: true,
            allowedFileExtensions : ['png', 'jpg','jpeg','tif'],
            allowedPreviewTypes : ['image'],
            overwriteInitial: true,
            showUpload:false,
            defaultPreviewContent: '<img src=\"/public/images/nopic.png\" style=\"width: 100px;height: 100px;\" >',
            }).on('filebatchselected', function(event, files) {
                $('#pic0-up').fileinput('upload');
            }).on('filecleared', function(event, id) {
               $('#pic0').val('');
            }).on('fileuploaded', function (event, data) {
                //console.log(data.response);
                var d=data.response;
            if(d.s==1){
            $('#pic0').val(d.www_path);
                toastr.success('上传成功');
            }else{
                toastr.error(d.msg);
            }
            });
    }
    function setCPGG1(id,match) {
        $("#cpkg"+match).empty();
        $.each(c2json[id], function (key, value) {
            var sleected = '';
            if (key == cpgk) {
                sleected = ' selected="selected" '
            }
            $("#cpkg"+match).append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
        });
    }
    function headerChange(id){
        const regex = /\d+/g;
        let match = regex.exec($(id).attr('id'))[0]
        // 判断是否产品类型，如果是产品类型则请求使用一下方法
        if($(id).attr('id').indexOf('cplx') === 0){
            setCPGG1($(id).val(),match)
        }
    }

    function headerBlur(value){
        
        var cplx = parseInt($('#cplx'+value).val());
        var cpkg = parseInt($('#cpkg'+value).val());
        
        if(cplx == 106 && cpkg == 118 || cpkg == 120 || cpkg == 121) {
            if ($("#num"+value).val() % 2 !== 0) {
                show_error('数量必须为偶数');
                return false;
            }
        }
    }

<?php echo '</script'; ?>
><?php }
}
