<?php
/* Smarty version 3.1.33, created on 2024-07-31 12:16:06
  from '/var/www/html/favo/application/views/admin/system/log_action.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a9ba86413117_05958444',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'a85ceffd94c380d7d746041768449c0d9c1c0916' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/system/log_action.tpl',
      1 => 1702388592,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a9ba86413117_05958444 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/modifier.date_format.php','function'=>'smarty_modifier_date_format',),1=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


<div class="box box-info">
    <!-- /.box-header -->
    <!-- form start -->
    <form method="post" action="<?php echo RELROUTE;?>
/system/log_action">
        <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
        <input type="hidden" name="action" id="action" value="">

        <div class="box">
            <div class="box-header">
                <h3 class="box-title">操作日志</h3>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <?php if ($_smarty_tpl->tpl_vars['qx']->value['founder']) {?>
                <input type="submit" class="btn btn-danger btn-sm" value="删 除">
                <?php }?>
            </div>
            <!-- /.box-header -->
            <div class="box-body table-responsive no-padding">
                <table class="table table-hover">
                    <tbody>
                        <tr>
                            <th><input id="select_all_btn" type="checkbox"></th>
                            <th>日志ID</th>
                            <th>操作者</th>
                            <th>内容</th>
                            <th>操作日期</th>
                            <th>IP地址</th>
                            <th>操作详情</th>
                            <th>URL</th>
                        </tr>
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['lst']->value, 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
                        <tr>
                            <td>
                                <input art="check_arr" type="checkbox" name="ids[]" value="<?php echo $_smarty_tpl->tpl_vars['v']->value['log_id'];?>
" />
                            </td>
                            <td><?php echo $_smarty_tpl->tpl_vars['v']->value['log_id'];?>
</td>
                            <td><?php echo $_smarty_tpl->tpl_vars['v']->value['username'];?>
</td>
                            <td><?php echo $_smarty_tpl->tpl_vars['v']->value['title'];?>
</td>
                            <td><?php echo smarty_modifier_date_format($_smarty_tpl->tpl_vars['v']->value['log_time'],"Y-m-d H:i:s");?>
</td>
                            <td><?php echo $_smarty_tpl->tpl_vars['v']->value['ip'];?>
</td>
                            <td style="max-width:200px;word-wrap:break-word;word-break:break-all;"><?php echo preg_replace('!<[^>]*?>!', ' ', $_smarty_tpl->tpl_vars['v']->value['log_info']);?>
</td>
                            <td><?php echo $_smarty_tpl->tpl_vars['v']->value['url'];?>
</td>
                        </tr>
                        <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </tbody>
                </table>
            </div>
            <!-- /.box-body -->
            <div class="box-footer clearfix">
                <?php echo smarty_function_formgroup(array('t'=>'page','page'=>$_smarty_tpl->tpl_vars['page']->value),$_smarty_tpl);?>

            </div>
        </div>
        <!-- /.box -->

    </form>
</div>

<?php echo '<script'; ?>
 type="text/javascript">
    $(document).ready(function (e) {
        // 全选或全取消
        $('#select_all_btn').click(function (e) {
            var o = $(this).is(':checked');
            if (o) {
                $("input[art='check_arr']").each(function () {
                    $(this).prop("checked", true);
                });
            } else {
                $("input[art='check_arr']").each(function () {
                    $(this).prop("checked", false);
                });
            }
        });
        // 删除
        $('.btn-danger').click(function (e) {

            var t = false;
            $("input[art='check_arr']").each(function () {
                if ($(this).is(':checked')) {
                    t = true;
                }
            });
            if (!t) {
                show_error("请至少选择一项");
                return false;
            }

            var r = confirm('一旦删除将无法恢复，确定要删除吗？');
            if (r == true) {
                $("#action").val("dodelete")
                return true;
            } else {
                return false;
            }
        });
    });
<?php echo '</script'; ?>
>
<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
