<?php
/* Smarty version 3.1.33, created on 2024-08-29 17:48:32
  from '/var/www/html/favo/application/views/admin/sorder/mprintstamp.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66d043f0de2de0_37026592',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '4b2756ba4cab46ba2b00780927d373c85f2e65e3' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/mprintstamp.tpl',
      1 => 1721879277,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_66d043f0de2de0_37026592 (Smarty_Internal_Template $_smarty_tpl) {
?><div class="box-header">
    <button onclick="doprintB()" class="btn btn-success pull-left">打印</button>
    <!-- <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button> -->
    <button onclick="printSuccess()" class="btn btn-danger pull-right">打印成功请点击批量修改邮票状态</button>
</div>
<style>
    #printdivB {
        width: 10.5cm;
        
    }
    #printCssB {
        width:10.5cm;
        height: 14.8cm;
        /* border: 0.5cm solid red; */
        box-sizing: border-box;
        
        /* text-align: center; */
    }

    #printCssImgB {
        width:100%;
        height: 100%;
    }
    
</style>
<div class="a6printw" id="printdivB">
    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, (($tmp = @$_smarty_tpl->tpl_vars['data']->value)===null||$tmp==='' ? array() : $tmp), 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
        <?php if ($_smarty_tpl->tpl_vars['v']->value['stamp'] != '') {?> 
        <div id="printCssB">
            <!-- <?php echo $_smarty_tpl->tpl_vars['v']->value['stamp'];?>
 -->

            <!-- <?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['title'])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_arr']->value[$_smarty_tpl->tpl_vars['v']->value['cpkg']])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo $_smarty_tpl->tpl_vars['v']->value['num'];?>
-<?php echo $_smarty_tpl->tpl_vars['v']->value['sort'];
if ($_smarty_tpl->tpl_vars['v']->value['num'] != 1) {?> + <span style="color:red;font-weight: bold;">combine</span><?php }?> <br /> -->
            <img src="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['stamp'])===null||$tmp==='' ? '' : $tmp);?>
" id="printCssImgB" class="pics" />
        </div>
        <?php }?>
    <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
</div>
<div class="box-footer">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button>
</div>
<?php echo '<script'; ?>
>
    $(document).ready(function (e) {
        //tif2img("#printdiv");
    });

    function printSuccess(){
        var userResponse1 = confirm("你确定要批量修改邮票状态吗？请先确认打印无误");
        if (userResponse1) {
            console.log("用户点击了确定。");
            // 执行操作
            $.ajax({
                url: '/index.php/htgl/sorder/dprintStamp',
                type: 'GET',
                data:{
                    'lstNidArr': <?php echo $_smarty_tpl->tpl_vars['lstNidArr']->value;?>

                },
                contentType: "application/json",
                async: true,
                dataType: 'json',
                success: function(data){
                    if(data == 1){
                        alert('修改批量邮票状态成功');
                        location.reload();
                    }else {
                        alert('修改批量邮票状态失败');
                    }
                },
            });
        } else {
            console.log("用户点击了取消。");
            // 执行其他操作或者什么都不做
        }
        
        // alert(123);
    }
<?php echo '</script'; ?>
><?php }
}
