<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:45:50
  from '/var/www/html/favo/application/views/admin/sorder/mprintjiu.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1ca6e2dae80_02132312',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '6f9b630b7a831e4d70fed21d53f87731f0b9351e' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/mprintjiu.tpl',
      1 => 1721878967,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_66a1ca6e2dae80_02132312 (Smarty_Internal_Template $_smarty_tpl) {
?><div class="box-header">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <!-- <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button> -->
    <button onclick="printSuccess()" class="btn btn-danger pull-right">打印成功请点击批量修改产品状态</button>
    
</div>
<style>
    #printdiv {
        width: 12.4cm;
        
    }
    #printCss {
        width:12.4cm;
        height: 19cm;
        text-align:center;
        /* border: 0.5cm solid red; */
        position: relative;
        box-sizing: border-box;
    }

    #printCssImg {
        width: 12.4cm;
        height: 19cm;
        /* height: auto; */
    }

    #printSpan {
        color:red !important;
        font-weight: bold !important;
    }
    
    .title {
        width: 11.6cm;
        height: 1cm;
        text-align: center;
        /* line-height: 1cm; */
        position: absolute;
        top: 4.5mm;
        left: 4mm;
        font-size: 3mm;
    }
    
</style>
<div class="a6printw" id="printdiv">

    <?php if (isset($_smarty_tpl->tpl_vars['type']->value) && $_smarty_tpl->tpl_vars['type']->value == 'stamp') {?> 
        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, (($tmp = @$_smarty_tpl->tpl_vars['data']->value)===null||$tmp==='' ? array() : $tmp), 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
        <?php if ($_smarty_tpl->tpl_vars['v']->value['stamp'] != '') {?> 
        <div class="a6print pagep" style="height:<?php ob_start();
echo (($tmp = @$_smarty_tpl->tpl_vars['aconfig']->value['conf_fsheete'])===null||$tmp==='' ? 900 : $tmp);
$_prefixVariable1 = ob_get_clean();
echo $_prefixVariable1;?>
px;float:left;">
            <!-- <?php echo $_smarty_tpl->tpl_vars['v']->value['stamp'];?>
 -->

            <!-- <?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['title'])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_arr']->value[$_smarty_tpl->tpl_vars['v']->value['cpkg']])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo $_smarty_tpl->tpl_vars['v']->value['num'];?>
-<?php echo $_smarty_tpl->tpl_vars['v']->value['sort'];
if ($_smarty_tpl->tpl_vars['v']->value['num'] != 1) {?> + <span style="color:red;font-weight: bold;">combine</span><?php }?> <br /> -->
            <img src="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['stamp'])===null||$tmp==='' ? '' : $tmp);?>
" class="pics" />
        </div>
        <?php }?>
        <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
    <?php } else { ?>
        <!-- <img src=" <?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['stamp'])===null||$tmp==='' ? '' : $tmp);?>
" style="width:560px;height:<?php ob_start();
echo (($tmp = @$_smarty_tpl->tpl_vars['aconfig']->value['conf_fsheete']-40)===null||$tmp==='' ? 880 : $tmp);
$_prefixVariable2 = ob_get_clean();
echo $_prefixVariable2;?>
px;"> -->
        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, (($tmp = @$_smarty_tpl->tpl_vars['data']->value)===null||$tmp==='' ? array() : $tmp), 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
        <div id="printCss">
            <div class="title">
                <?php if ($_smarty_tpl->tpl_vars['v']->value['cpkg'] == 118 || $_smarty_tpl->tpl_vars['v']->value['cpkg'] == 120 || $_smarty_tpl->tpl_vars['v']->value['cpkg'] == 121) {?>
                    <?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['title'])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_arr']->value[$_smarty_tpl->tpl_vars['v']->value['cpkg']])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo $_smarty_tpl->tpl_vars['v']->value['numSum'];?>
-<?php echo $_smarty_tpl->tpl_vars['v']->value['sort']-1;?>
 / <?php echo $_smarty_tpl->tpl_vars['v']->value['numSum'];?>
-<?php echo $_smarty_tpl->tpl_vars['v']->value['sort'];
if ($_smarty_tpl->tpl_vars['v']->value['numSum'] != 1) {?> + <span id="printSpan">combine</span><?php }?>
                <?php } else { ?>
                    <?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['title'])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_arr']->value[$_smarty_tpl->tpl_vars['v']->value['cpkg']])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo $_smarty_tpl->tpl_vars['v']->value['numSum'];?>
-<?php echo $_smarty_tpl->tpl_vars['v']->value['sort'];
if ($_smarty_tpl->tpl_vars['v']->value['numSum'] != 1) {?> + <span id="printSpan">combine</span><?php }?>
                <?php }?>
                
            </div>
            <!-- <span id="printCssSpan"><?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['title'])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_arr']->value[$_smarty_tpl->tpl_vars['v']->value['cpkg']])===null||$tmp==='' ? '' : $tmp);?>
 + <?php echo $_smarty_tpl->tpl_vars['v']->value['num'];?>
-<?php echo $_smarty_tpl->tpl_vars['v']->value['sort'];
if ($_smarty_tpl->tpl_vars['v']->value['num'] != 1) {?> + <span style="color:red;font-weight: bold;">combine</span><?php }?> <br /></span> -->
            <img src="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['pic'])===null||$tmp==='' ? '' : $tmp);?>
.png" id="printCssImg" class="pics" />
            
        </div>
        <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
    <?php }?>
</div>
<div class="box-footer">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button>
</div>
<?php echo '<script'; ?>
>
    $(document).ready(function (e) {
        //tif2img("#printdiv");
    });

    function printSuccess(){
        var userResponse = confirm("你确定要批量修改产品状态吗？请先确认打印无误");
        if (userResponse) {
            console.log("用户点击了确定。");
            // 执行操作
            $.ajax({
                url: '/index.php/htgl/sorder/dprintAll',
                type: 'GET',
                data:{
                    'idAll': <?php echo $_smarty_tpl->tpl_vars['idAll']->value;?>

                },
                contentType: "application/json",
                async: true,
                dataType: 'json',
                success: function(data){
                    if(data == 1){
                        alert('批量生产状态修改成功');
                        location.reload();
                    }else {
                        alert('批量生产状态修改失败');
                    }
                },
            });
        } else {
            console.log("用户点击了取消。");
            // 执行其他操作或者什么都不做
        }
        
        // alert(123);
    }
<?php echo '</script'; ?>
><?php }
}
