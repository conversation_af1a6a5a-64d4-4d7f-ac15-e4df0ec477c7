<?php
/* Smarty version 3.1.33, created on 2024-07-26 15:55:01
  from '/var/www/html/favo/application/views/admin/user/product.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a35655d7b783_80042977',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '26a13af030b3fc6af52d566293a91ba33a2f0d9f' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/user/product.tpl',
      1 => 1721806728,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a35655d7b783_80042977 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<form method="post" action="<?php echo RELROUTE;?>
/user/product_update">
    <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
    <input type="hidden" name="uid" value="<?php echo $_smarty_tpl->tpl_vars['uid']->value;?>
">

    <h3 class="box-title">分配产品</h3>

    <div class="box-body table-responsive no-padding">
            
            
            
            <div class="col-xs-12" >
               
                <ul class="role_control_setting" style=''>
               
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['lst']->value, 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
                    <li>
                        <?php echo smarty_function_formgroup(array('t'=>'authcbox','name'=>'authaction[]','value'=>$_smarty_tpl->tpl_vars['v']->value['nid'],'desc'=>$_smarty_tpl->tpl_vars['v']->value['title'],'auth'=>$_smarty_tpl->tpl_vars['role_auth']->value),$_smarty_tpl);?>

                    </li>
                    <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>    
            </div>    
            
           
            


        <div class="box-footer">
            &nbsp;<br />&nbsp;<br />
            <input class="btn" type="button" onclick="location='<?php echo RELROUTE;?>
/user/list'" value="返回">
            <input class="btn btn-info pull-right" type="submit" value="更 新">
        </div>
    </div>
</form>
<?php echo '<script'; ?>
 type="text/javascript">
    $(document).ready(function (e) {
        $('.app').click(function (e) {
            //console.log($(this).val());
            var o = $(this).is(':checked');
            var a = $(this).val().split('/');
            //console.log(a.length);
            if (a.length == 1) {
                //app
                $("#app_" + a[0] + " input").prop("checked", o);
            }
            if (a.length == 2) {
                //app
                $("#mod_" + a[0] + "_" + a[1] + " input").prop("checked", o);
            }
        });
    });
<?php echo '</script'; ?>
>
<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
