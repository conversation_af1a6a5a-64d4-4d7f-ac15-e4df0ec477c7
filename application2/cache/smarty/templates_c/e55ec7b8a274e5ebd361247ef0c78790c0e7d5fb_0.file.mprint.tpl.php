<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:55:41
  from '/var/www/html/favo/application/views/admin/sorder/mprint.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1ccbde3a510_49271148',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'e55ec7b8a274e5ebd361247ef0c78790c0e7d5fb' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/mprint.tpl',
      1 => 1721879277,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_66a1ccbde3a510_49271148 (Smarty_Internal_Template $_smarty_tpl) {
?><div class="box-header">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button>
</div>
<div class="a6printw" id="printdiv">
    <div class="a6print pagep" style="height:<?php ob_start();
echo (($tmp = @$_smarty_tpl->tpl_vars['aconfig']->value['conf_fsheete'])===null||$tmp==='' ? 900 : $tmp);
$_prefixVariable1 = ob_get_clean();
echo $_prefixVariable1;?>
px;b">
        <img src=" <?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['stamp'])===null||$tmp==='' ? '' : $tmp);?>
" style="width:560px;height:<?php ob_start();
echo (($tmp = @$_smarty_tpl->tpl_vars['aconfig']->value['conf_fsheete']-40)===null||$tmp==='' ? 880 : $tmp);
$_prefixVariable2 = ob_get_clean();
echo $_prefixVariable2;?>
px;">
    </div>
    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, (($tmp = @$_smarty_tpl->tpl_vars['data']->value['lst'])===null||$tmp==='' ? array() : $tmp), 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
    <div class="a6print pagep" style="height:<?php ob_start();
echo (($tmp = @$_smarty_tpl->tpl_vars['aconfig']->value['conf_fsheete'])===null||$tmp==='' ? 900 : $tmp);
$_prefixVariable3 = ob_get_clean();
echo $_prefixVariable3;?>
px;">
        <img src="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['pic'])===null||$tmp==='' ? '' : $tmp);?>
.png" class="pics" />
        <br /> <span class="cpsl">数量：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['num'])===null||$tmp==='' ? '' : $tmp);?>
</span> <br />
        订单ID：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
 <br />
        产品ID：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
 <br />
        客户名称：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['title'])===null||$tmp==='' ? '' : $tmp);?>
 <br />
        店主：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c3_arr']->value[$_smarty_tpl->tpl_vars['data']->value['s1']])===null||$tmp==='' ? '' : $tmp);?>
 <br />
        产品类型：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c1_arr']->value[$_smarty_tpl->tpl_vars['v']->value['cplx']])===null||$tmp==='' ? '' : $tmp);?>
<br />
        产品规格：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_arr']->value[$_smarty_tpl->tpl_vars['v']->value['cpkg']])===null||$tmp==='' ? '' : $tmp);?>
<br />
        下单时间：<?php if ((($tmp = @$_smarty_tpl->tpl_vars['data']->value['oid'])===null||$tmp==='' ? 0 : $tmp) > 0) {
echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['data']->value['oid']))===null||$tmp==='' ? '' : $tmp);
}?><br />
        提交时间：<?php if ((($tmp = @$_smarty_tpl->tpl_vars['data']->value['a1'])===null||$tmp==='' ? 0 : $tmp) > 0) {
echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['data']->value['a1']))===null||$tmp==='' ? '' : $tmp);
}?><br />
        生产日期：<?php if ((($tmp = @$_smarty_tpl->tpl_vars['data']->value['ptime'])===null||$tmp==='' ? 0 : $tmp) > 0) {
echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['data']->value['ptime']))===null||$tmp==='' ? '' : $tmp);
}?><br />
        产品内容：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['s1'])===null||$tmp==='' ? '' : $tmp);?>
<br />
    </div>
    <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
</div>
<div class="box-footer">
    <button onclick="doprint()" class="btn btn-success pull-left">打印</button>
    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"
        onclick="location.reload();">关闭</button>
</div>
<?php echo '<script'; ?>
>
    $(document).ready(function (e) {
        //tif2img("#printdiv");
    });
<?php echo '</script'; ?>
><?php }
}
