<?php
/* Smarty version 3.1.33, created on 2024-07-27 09:41:16
  from '/var/www/html/favo/application/views/admin/column/input.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a4503c7347d4_94617861',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '3f7ab3c47ac8570646d87c6f9cf73deccceac512' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/column/input.tpl',
      1 => 1717754821,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a4503c7347d4_94617861 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<style>
    select[readonly] {
        background: #eee;
        cursor: no-drop;
    }

    select[readonly] option {
        display: none;
    }
</style>
<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title"><?php echo $_smarty_tpl->tpl_vars['modeName']->value;
if (ACTION == "add") {?>添加<?php }
if (ACTION == "update") {?>修改<?php }?></h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="<?php echo URI;?>
" method="post">
                <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
                <div class="box-body">


                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"名称",'id'=>"title",'name'=>"title",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['title'])===null||$tmp==='' ? '' : $tmp),'validator'=>" required data-bv-notempty-message='名称不能为空' "),$_smarty_tpl);?>



                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"分类",'id'=>"tid",'name'=>"tid",'lst'=>$_smarty_tpl->tpl_vars['tid_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['tid'])===null||$tmp==='' ? 0 : $tmp)),$_smarty_tpl);?>

                    
                    <?php if ((($tmp = @$_smarty_tpl->tpl_vars['data']->value['tid'])===null||$tmp==='' ? 0 : $tmp) == 1) {?>
                    <?php echo smarty_function_formgroup(array('t'=>'input','desc'=>"单价",'id'=>"description",'name'=>"description",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['description'])===null||$tmp==='' ? 0 : $tmp)),$_smarty_tpl);?>

                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"所属类型",'id'=>"oid",'name'=>"oid",'lst'=>$_smarty_tpl->tpl_vars['c1_arr']->value,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['oid'])===null||$tmp==='' ? 0 : $tmp)),$_smarty_tpl);?>

                    <?php }?>

                    <?php echo smarty_function_formgroup(array('t'=>'select','desc'=>"是否显示",'id'=>"status",'name'=>"status",'lst'=>"tf",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['data']->value['status'])===null||$tmp==='' ? 1 : $tmp)),$_smarty_tpl);?>


                    <input type="hidden" name="nid" id="nid" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/list?stid=<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['tid'])===null||$tmp==='' ? 0 : $tmp);?>
">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<?php echo '<script'; ?>
 type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {
        $('#input_form').bootstrapValidator();
        $('#tid').attr('readonly', true);
    });
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
