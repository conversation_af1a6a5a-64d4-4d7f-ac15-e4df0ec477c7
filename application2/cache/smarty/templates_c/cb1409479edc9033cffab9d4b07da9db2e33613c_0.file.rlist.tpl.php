<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:51:46
  from '/var/www/html/favo/application/views/admin/sorder/rlist.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1cbd2dd1897_71455741',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'cb1409479edc9033cffab9d4b07da9db2e33613c' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/rlist.tpl',
      1 => 1721879277,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a1cbd2dd1897_71455741 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/jquery/printThis.js"><?php echo '</script'; ?>
>
<!--日期框-->
<link href="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"><?php echo '</script'; ?>
>
<style>
    th {
        font-size: 16px;
    }

    .tprice {
        font-weight: 600;
    }

</style>
<div class="box">

    <form class="form-horizontal" id="search_form" action="<?php echo URI;?>
">
        <div class="box-header">
            <h3 class="box-title">生产产品列表</h3>
        </div>
        <div class="box-body searchform">
            <!-- /.box-header -->
            <div class="box-body">
                <div class="row">

                    <div class="col-sm-5">
                        <?php ob_start();
echo (($tmp = @$_smarty_tpl->tpl_vars['s']->value['stitle'])===null||$tmp==='' ? '' : $tmp);
$_prefixVariable1=ob_get_clean();
echo smarty_function_formgroup(array('t'=>'input','desc'=>"输入客户",'id'=>"stitle",'name'=>"stitle",'value'=>$_prefixVariable1),$_smarty_tpl);?>

                    </div>
                    <div class="col-sm-3">
                        <code style="float: left;">(*)</code>
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"订单状态",'id'=>"sstatus",'name'=>"sstatus",'lst'=>$_smarty_tpl->tpl_vars['env']->value['sstatus'],'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sstatus'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-3">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"产品类型",'id'=>"scplx",'name'=>"scplx",'lst'=>$_smarty_tpl->tpl_vars['c1_arr']->value,'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['scplx'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                    
                    <div class="col-sm-3" id="scpkgdiv">
                        <code style="float: left;">(*)</code>
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"尺寸类型",'id'=>"scpkg",'name'=>"scpkg",'lst'=>array(),'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['scpkg'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>

                    <div class="col-sm-3">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"订单店主",'id'=>"sdddz",'name'=>"sdddz",'lst'=>$_smarty_tpl->tpl_vars['c3_arr']->value,'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sdddz'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>

                    <div class="col-sm-3">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"邮票状态",'id'=>"sstamp",'name'=>"sstamp",'lst'=>$_smarty_tpl->tpl_vars['env']->value['sstamp'],'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sstamp'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>

                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker2','desc'=>'订单提交时间（开始）','id'=>"stjsjs",'name'=>"stjsjs",'style'=>"datepicker",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['stjsjs'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker2','desc'=>'订单提交时间（结束）','id'=>"stjsje",'name'=>"stjsje",'style'=>"datepicker",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['stjsje'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>

                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker2','desc'=>'订单生产时间（开始）','id'=>"sscsjs",'name'=>"sscsjs",'style'=>"datepicker",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sscsjs'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker2','desc'=>'订单生产时间（结束）','id'=>"sscsje",'name'=>"sscsje",'style'=>"datepicker",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sscsje'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                    
                </div>
            </div>
            <div class="btn-group">
                <?php echo smarty_function_formgroup(array('t'=>'searchbtn','type'=>"lg"),$_smarty_tpl);?>

            </div>
            
            <a href="<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/export?<?php echo http_build_query($_smarty_tpl->tpl_vars['s']->value);?>
" target="_blank"
                style="float:right;width: 120px;height: 45px;line-height: 36px;font-size: 18px;text-align: center;"
                class="btn btn-danger btn-sm " >
                导出记录
            </a>
            <!-- <a style="float:right;width:90px;height:46px;background-color:red;line-height: 46px;text-align: center;" href="<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/printall">123</a> -->
            <?php if ($_smarty_tpl->tpl_vars['s']->value['scplx'] == 106 && $_smarty_tpl->tpl_vars['s']->value['scpkg'] > -99 && $_smarty_tpl->tpl_vars['s']->value['sstatus'] > -99) {?>
            <button type="button"
                style="width: 120px;height: 45px;font-size: 13px;"
                onclick="showMyModal('批量打印生产文件','<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/mprintjiuAll?stitle=<?php echo $_smarty_tpl->tpl_vars['s']->value['stitle'];?>
&sstatus=<?php echo $_smarty_tpl->tpl_vars['s']->value['sstatus'];?>
&scplx=<?php echo $_smarty_tpl->tpl_vars['s']->value['scplx'];?>
&sdddz=<?php echo $_smarty_tpl->tpl_vars['s']->value['sdddz'];?>
&sstamp=<?php echo $_smarty_tpl->tpl_vars['s']->value['sstamp'];?>
&stjsjs=<?php echo $_smarty_tpl->tpl_vars['s']->value['stjsjs'];?>
&stjsje=<?php echo $_smarty_tpl->tpl_vars['s']->value['stjsje'];?>
&sscsjs=<?php echo $_smarty_tpl->tpl_vars['s']->value['sscsjs'];?>
&sscsje=<?php echo $_smarty_tpl->tpl_vars['s']->value['sscsje'];?>
&scpkg=<?php echo $_smarty_tpl->tpl_vars['s']->value['scpkg'];?>
&sstatus=<?php echo $_smarty_tpl->tpl_vars['s']->value['sstatus'];?>
');"
                class="btn btn-danger btn-sm " data-toggle="modal"
                data-target="#myModal" value="">
                批量打印生产文件
            </button>
            <button type="button"
                style="width: 120px;height: 45px;font-size: 13px;"
                onclick="showMyModal('批量打印邮票','<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/mprintStamp?lstNidArr=<?php echo $_smarty_tpl->tpl_vars['lstNidArr']->value;?>
');"
                class="btn btn-danger btn-sm " data-toggle="modal"
                data-target="#myModal" value="">
                批量打印邮票
            </button>
            <?php }?>
            
        </div>
        <!-- /.box-body -->
</div>
</form>

</div>
<div class="box">
    <!-- /.box-header -->
    <div class="box-body no-padding">
        <table class="table">
            <tbody>
                <tr>
                    <th>订单ID</th>
                    <th>客户名称<br />店主归属</th>
                    <th>下单时间<br />提交时间</th>
                    <th>订单状态</th>
                    <th>所属账号/数量</th>
                    <th>价格(美金)</th>
                    <th>操作</th>
                </tr>
                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['lst']->value, 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
                <tr>
                    <td><?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
</td>
                    <td>
                        <?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['title'])===null||$tmp==='' ? '' : $tmp);?>

                        <br />店主：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c3_arr']->value[$_smarty_tpl->tpl_vars['v']->value['s1']])===null||$tmp==='' ? '' : $tmp);?>

                    </td>
                    <td>
                        下单时间：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['v']->value['oid']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['a1'])===null||$tmp==='' ? 0 : $tmp) > 0) {?>
                        <br />提交时间：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['v']->value['a1']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php }?>
                    </td>
                    <td>
                        <?php echo (($tmp = @$_smarty_tpl->tpl_vars['env']->value['sstatus'][$_smarty_tpl->tpl_vars['v']->value['status']])===null||$tmp==='' ? '' : $tmp);?>


                        <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['status'])===null||$tmp==='' ? '1' : $tmp) == 3) {?>
                        <br />生产日期：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['v']->value['ptime']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php }?>
                    </td>
                    <td><?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['creater'])===null||$tmp==='' ? '' : $tmp);?>

                        <br />
                        <?php if ($_smarty_tpl->tpl_vars['v']->value['stamp']) {?>
                        <a href="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['stamp'])===null||$tmp==='' ? '' : $tmp);?>
" target="_blank">查看邮票</a>
                        <?php }?>
                    </td>
                    <td nid="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
" class="tprice">0</td>
                    <td>
                        <div class="btn-group" style="padding-top:10px">
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['qx']->value['mprint'])===null||$tmp==='' ? false : $tmp)) {?>
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['status'])===null||$tmp==='' ? '1' : $tmp) >= 2) {?>
                            <button type="button"
                                onclick="showMyModal('打印面单','<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/mprint?nid=<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
');"
                                class="btn btn-info btn-sm <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['a2'])===null||$tmp==='' ? 0 : $tmp) > 0) {?>disabled<?php }?>" data-toggle="modal"
                                data-target="#myModal" value="">
                                【<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['a2'])===null||$tmp==='' ? 0 : $tmp);?>
】打印面单
                            </button>
                                <?php if ($_smarty_tpl->tpl_vars['v']->value['pdater']) {?>
                                <button type="button"
                                 class="btn btn-info btn-sm ">
                                打印者：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['pdater'])===null||$tmp==='' ? '' : $tmp);?>

                                </button>
                                <?php }?>
                            <?php }?>
                            <?php }?>
    
                            <!-- <?php if ($_smarty_tpl->tpl_vars['s']->value['scplx'] == 106) {?>
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['qx']->value['mprint'])===null||$tmp==='' ? false : $tmp)) {?>
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['status'])===null||$tmp==='' ? '1' : $tmp) >= 2) {?>
                            <button type="button"
                                onclick="showMyModal('打印生产文件','<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/mprintjiu?nid=<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
');"
                                class="btn btn-danger btn-sm <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['a2'])===null||$tmp==='' ? 0 : $tmp) > 0) {?>disabled<?php }?>" data-toggle="modal"
                                data-target="#myModal" value="">
                                打印生产文件
                            </button>
                            <?php }?>
                            <?php }?>
                         </div>    
                        <?php }?> -->
                    </td>
                </tr>

                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, (($tmp = @$_smarty_tpl->tpl_vars['v']->value['plst'])===null||$tmp==='' ? array() : $tmp), 'vv', false, 'kk');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['kk']->value => $_smarty_tpl->tpl_vars['vv']->value) {
?>
                <tr>
                    <td>产品ID：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
<br /><?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['pm'])===null||$tmp==='' ? '' : $tmp);?>
</td>
                    <td>
                        <?php if ($_smarty_tpl->tpl_vars['vv']->value['pic']) {?>
                        <img src="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['pic'])===null||$tmp==='' ? '' : $tmp);?>
.png" data-toggle="modal" data-target="#imageModal" data-img-src="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['pic'])===null||$tmp==='' ? '' : $tmp);?>
.png" style="height: 50px;" />
                        
                        <!-- <div id="imageContainer"></div> -->

                        
                        <!-- 图片模态框 -->
                        <div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                </div>
                                <div class="modal-body">
                                <img src="" id="imageModalSrc" alt="图片加载中" class="img-responsive center-block d-block mx-auto">
                                </div>
                            </div>
                            </div>
                        </div>

                        <br />
                        <a href="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['pic'])===null||$tmp==='' ? '' : $tmp);?>
"
                            download="<?php echo (($tmp = @getdlname($_smarty_tpl->tpl_vars['v']->value,$_smarty_tpl->tpl_vars['vv']->value,$_smarty_tpl->tpl_vars['c1_arr']->value,$_smarty_tpl->tpl_vars['c2_arr']->value,$_smarty_tpl->tpl_vars['c3_arr']->value))===null||$tmp==='' ? '' : $tmp);?>
"
                            target="_blank">下载生产文件</a>
                        <?php }?>
                    </td>
                    <td class="wb">
                        类型：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c1_arr']->value[$_smarty_tpl->tpl_vars['vv']->value['cplx']])===null||$tmp==='' ? '' : $tmp);?>

                        <br />
                        规格：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_arr']->value[$_smarty_tpl->tpl_vars['vv']->value['cpkg']])===null||$tmp==='' ? '' : $tmp);?>

                        <br />
                        内容：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['s1'])===null||$tmp==='' ? '' : $tmp);?>

                    </td>
                    <td>
                        <?php echo (($tmp = @$_smarty_tpl->tpl_vars['env']->value['pstatus'][$_smarty_tpl->tpl_vars['vv']->value['status']])===null||$tmp==='' ? '' : $tmp);?>

                        <?php if ((($tmp = @$_smarty_tpl->tpl_vars['vv']->value['a1'])===null||$tmp==='' ? 0 : $tmp) > 0) {?>
                        <br />提交时间：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['vv']->value['a1']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php }?>
                        <?php if ((($tmp = @$_smarty_tpl->tpl_vars['vv']->value['status'])===null||$tmp==='' ? '1' : $tmp) == 3) {?>
                        <br />生产时间：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['vv']->value['ptime']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php }?>
                    </td>
                    <td>数量：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['num'])===null||$tmp==='' ? '' : $tmp);?>
 </td>
                    <td class="cprice_<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
"><?php echo (($tmp = @($_smarty_tpl->tpl_vars['c2_1_arr']->value[$_smarty_tpl->tpl_vars['vv']->value['cpkg']]*$_smarty_tpl->tpl_vars['vv']->value['num']))===null||$tmp==='' ? 0 : $tmp);?>
</td>
                    <td>
                        <div class="btn-group">
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['vv']->value['status'])===null||$tmp==='' ? '1' : $tmp) == 2) {?>
                            <button type="button"
                                onclick="doDelete('确定要回退订单吗？','<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/dprint?nid=<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
&t=-1');"
                                class="btn btn-info btn-sm"><i class="fa fa-print"></i>&nbsp;&nbsp;回退订单</button>
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['a2'])===null||$tmp==='' ? 0 : $tmp) > 0) {?>
                            <button type="button"
                                onclick="doDelete('确定要改变状态为已生产吗？','<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/dprint?nid=<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
&t=3');"
                                class="btn btn-danger btn-sm"><i class="fa fa-print"></i>&nbsp;&nbsp;设为已生产</button>
                            <?php }?>
                            <?php }?>
                            <?php if (1 == 2) {?>
                            <a class="btn btn-sm btn-success" data-toggle="modal" data-target="#printModal"
                                onclick="showprintModalImg('查看','<?php echo $_smarty_tpl->tpl_vars['vv']->value['pm'];?>
','<?php echo $_smarty_tpl->tpl_vars['vv']->value['pic'];?>
');" href="#"><i
                                    class="fa fa-eye"></i>&nbsp;&nbsp;查看打印</a>
                            <?php }?>
                        </div>


                    </td>
                </tr> <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?> </tr>
                <tr>
                    <td colspan="20" style="background-color: #f4f4f4;height: 5px;font-size: 1px;">&nbsp;</td>
                </tr>
                <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

            </tbody>
        </table>
    </div>
    <!-- /.box-body -->
    <div class="box-footer clearfix">
        <?php echo smarty_function_formgroup(array('t'=>'page','page'=>$_smarty_tpl->tpl_vars['page']->value),$_smarty_tpl);?>

    </div>
</div>
<!-- /.box -->
<?php echo '<script'; ?>
>
    
    $(document).on('click', '[data-toggle="modal"]', function () {
        var imgSrc = $(this).data('img-src');
        $('#imageModalSrc').attr('src', imgSrc);
    });


    function doprint() {
        $("#printdiv").printThis({
            debug: false,
            importCSS: true,
            importStyle: true,
            printContainer: true,
            // size: {
            //     w: '124mm',
            //     h: '190mm'
            // },
            // 'margin-top': '0mm',
            // 'margin-bottom': '0mm',
            // 'margin-left': '0mm',
            // 'margin-right': '0mm',
            // mediaPrint: [{
            //     selector: 'body',
            //     properties: {
            //     'width': '100%',
            //     'height': '100%',
            //     'margin': '0',
            //     'padding': '0'
            //     }
            // }],
            //    loadCSS: "/Content/Themes/Default/style.css", 
            pageTitle: "",
            removeInline: false,
            printDelay: 333,
            header: null,
            formValues: false,
            afterPrint: function() {
                console.log('打印成功！');
            }
        });
    }

    function doprintB() {
        $("#printdivB").printThis({
            debug: false,
            importCSS: true,
            importStyle: true,
            printContainer: true,
            // size: {
            //     w: '124mm',
            //     h: '190mm'
            // },
            // 'margin-top': '0mm',
            // 'margin-bottom': '0mm',
            // 'margin-left': '0mm',
            // 'margin-right': '0mm',
            // mediaPrint: [{
            //     selector: 'body',
            //     properties: {
            //     'width': '100%',
            //     'height': '100%',
            //     'margin': '0',
            //     'padding': '0'
            //     }
            // }],
            //    loadCSS: "/Content/Themes/Default/style.css", 
            pageTitle: "",
            removeInline: false,
            printDelay: 333,
            header: null,
            formValues: false,
            afterPrint: function() {
                console.log('打印成功！');
            }
        });
    }

    function showPic(pic) {
        //alert(pic);
        showMyModalHtml("显示图片", "<img src='" + pic + "' class='m_img'/>")
    }

    $(document).ready(function (e) {

        $("td.tprice").each(function (index, element) {
            //console.log($(this).attr('nid'));
            var totle = 0.0;
            var nid = $(this).attr('nid');
            $("td.cprice_" + nid).each(function (index, element) {
                totle = totle + parseFloat($(this).html());
            });
            $(this).html(totle);
        });

        $('.datepicker').datetimepicker({
            format: "yyyy-mm-dd", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'month',
            autoclose: true //选择日期后自动关闭
        });
        //tif2img();
    });

    var c2_sjson = '<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_json']->value)===null||$tmp==='' ? array() : $tmp);?>
';
    var c2json = $.parseJSON(c2_sjson);
    $("#scpkgdiv").hide();
  
    var scpkg = '<?php echo (($tmp = @$_smarty_tpl->tpl_vars['s']->value['scpkg'])===null||$tmp==='' ? 0 : $tmp);?>
';
    //console.log(c2json);

    if('<?php echo $_smarty_tpl->tpl_vars['s']->value['scplx'] == 106;?>
'){
        $("#scpkgdiv").show();
    }
    
    $(document).ready(function (e) {
        setCPGG($('#scplx').val());
        $('#scplx').on('change', function () {
            if($('#scplx').val() == 106){
                setCPGG($('#scplx').val());
                $("#scpkgdiv").show();
            }else {
                $("#scpkg").empty();
                $("#scpkgdiv").hide();
            }
            
            
        });
    });
    

    function setCPGG(id) {
        $("#scpkg").empty();
        $("#scpkg").append('<option value="-99" sleected="selected">全部</option>');
        $.each(c2json[id], function (key, value) {
            // console.log(c2json[id]);
            console.log('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
            var sleected = '';
            if (key == scpkg) {
                sleected = ' selected="selected" '
            }
            $("#scpkg").append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
        });
    }
<?php echo '</script'; ?>
>
<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
