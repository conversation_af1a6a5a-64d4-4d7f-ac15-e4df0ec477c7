<?php
/* Smarty version 3.1.33, created on 2024-07-25 09:41:48
  from '/var/www/html/favo/application/views/admin/user/role_auth_setting.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1ad5c574029_85095851',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'f7ec754ef0a141a55230f1a37863eac0ab79bdd6' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/user/role_auth_setting.tpl',
      1 => 1702388592,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a1ad5c574029_85095851 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<form method="post" action="<?php echo RELROUTE;?>
/user/role_auth_update">
    <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
    <input type="hidden" name="role_id" value="<?php echo $_smarty_tpl->tpl_vars['odata']->value['role_id'];?>
">

    <h3 class="box-title">[<?php echo $_smarty_tpl->tpl_vars['odata']->value['role_name'];?>
] 角色权限分配</h3>

    <div class="box-body table-responsive no-padding">

        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['_authaction']->value, 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
        <div class="col-xs-12" id="app_<?php echo $_smarty_tpl->tpl_vars['v']->value['app'];?>
">
            <h4>
                <?php echo smarty_function_formgroup(array('t'=>'authcbox','name'=>'authaction[]','value'=>$_smarty_tpl->tpl_vars['v']->value['app'],'desc'=>$_smarty_tpl->tpl_vars['v']->value['name'],'auth'=>$_smarty_tpl->tpl_vars['role_auth']->value),$_smarty_tpl);?>

            </h4>
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['v']->value['children'], 'cv', false, 'ck');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['ck']->value => $_smarty_tpl->tpl_vars['cv']->value) {
?>
            <div id="mod_<?php echo $_smarty_tpl->tpl_vars['v']->value['app'];?>
_<?php echo $_smarty_tpl->tpl_vars['cv']->value['module'];?>
" class="col-xs-12" style="padding: 20px;border-bottom: 1px solid #ccc;">
                <h5>
                    <?php echo smarty_function_formgroup(array('t'=>'authcbox','name'=>'authaction[]','value'=>(($_smarty_tpl->tpl_vars['v']->value['app']).('/')).($_smarty_tpl->tpl_vars['cv']->value['module']),'desc'=>$_smarty_tpl->tpl_vars['cv']->value['name'],'auth'=>$_smarty_tpl->tpl_vars['role_auth']->value),$_smarty_tpl);?>

                </h5>
                <ul class="role_control_setting">
                    <!-- 三级 -->
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['cv']->value['children'], 'ccv', false, 'cck');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['cck']->value => $_smarty_tpl->tpl_vars['ccv']->value) {
?>
                    <li>
                        <?php echo smarty_function_formgroup(array('t'=>'authcbox','name'=>'authaction[]','value'=>(((($_smarty_tpl->tpl_vars['v']->value['app']).('/')).($_smarty_tpl->tpl_vars['cv']->value['module'])).('/')).($_smarty_tpl->tpl_vars['ccv']->value['action']),'desc'=>$_smarty_tpl->tpl_vars['ccv']->value['name'],'auth'=>$_smarty_tpl->tpl_vars['role_auth']->value),$_smarty_tpl);?>

                    </li>
                    <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
            <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </div>
        <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

        <div class="box-footer">
            &nbsp;<br />&nbsp;<br />
            <input class="btn" type="button" onclick="location='<?php echo RELROUTE;?>
/user/role'" value="返回">
            <input class="btn btn-info pull-right" type="submit" value="更 新">
        </div>
    </div>
</form>
<?php echo '<script'; ?>
 type="text/javascript">
    $(document).ready(function (e) {
        $('.app').click(function (e) {
            //console.log($(this).val());
            var o = $(this).is(':checked');
            var a = $(this).val().split('/');
            //console.log(a.length);
            if (a.length == 1) {
                //app
                $("#app_" + a[0] + " input").prop("checked", o);
            }
            if (a.length == 2) {
                //app
                $("#mod_" + a[0] + "_" + a[1] + " input").prop("checked", o);
            }
        });
    });
<?php echo '</script'; ?>
>
<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
