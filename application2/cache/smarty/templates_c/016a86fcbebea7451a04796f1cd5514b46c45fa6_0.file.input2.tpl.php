<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:51:36
  from '/var/www/html/favo/application/views/admin/sorder/input2.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1cbc8631885_44129552',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '016a86fcbebea7451a04796f1cd5514b46c45fa6' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/input2.tpl',
      1 => 1721879277,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/fileinputjs.tpl' => 1,
    'file:admin/sorder/intut_p.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a1cbc8631885_44129552 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
$_smarty_tpl->_subTemplateRender('file:admin/public/fileinputjs.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<!--日期框-->
<link href="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"><?php echo '</script'; ?>
>

<link href="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/css/bootstrap-select.min.css" rel="stylesheet">
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/js/bootstrap-select.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/bootstrap-select/js/i18n/defaults-zh_CN.min.js"><?php echo '</script'; ?>
>

<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">产品<?php if (ACTION == "add2") {?>添加<?php }
if (ACTION == "update2") {?>修改<?php }?></h3>
            </div>
            <!-- /.box-header -->
            <!-- form start -->
            <form class="form-horizontal" id="input_form" action="<?php echo URI;?>
" method="post">
                <input type="hidden" name="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['name'];?>
" value="<?php echo $_smarty_tpl->tpl_vars['csrf']->value['hash'];?>
" />
                <div class="box-body">


                    <?php $_smarty_tpl->_subTemplateRender('file:admin/sorder/intut_p.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

                    <input type="hidden" name="nid" id="nid" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
">
                    <input type="hidden" name="cid" id="cid" value="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['data']->value['cid'])===null||$tmp==='' ? 0 : $tmp);?>
">
                    <input type="hidden" name="action" id="action" value="dosave">

                </div>
                <!-- /.box-body -->
                <div class="box-footer">
                    <a class="btn btn-default" href="<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/list">返 回</a>
                    <button type="submit" class="btn btn-info pull-right">保 存</button>
                </div>
                <!-- /.box-footer -->
            </form>
        </div>
    </div>
</div>
<?php echo '<script'; ?>
 type="text/javascript">
    // 加载完毕执行jq
    $(document).ready(function (e) {

        $('#input_form').submit(function (e) {
            if ($.trim($('#pic0').val()) == '') {
                show_error('产品图片不能为空');
                $('#pic0').focus();
                return false;
            }
        });
    });
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
