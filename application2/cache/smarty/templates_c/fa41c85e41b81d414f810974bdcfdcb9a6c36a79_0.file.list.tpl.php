<?php
/* Smarty version 3.1.33, created on 2024-07-25 11:51:45
  from '/var/www/html/favo/application/views/admin/sorder/list.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.33',
  'unifunc' => 'content_66a1cbd11c16d6_38220465',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'fa41c85e41b81d414f810974bdcfdcb9a6c36a79' => 
    array (
      0 => '/var/www/html/favo/application/views/admin/sorder/list.tpl',
      1 => 1721879276,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:admin/public/cheader.tpl' => 1,
    'file:admin/public/cfooter.tpl' => 1,
  ),
),false)) {
function content_66a1cbd11c16d6_38220465 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/var/www/html/favo/application/third_party/smarty/plugins/function.formgroup.php','function'=>'smarty_function_formgroup',),));
$_smarty_tpl->_subTemplateRender('file:admin/public/cheader.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/jquery/printThis.js"><?php echo '</script'; ?>
>
<!--日期框-->
<link href="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/bootstrap-datetimepicker.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['env']->value['public'];?>
/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"><?php echo '</script'; ?>
>
<style>
    th {
        font-size: 16px;
    }

    .tprice {
        font-weight: 600;
    }
</style>
<div class="box">

    <form class="form-horizontal" id="search_form" action="<?php echo URI;?>
">
        <div class="box-header">
            <h3 class="box-title"><?php echo $_smarty_tpl->tpl_vars['modeName']->value;?>
列表</h3>
        </div>
        <div class="box-body searchform">
            <!-- /.box-header -->
            <div class="box-body">
                <div class="row">

                    <div class="col-sm-6">
                        <?php ob_start();
echo (($tmp = @$_smarty_tpl->tpl_vars['s']->value['stitle'])===null||$tmp==='' ? '' : $tmp);
$_prefixVariable1=ob_get_clean();
echo smarty_function_formgroup(array('t'=>'input','desc'=>"输入客户",'id'=>"stitle",'name'=>"stitle",'value'=>$_prefixVariable1),$_smarty_tpl);?>

                    </div>
                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"订单状态",'id'=>"sstatus",'name'=>"sstatus",'lst'=>$_smarty_tpl->tpl_vars['env']->value['sstatus'],'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sstatus'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-4">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"产品类型",'id'=>"scplx",'name'=>"scplx",'lst'=>$_smarty_tpl->tpl_vars['c1_arr']->value,'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['scplx'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>

                    <div class="col-sm-4">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"订单店主",'id'=>"sdddz",'name'=>"sdddz",'lst'=>$_smarty_tpl->tpl_vars['c3_arr']->value,'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sdddz'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>

                    <div class="col-sm-4">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"邮票状态",'id'=>"sstamp",'name'=>"sstamp",'lst'=>$_smarty_tpl->tpl_vars['env']->value['sstamp'],'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sstamp'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                    
                    <?php if ($_smarty_tpl->tpl_vars['user_arr']->value) {?>
                    <div class="col-sm-4">
                        <?php echo smarty_function_formgroup(array('t'=>'sselect','desc'=>"订单所属账号",'id'=>"creater",'name'=>"creater",'lst'=>$_smarty_tpl->tpl_vars['user_arr']->value,'issearch'=>1,'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['creater'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                    <?php }?>
                    
                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker2','desc'=>'订单提交时间（开始）','id'=>"stjsjs",'name'=>"stjsjs",'style'=>"datepicker",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['stjsjs'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker2','desc'=>'订单提交时间（结束）','id'=>"stjsje",'name'=>"stjsje",'style'=>"datepicker",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['stjsje'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>

                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker2','desc'=>'订单生产时间（开始）','id'=>"sscsjs",'name'=>"sscsjs",'style'=>"datepicker",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sscsjs'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                    <div class="col-sm-6">
                        <?php echo smarty_function_formgroup(array('t'=>'inputdatepicker2','desc'=>'订单生产时间（结束）','id'=>"sscsje",'name'=>"sscsje",'style'=>"datepicker",'value'=>(($tmp = @$_smarty_tpl->tpl_vars['s']->value['sscsje'])===null||$tmp==='' ? '' : $tmp)),$_smarty_tpl);?>

                    </div>
                </div>
            </div>
            <div class="btn-group">
                <?php echo smarty_function_formgroup(array('t'=>'addbtn','url'=>(((RELROUTE).("/")).(MODULE)).("/add"),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['add'],'type'=>"lg",'desc'=>($_smarty_tpl->tpl_vars['modeName']->value).("添加")),$_smarty_tpl);?>

                <?php echo smarty_function_formgroup(array('t'=>'searchbtn','type'=>"lg"),$_smarty_tpl);?>

            </div>
        </div>
        <!-- /.box-body -->
</div>
</form>

</div>
<div class="box">
    <!-- /.box-header -->
    <div class="box-body no-padding">
        <table class="table">
            <tbody>
                <tr>
                    <th>订单ID</th>
                    <th>客户名称<br />店主归属</th>
                    <th>下单时间<br />提交时间</th>
                    <th>订单状态</th>
                    <th>所属账号/数量</th>
                    <th>价格(美金)</th>
                    <th>操作</th>
                </tr>
                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['lst']->value, 'v', false, 'k');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['k']->value => $_smarty_tpl->tpl_vars['v']->value) {
?>
                <tr>
                    <td><?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
</td>
                    <td>
                        <?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['title'])===null||$tmp==='' ? '' : $tmp);?>

                        <br />店主：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c3_arr']->value[$_smarty_tpl->tpl_vars['v']->value['s1']])===null||$tmp==='' ? '' : $tmp);?>

                    </td>
                    <td>
                        下单时间：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['v']->value['oid']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['a1'])===null||$tmp==='' ? 0 : $tmp) > 0) {?>
                        <br />提交时间：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['v']->value['a1']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php }?>
                    </td>
                    <td>
                        <?php echo (($tmp = @$_smarty_tpl->tpl_vars['env']->value['sstatus'][$_smarty_tpl->tpl_vars['v']->value['status']])===null||$tmp==='' ? '' : $tmp);?>

                        <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['status'])===null||$tmp==='' ? '1' : $tmp) == 3) {?>
                        <br />生产日期：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['v']->value['ptime']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php }?>
                    </td>
                    <td><?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['creater'])===null||$tmp==='' ? '' : $tmp);?>

                        <br />
                        <?php if ($_smarty_tpl->tpl_vars['v']->value['stamp']) {?>
                        <a href="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['stamp'])===null||$tmp==='' ? '' : $tmp);?>
" target="_blank">查看邮票</a>
                        <?php }?>
                    </td>
                    <td nid="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
" class="tprice">0</td>
                    <td>

                        <div class="btn-group">
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['status'])===null||$tmp==='' ? '1' : $tmp) <= 1) {?> <?php echo smarty_function_formgroup(array('t'=>'addbtn','url'=>((((RELROUTE).("/")).(MODULE)).("/add2?cid=")).($_smarty_tpl->tpl_vars['v']->value['nid']),'desc'=>'添加产品','isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['add2']),$_smarty_tpl);?>
 <?php echo smarty_function_formgroup(array('t'=>'editbtn','url'=>((((RELROUTE).("/")).(MODULE)).("/update?nid=")).($_smarty_tpl->tpl_vars['v']->value['nid']),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['update']),$_smarty_tpl);?>

                                <?php echo smarty_function_formgroup(array('t'=>'delbtn','url'=>((((RELROUTE).("/")).(MODULE)).("/delete?nid=")).($_smarty_tpl->tpl_vars['v']->value['nid']),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['delete']),$_smarty_tpl);?>
 <?php }?> </div> <br />
                            <div class="btn-group">
                                <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['status'])===null||$tmp==='' ? '1' : $tmp) <= 1) {?> <button type="button"
                                    onclick="doDelete('确定要提交生产吗？','<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/doprod?nid=<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
');"
                                    class="btn btn-success btn-sm">
                                    <i class="fa fa-check"></i>&nbsp;&nbsp;提交生产
                                    </button>
                                    <?php }?>
                                    <?php echo smarty_function_formgroup(array('t'=>'editbtn','url'=>((((RELROUTE).("/")).(MODULE)).("/update3?nid=")).($_smarty_tpl->tpl_vars['v']->value['nid']),'desc'=>'更新邮票','isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['update3']),$_smarty_tpl);?>

                                    <?php if ((($tmp = @$_smarty_tpl->tpl_vars['qx']->value['mprint'])===null||$tmp==='' ? false : $tmp)) {?>
                                    <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['status'])===null||$tmp==='' ? '1' : $tmp) >= 2) {?>
                                    <button type="button"
                                        onclick="showMyModal('打印面单','<?php echo RELROUTE;?>
/<?php echo MODULE;?>
/mprint?nid=<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? 0 : $tmp);?>
');"
                                        class="btn btn-info btn-sm <?php if ((($tmp = @$_smarty_tpl->tpl_vars['v']->value['a2'])===null||$tmp==='' ? 0 : $tmp) > 0) {?>disabled<?php }?>"
                                        data-toggle="modal" data-target="#myModal" value="">
                                        【<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['a2'])===null||$tmp==='' ? 0 : $tmp);?>
】打印面单
                                    </button>
                                    <?php }?>
                                    <?php }?>
                            </div>

                    </td>
                </tr>

                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, (($tmp = @$_smarty_tpl->tpl_vars['v']->value['plst'])===null||$tmp==='' ? array() : $tmp), 'vv', false, 'kk');
if ($_from !== null) {
foreach ($_from as $_smarty_tpl->tpl_vars['kk']->value => $_smarty_tpl->tpl_vars['vv']->value) {
?>
                <tr>
                    <td>产品ID：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
<br /><?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['pm'])===null||$tmp==='' ? '' : $tmp);?>
</td>
                    <td>
                        <?php if ($_smarty_tpl->tpl_vars['vv']->value['pic']) {?>
                        <img src="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['pic'])===null||$tmp==='' ? '' : $tmp);?>
.png" style="height: 50px;" />
                        <br />
                        <a href="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['pic'])===null||$tmp==='' ? '' : $tmp);?>
"
                            download="<?php echo (($tmp = @getdlname($_smarty_tpl->tpl_vars['v']->value,$_smarty_tpl->tpl_vars['vv']->value,$_smarty_tpl->tpl_vars['c1_arr']->value,$_smarty_tpl->tpl_vars['c2_arr']->value,$_smarty_tpl->tpl_vars['c3_arr']->value))===null||$tmp==='' ? '' : $tmp);?>
"
                            target="_blank">下载生产文件</a>
                        <?php }?>
                    </td>
                    <td class="wb">
                        类型：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c1_arr']->value[$_smarty_tpl->tpl_vars['vv']->value['cplx']])===null||$tmp==='' ? '' : $tmp);?>

                        <br />
                        规格：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['c2_arr']->value[$_smarty_tpl->tpl_vars['vv']->value['cpkg']])===null||$tmp==='' ? '' : $tmp);?>

                        <br />
                        内容：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['s1'])===null||$tmp==='' ? '' : $tmp);?>

                    </td>
                    <td>
                        <?php echo (($tmp = @$_smarty_tpl->tpl_vars['env']->value['pstatus'][$_smarty_tpl->tpl_vars['vv']->value['status']])===null||$tmp==='' ? '' : $tmp);?>

                        <?php if ((($tmp = @$_smarty_tpl->tpl_vars['vv']->value['a1'])===null||$tmp==='' ? 0 : $tmp) > 0) {?>
                        <br />提交时间：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['vv']->value['a1']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php }?>
                        <?php if ((($tmp = @$_smarty_tpl->tpl_vars['vv']->value['status'])===null||$tmp==='' ? '1' : $tmp) == 3) {?>
                        <br />生产时间：<?php echo (($tmp = @date("Y-m-d H:i",$_smarty_tpl->tpl_vars['vv']->value['ptime']))===null||$tmp==='' ? '' : $tmp);?>

                        <?php }?>
                    </td>
                    <td>数量：<?php echo (($tmp = @$_smarty_tpl->tpl_vars['vv']->value['num'])===null||$tmp==='' ? '' : $tmp);?>
 </td>
                    <td class="cprice_<?php echo (($tmp = @$_smarty_tpl->tpl_vars['v']->value['nid'])===null||$tmp==='' ? '' : $tmp);?>
"><?php echo (($tmp = @($_smarty_tpl->tpl_vars['c2_1_arr']->value[$_smarty_tpl->tpl_vars['vv']->value['cpkg']]*$_smarty_tpl->tpl_vars['vv']->value['num']))===null||$tmp==='' ? 0 : $tmp);?>
</td>
                    <td>
                        <div class="btn-group">
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['vv']->value['status'])===null||$tmp==='' ? '1' : $tmp) <= 1) {?> <?php echo smarty_function_formgroup(array('t'=>'editbtn','url'=>((((RELROUTE).("/")).(MODULE)).("/update2?nid=")).($_smarty_tpl->tpl_vars['vv']->value['nid']),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['update2']),$_smarty_tpl);?>

                                <?php echo smarty_function_formgroup(array('t'=>'delbtn','url'=>((((RELROUTE).("/")).(MODULE)).("/delete2?nid=")).($_smarty_tpl->tpl_vars['vv']->value['nid']),'isbtn'=>$_smarty_tpl->tpl_vars['qx']->value['delete2']),$_smarty_tpl);?>
 <?php }?> </div> </td> </tr> <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?> </tr> <tr>
                    <td colspan="20" style="background-color: #f4f4f4;height: 5px;font-size: 1px;">&nbsp;</td>
                </tr>
                <?php
}
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

            </tbody>
        </table>
    </div>
    <!-- /.box-body -->
    <div class="box-footer clearfix">
        <?php echo smarty_function_formgroup(array('t'=>'page','page'=>$_smarty_tpl->tpl_vars['page']->value),$_smarty_tpl);?>

    </div>
</div>
<!-- /.box -->
<?php echo '<script'; ?>
>
    function doprint() {
        $("#printdiv").printThis({
            debug: false,
            importCSS: true,
            importStyle: true,
            printContainer: true,
            //    loadCSS: "/Content/Themes/Default/style.css", 
            pageTitle: "",
            removeInline: false,
            printDelay: 333,
            header: null,
            formValues: false
        });
    }

    function showPic(pic) {
        //alert(pic);
        showMyModalHtml("显示图片", "<img src='" + pic + "' class='m_img'/>")
    }

    $(document).ready(function (e) {

        $("td.tprice").each(function (index, element) {
            //console.log($(this).attr('nid'));
            var totle = 0.0;
            var nid = $(this).attr('nid');
            $("td.cprice_" + nid).each(function (index, element) {
                totle = totle + parseFloat($(this).html());
            });
            $(this).html(totle);
        });

        $('.datepicker').datetimepicker({
            format: "yyyy-mm-dd", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'month',
            autoclose: true //选择日期后自动关闭
        });

        //tif2img();

    });
<?php echo '</script'; ?>
>
<?php $_smarty_tpl->_subTemplateRender('file:admin/public/cfooter.tpl', $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
