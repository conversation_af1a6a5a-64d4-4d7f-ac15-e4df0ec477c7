{include file='admin/public/cheader.tpl'}
<script src="{$env.public}/jquery/printThis.js"></script>
<!--日期框-->
<link href="{$env.public}/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet">
<script src="{$env.public}/datepicker/bootstrap-datetimepicker.min.js"></script>
<script src="{$env.public}/datepicker/locales/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"></script>
<style>
    th {
        font-size: 16px;
    }

    .tprice {
        font-weight: 600;
    }

</style>
<div class="box">

    <form class="form-horizontal" id="search_form" action="{URI}">
        <div class="box-header">
            <h3 class="box-title">库存管理</h3>
        </div>
        <div class="box-body searchform">
            <!-- /.box-header -->
            <div class="box-body">
                <div class="row">

                    <div class="col-sm-3 scplx-box issearch">
                        {formgroup t='sselect' desc="产品类型" id="scplx" name="scplx"  lst=$c1_arr
                        issearch=1
                        value=$s.scplx|default:''}
                    </div>
                    
                    <div class="col-sm-3">
                        {formgroup t='sselect' desc="产品属性" id="scpsku" name="scpsku" lst=$sku_arr
                        issearch=1
                        value=$s.scpsku|default:''}
                    </div>
                    
                     <div class="col-sm-3">
                        {formgroup t='sselect' desc="记录" id="type" name="type" lst=$env.storetype
                        issearch=1
                        value=$s.type|default:''}
                    </div>
                </div>
                
                
                <div class="row">
                    <div class="col-sm-12">
                        <label style="float:left;" class='control-label'>筛选时间</label>
                        <div class='col-sm-4'>
                            <input class='form-control datepicker' readonly='readonly' type='text' name='stime' id='datetimepicker3' value="{$s.stime|default:''}">
                        </div>
                        <label  class='col-sm-1 control-label'>至</label>
                        <div class='col-sm-4'>
                            <input class='form-control datepicker' readonly='readonly' type='text' name='etime' id='datetimepicker4' value="{$s.etime|default:''}">
                        </div>
                    </div>

                </div>
      
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-info btn-lg " onclick="showModals('warehous')" >操作入库</button>
                {formgroup t='searchbtn' type="lg"}
            </div>
            
            <button type="button" class="btn btn-danger btn-lg " onclick="showModals('warning')" style="float:right;margin-right:20px;">设置警告范围</button>
            
            
        </div>
        <!-- /.box-body -->
</div>
</form>

</div>
<div class="box">
    <!-- /.box-header -->
    <div class="box-body no-padding">
        <table class="table">
            <tbody>
                <tr>
                    <th>序号</th>
                    <th>产品类型</th>
                    <th>产品属性</th>
                    <th>原始库存</th>
                    <th>变化数量</th>
                    <th>现有库存</th>
                    <th>变化时间</th>
                    <th>操作账号</th>
                </tr>
                {foreach $wlsts as $k=>$v}
                <tr style="background:red; color: #fff;}">
                    <td>{$k+1|default:'1'}</td>
                    <td>
                        {$v.cplx_name|default:''}
                    </td>
                    <td>
                        {$v.cplx_name|default:''}
                    </td>
                    <td>
                        {$v.current|default:0}
                    </td>
                     <td>
                        {$v.change|default:0}
                    </td>
                    <td>
                        {$v.balance|default:0}
                    </td>
                    <td>
                        {date("Y-m-d H:i:s", $v['createtime'])|default:''}
                    </td>
                    <td>
                        {$v.creater_name|default:''}
                    </td>
                </tr>
                {/foreach}
                
                
                {foreach $lst as $k=>$v}
                <tr>
                    <td>{$k+1|default:'1'}</td>
                    <td>
                        {$v.cplx_name|default:''}
                    </td>
                    <td>
                        {$v.cpsku_name|default:''}
                    </td>
                    <td>
                        {$v.current|default:0}
                    </td>
                     <td>
                        {$v.change|default:0}
                    </td>
                    <td>
                        {$v.balance|default:0}
                    </td>
                    <td>
                        {date("Y-m-d H:i:s", $v['createtime'])|default:''}
                    </td>
                    <td>
                        {$v.creater_name|default:''}
                    </td>
                </tr>
                {/foreach}

            </tbody>
        </table>
    </div>
    <!-- /.box-body -->
    <div class="box-footer clearfix">
        {formgroup t='page' page=$page}
    </div>
</div>
<!-- /.box -->
<script>
    var c1_json = '{$c1_json|default:array()}';
    var c1json = $.parseJSON(c1_json);
    

    var attribute_sjson = '{$attribute_json|default:array()}';
    var attributejson = $.parseJSON(attribute_sjson);
    var cpattribute = '{$data.cpattribute|default:0}';
    
    
    $(document).on('click', '[data-toggle="modal"]', function () {
        var imgSrc = $(this).data('img-src');
        $('#imageModalSrc').attr('src', imgSrc);
    });
    
   
   
    


    function doprint() {
        $("#printdiv").printThis({
            debug: false,
            importCSS: true,
            importStyle: true,
            printContainer: true,
            // size: {
            //     w: '124mm',
            //     h: '190mm'
            // },
            // 'margin-top': '0mm',
            // 'margin-bottom': '0mm',
            // 'margin-left': '0mm',
            // 'margin-right': '0mm',
            // mediaPrint: [{
            //     selector: 'body',
            //     properties: {
            //     'width': '100%',
            //     'height': '100%',
            //     'margin': '0',
            //     'padding': '0'
            //     }
            // }],
            //    loadCSS: "/Content/Themes/Default/style.css", 
            pageTitle: "",
            removeInline: false,
            printDelay: 333,
            header: null,
            formValues: false,
            afterPrint: function() {
                console.log('打印成功！');
            }
        });
    }

    function doprintB() {
        $("#printdivB").printThis({
            debug: false,
            importCSS: true,
            importStyle: true,
            printContainer: true,
            // size: {
            //     w: '124mm',
            //     h: '190mm'
            // },
            // 'margin-top': '0mm',
            // 'margin-bottom': '0mm',
            // 'margin-left': '0mm',
            // 'margin-right': '0mm',
            // mediaPrint: [{
            //     selector: 'body',
            //     properties: {
            //     'width': '100%',
            //     'height': '100%',
            //     'margin': '0',
            //     'padding': '0'
            //     }
            // }],
            //    loadCSS: "/Content/Themes/Default/style.css", 
            pageTitle: "",
            removeInline: false,
            printDelay: 333,
            header: null,
            formValues: false,
            afterPrint: function() {
                console.log('打印成功！');
            }
        });
    }

    function showPic(pic) {
        //alert(pic);
        showMyModalHtml("显示图片", "<img src='" + pic + "' class='m_img'/>")
    }

    $(document).ready(function (e) {

        $('.datepicker').datetimepicker({
            format: "yyyy-mm-dd", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'month',
            autoclose: true //选择日期后自动关闭
        });

    });

    var c2_sjson = '{$c2_json|default:array()}';
    var c2json = $.parseJSON(c2_sjson);
    $("#scpkgdiv").hide();
  
    var scpkg = '{$s.scpkg|default:0}';
    //console.log(c2json);

    if('{$s.scplx == 106}'){
        $("#scpkgdiv").show();
    }
    
    $(document).ready(function (e) {
        setCPGG($('#scplx').val());
        $('#scplx').on('change', function () {
            if($('#scplx').val() == 106){
                setCPGG($('#scplx').val());
                $("#scpkgdiv").show();
            }else {
                $("#scpkg").empty();
                $("#scpkgdiv").hide();
            }
            
            
        });
    });
    

    function setCPGG(id) {
        $("#scpkg").empty();
        $("#scpkg").append('<option value="-99" sleected="selected">全部</option>');
        $.each(c2json[id], function (key, value) {
            // console.log(c2json[id]);
            console.log('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
            var sleected = '';
            if (key == scpkg) {
                sleected = ' selected="selected" '
            }
            $("#scpkg").append('<option value="' + key + '" ' + sleected + '>' + value + '</option>');
        });
    }
    function showModals(obj){
        $('#'+obj).modal('show');
        if(obj == 'warehous'){
            $('#scplx1').on('change', function () {
                changelx($(this),obj);
            });
            $('#scpsku1').on('change', function () {
                 getstore();
            });
        }else{
            $('#scplx2').on('change', function () {
                 changelx($(this),obj);
            });
             
            $('#scpsku2').on('change', function () {
                 getwarning();
            });
        }
        
        resets(obj);
    }
    
    function hideModals(obj){
        resets(obj);
        $('#'+obj).modal('hide');
    }
    
    function resets(obj){
        console.log(c1json);
       var html = '<option value="-99">请选择产品</option>';
        $.each(c1json, function (key, value) {
            console.log(key + ": " + value);
            html +='<option value="' + key + '">' + value + '</option>';
        });
        if(obj == 'warehous'){
            $('#scplx1').empty();
            $('#scplx1').append(html);
            $('#scpsku1').empty();
            $('#current1').val('');
            $('#changenum1').val('');
        }else{
            $('#scplx2').empty();
            $('#scpsku2').empty();
            $('#scplx2').append(html);
            $('#current2').val('');
            $('#changenum2').val('');
        }
    }
    
    
    $(document).ready(function (e) {

        $('.datetimepicker').datetimepicker({
            format: "yyyy-mm-dd hh:ii", //选择日期后，文本框显示的日期格式
            language: 'zh-CN', //汉化
            startView: 'month',
            minView: 'day',
            autoclose: true //选择日期后自动关闭
        });
        
        
        

    });
    
    $('#scplx').on('change', function () {
         changelx($(this));
    });
   
    function changelx(obj,objname = ''){
         console.log('change');
         console.log(obj.find('option:selected').val());
         console.log(obj.find('option:selected').parent().parent().parent().hasClass('scplx-box'));
         
         
         id = obj.find('option:selected').val();
         obj.find('option:selected').parents('.row').find("[name='scpsku']").empty();
         if(obj.find('option:selected').parent().parent().parent().hasClass('issearch')){
            var html = '<option value="-99">不限</option>';
         }else{
            var html = '';
         }
         $.each(attributejson[id], function (key, value) {
            console.log(key + ": " + value);
            html +='<option value="' + key + '">' + value + '</option>';
         });
         obj.find('option:selected').parents('.row').find("[name='scpsku']").append(html);
         
         if(objname == 'warehous'){
            getstore();
         }
         
         if(objname == 'warning'){
            getwarning();
         }
         

    }

    
    function getstore(){
        var scplx = $('#scplx1').val();
        var scpsku = $('#scpsku1').val();
        $.ajax({
            url: '/index.php/htgl/sorder/getstore',
            type: 'GET',
            data:{
                'scplx': scplx,
                'scpsku':scpsku,
            },
            contentType: "application/json",
            async: true,
            dataType: 'json',
            success: function(data){
                if(data.status == 1){
                    $('#current1').val(data.value);
                }
            },
        });
    }
    
    function getwarning(){
        var scplx = $('#scplx2').val();
        var scpsku = $('#scpsku2').val();
        $.ajax({
            url: '/index.php/htgl/sorder/getwarning',
            type: 'GET',
            data:{
                'scplx': scplx,
                'scpsku':scpsku,
            },
            contentType: "application/json",
            async: true,
            dataType: 'json',
            success: function(data){
                if(data.status == 1){
                    $('#current2').val(data.value);
                }
            },
        });
    }
    
    function entry(){
        var scplx = $('#scplx1').val();
        var scpsku = $('#scpsku1').val();
        var changenum =  $('#changenum1').val();
        if(!scplx){
            alert('请选择产品');
            return false;
        }
        
        if(!scpsku){
            alert('请选择属性');
            return false;
        }
        
        if(!changenum){
            alert('请输入入库数量');
            return false;
        }
        
        $.ajax({
            url: '/index.php/htgl/sorder/entry',
            type: 'GET',
            data:{
                'scplx': scplx,
                'scpsku':scpsku,
                'changenum':changenum,
            },
            contentType: "application/json",
            async: true,
            dataType: 'json',
            success: function(data){
                if(data == 1){
                    alert('入库成功');
                    $('#warehous').modal('hide');
                    location.reload();
                }
            },
        });
    }
    
   function setwarning(){
        var scplx = $('#scplx2').val();
        var scpsku = $('#scpsku2').val();
        var changenum =  $('#changenum2').val();
        if(!scplx){
            alert('请选择产品');
            return false;
        }
        
        if(!scpsku){
            alert('请选择属性');
            return false;
        }
        
        if(!changenum){
            alert('请输入入库数量');
            return false;
        }
        
        $.ajax({
            url: '/index.php/htgl/sorder/setwarning',
            type: 'GET',
            data:{
                'scplx': scplx,
                'scpsku':scpsku,
                'changenum':changenum,
            },
            contentType: "application/json",
            async: true,
            dataType: 'json',
            success: function(data){
                if(data == 1){
                    alert('修改成功');
                    $('#warning').modal('hide');
                    location.reload();
                }
            },
        });
    }


</script>
<style>
    .modal .title{
        padding: 10px 16px;
        font-size: 18px;
        line-height: 1.3333333;
        background-color: #ea8477;
        border-color: #ea8477;
        display: inline-block;
    }
    .modal .row{
        margin-bottom:10px;
    }

</style>
<!-- /.modal -->
<div class="modal" id="warehous">
    <div class="modal-dialog">
        <div class="modal-content">
            <span class='title'>操作入库</span>
            <div style="padding:15px;background-color:#fff;overflow: hidden;">
                
                <div class="row">

                    <div class="col-sm-6 scplx-box">
                        {formgroup t='sselect' desc="选择产品" id="scplx1"  name="scplx"   lst=$c1_arr
                        value=$s.scplx|default:''}
                    </div>
                    
                    <div class="col-sm-6">
                        {formgroup t='sselect' desc="选择属性" id="scpsku1" name="scpsku" lst=array() }
                    </div>
   
                </div>
                
                
                <div class="row">
                
                    <div class="col-sm-6">
                        <div class="input-group">
                            <span class="input-group-addon bg-aqua color-palette">现有库存</span>
                            <input class="form-control" type="text" id="current1" name="current"  disabled value="4548455">
                        </div>
                    </div>
                    
                    <div class="col-sm-6">
                        <div class="input-group">
                            <span class="input-group-addon bg-aqua color-palette">入库数量</span>
                            <input class="form-control" type="text" id="changenum1" name="changenum"  value="">
                        </div>
                    </div>
   
                </div>
                
                
                <div style="float:right;margin-right:30px;">
                    <button type="button" class="btn btn-sm btn-info"  onclick="entry()">确认</button>
                    <button type="button" class="btn btn-sm btn-warning"  onclick="hideModals('warehous')">取消</button>
                </div>
            </div>
           
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>
<div class="modal" id="warning">
    <div class="modal-dialog">
        <div class="modal-content">
            <span class='title'>设置警告范围</span>
            <div style="padding:15px;background-color:#fff;overflow: hidden;">
                
                <div class="row">

                    <div class="col-sm-6 scplx-box">
                        {formgroup t='sselect' desc="选择产品" id="scplx2"  name="scplx" lst=$c1_arr}
                    </div>
                    
                    <div class="col-sm-6">
                        {formgroup t='sselect' desc="选择属性"  id="scpsku2" name="scpsku" lst=array()}
                    </div>
   
                </div>
                
                
                <div class="row">
                
                    <div class="col-sm-6">
                        <div class="input-group">
                            <span class="input-group-addon bg-aqua color-palette">目前范围</span>
                            <input class="form-control" type="text"  name="current" id="current2" disabled value="4548455">
                        </div>
                    </div>
                    
                    <div class="col-sm-6">
                        <div class="input-group">
                            <span class="input-group-addon bg-aqua color-palette">数量低于</span>
                            <input class="form-control" type="text" name="changenum" id="changenum2" value="">
                            <span class="input-group-addon bg-aqua color-palette">警告</span>
                        </div>
                    </div>
   
                </div>
                
                
                <div style="float:right;margin-right:30px;">
                    <button type="button" class="btn btn-sm btn-info"  onclick="setwarning()">确认</button>
                    <button type="button" class="btn btn-sm btn-warning"  onclick="hideModals('warning')">取消</button>
                </div>
            </div>
           
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>
<!-- /.modal -->
{include file='admin/public/cfooter.tpl'}